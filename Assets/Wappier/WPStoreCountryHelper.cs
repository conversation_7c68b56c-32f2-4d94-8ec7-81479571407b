using System.Collections;
using System.Collections.Generic;
using UnityEngine;

/**
 * Wappier Helper
 */
namespace WappierHelper
{
    /**
     * Delegate to receive callback with the Stoure Country Code
     */
    public delegate void WPCountryCodeDelegate(string countryCode);

    public partial class WPStoreCountryHelper : MonoBehaviour
    {

        private static WPStoreCountryHelper sInstance;

        /**
         * Singleton Instance to access Helper
         */
        public static WPStoreCountryHelper Instance
        {
            get
            {
                if (sInstance == null)
                {
                    sInstance = (new GameObject("WPStoreCountryHelper")).AddComponent<WPStoreCountryHelper>();
                    DontDestroyOnLoad(sInstance);
                }
                return sInstance;
            }
        }

        /**
         * Retrieves the Store Country Code from the IAP Platform
         * Requires a valid SKU to be used for communication and retrieval
         * Will callback with WP_ERROR_ prefixed error codes. e.g.
         * WP_ERROR_PRODUCT_UNAVAILABLE : the provideed SKU was unavailable
         * WP_ERROR_NIL_PRODUCT : the provided SKU was null
         * WP_ERROR_REQUEST_[store error description] : The Product Request failed
         * @param SKU a valid SKU available on the target platform
         * @param countryCodeDelegate the delegate to receive a callback with the country code.
         */
        public void retrieveCountryCode(string SKU, WPCountryCodeDelegate countryCodeDelegate)
        {
            _retrieveCountryCode(SKU, countryCodeDelegate);
        }
    }
}
