using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class SellingStrapButtonVFXController : MonoBehaviour {
  public const string VFX_SHOWED_KEY = "SellingStrapBtnVFX";
  public const string VFX_FRAME_VFX_PATH = "R68/SellButton_FrameVFX";
  public const string VFX_GLOW_VFX_PATH = "R68/SellButton_GlowVFX";
  
  [SerializeField] private Transform frameSellVfxRoot;
  [SerializeField] private Transform glowSellVfxRoot;

  private bool _showedVfx;

  public bool ShowedVfx {
    get => _showedVfx;
  }

  private GameObject frameSellVfx;
  private GameObject glowSellVfx;
  
  public void Populate(bool isUpDirection = false) {
    if (!ShouldShowVFX()) {
      return;
    }
    
    if (!_showedVfx) {
      _showedVfx = true;
      if (frameSellVfxRoot != null) {
        frameSellVfx = VFXManager.Instance.PlayInCanvas(VFX_FRAME_VFX_PATH, frameSellVfxRoot, -1f, frameSellVfxRoot.position);
        
        var toolTipConfig = new UIToolTipController.Config {
          tailDirection = isUpDirection ? UIToolTipController.TailDirection.Up : UIToolTipController.TailDirection.Down
        };
        UIToolTipController.Instance.Show(frameSellVfxRoot.position, toolTipConfig, KFFLocalization.Get("!!R68_SELL_STRAP_BTN_TOOLTIP"));
      }
      
      if (glowSellVfxRoot != null) {
        glowSellVfx = VFXManager.Instance.PlayInCanvas(VFX_GLOW_VFX_PATH, glowSellVfxRoot, -1f, glowSellVfxRoot.position);
      }
    }
  }

  public void SaveSellingButtonVFXState() {
    if (_showedVfx) {
      PlayerPrefs.SetInt(VFX_SHOWED_KEY, 1);
      TFUtils.SavePlayerPrefs();
      if (frameSellVfx != null) {
        frameSellVfx.SetActive(false);
      }
      if (glowSellVfx != null) {
        glowSellVfx.SetActive(false);
      }
    }
  }

  bool ShouldShowVFX() {
    bool show = PlayerPrefs.GetInt(VFX_SHOWED_KEY, 0) == 0;
    return show;
  }
}
