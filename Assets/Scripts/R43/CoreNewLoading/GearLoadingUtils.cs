using System.Collections;
using System.Collections.Generic;
using System.IO;
using System;
using System.Runtime.InteropServices;
using UnityEngine;
using System.Text;
using FlexBuffers;
using Prime31;
using Playgami.Core.NativeInfo;
using Scopely.Core;
using WWEProcessTracking;

public static class GearLoadingUtils {
  public static readonly string GC_LOGIN_URL = "account/gcAuth/";
  public static readonly string PRE_AUTH_URL = "account/preAuth/";
  public static readonly string CONNECT_AUTH_URL = "account/connectAuth/";
  public static readonly string REMAP_AUTH_URL = "account/remapAuth/";
  public static readonly string DISCONNECT_AUTH_URL = "account/disconnectAuth";
  public static readonly string CHECK_AUTH_URL = "account/checkAuth/";
  public static readonly string UPDATE_CHAT_URL = "account/update_chat/";
  public static readonly string GET_SERVER_TIME = "time/";
  public static readonly string GET_SERVER_SETTINGS = "ic_multiplayer/settings/";
  public static readonly string POST_FEATURE_GATE_URL = "ic_multiplayer/gates/";
  public static readonly string GC_AUTH_WITH_TRUST_DEVICE_URL = "account/auth_with_trusted_device/";
  public static readonly string CLONE_AUTH_URL = "account/cloneAuth/";
  public static readonly string GET_ALL_USER_DATA_URL = "wp_userdata/get_user_data_r43/";
  public static readonly string SET_DEVICE_TOKEN_URL = "account/set_device_token/";
  public static readonly string REQUEST_DELETE_APPLE_ACCOUNT = "account/request_delete_apple_account/";
  public static readonly string LEGAL_TEXT_URL = "account/get_legal_text/";

  #if UNITY_WEBGL
  private const string GC_GET_LOGIN_DATA_URL = "account/get_login_data/";
  #endif
  
  public const string BLUEPRINT_LAST_VARIANTS = "BLUEPRINT_LAST_VARIANTS";
  public const string BLUEPRINT_VARIANTS_LOOKUP = "BLUEPRINT_VARIANTS_LOOKUP";
  public static Dictionary<string, string> BlueprintsVariantLookup = new Dictionary<string, string>();
  private const string ETagFile = "eTagFile";

  #if UNITY_WEBGL
  [DllImport("__Internal")]
  private static extern string GetRrequest(string Url = "");
  #endif
  
  public static string ODL_VERSION_URL { get; private set; } = "";
  public static string ODL_BRANCH_NAME { get; private set; } = "";
  public static string ODB_VERSION_URL { get; private set; } = "";
  public static string VERSION_URL { get; private set; } = "";
  public static string SERVER_URL { get; private set; } = "";
  public static string PERSISTENT_ASSETS_PATH { get; private set; } = "";
  public static string BundleIdentifier { get; private set; } = "";
  public static string LOBBY_URL { get; private set; } = "";
  #if UNITY_WEBGL
  public static string XsollaProjectId { get; private set; }
  #endif
  public static string FEUD_LOBBY_URL { get; private set; } = "";
  public static string FF_SERVER_URL { get; private set; } = "";
  public static string CHAT_SERVER_URL { get; private set; } = "";
  
  private static string _clientNativeVersion;
  private static string _clientBuildVersion;
  private static Version _clientVersion = null;
  private static string _clientVersionString = string.Empty;
  private static readonly byte[] First = Encoding.ASCII.GetBytes("{ \"items\": ");
  private static readonly byte[] Last = Encoding.ASCII.GetBytes("}");
  
  private static bool _isInitialized;
  
  public static void InitServerSettings() {
    if (_isInitialized) {
      return;
    }
    
#if UNITY_EDITOR
    BundleIdentifier = UnityEditor.PlayerSettings.applicationIdentifier;
#elif UNITY_WEBGL
    BundleIdentifier = string.Empty;
#else
    BundleIdentifier = Application.identifier; //we shouldn't need native plugin to get this information.
#endif
    var filePath = TFUtils.GetStreamingAssetsFile("server_settings.json");
    var json = filePath.Contains("://") ? GetJsonPath(filePath) : File.ReadAllText(filePath);

    var data = (Dictionary<string, object>) MiniJSON.Json.Deserialize(json);

    SERVER_URL = TFUtils.LoadString(data, "server_url", "");
    LOBBY_URL = TFUtils.LoadString(data, "lobby_url", "");
    FF_SERVER_URL = TFUtils.LoadString(data, "ff_server_url", "");
    FEUD_LOBBY_URL = TFUtils.LoadString(data, "ff_lobby_url", "");
    CHAT_SERVER_URL = TFUtils.LoadString(data, "chat_server_url", "");
    ODL_VERSION_URL = TFUtils.LoadString(data, "odl_version_url", "") + $"?_version={clientNativeVersion}";
    ODL_BRANCH_NAME = TFUtils.LoadString(data, "odl_branch_name", "");
    ODB_VERSION_URL = TFUtils.LoadString(data, "odb_version_url", "");
    VERSION_URL = TFUtils.LoadString(data, "version_file_url", "");
    #if UNITY_WEBGL
    XsollaProjectId = TFUtils.LoadString(data, "xsolla_project_id", "");
    #endif
    PERSISTENT_ASSETS_PATH = TFUtils.GetPersistentAssetsPath();
    _isInitialized = true;
  }

  public static void DebugOverrideServerUrl(string inServerUrl) {
    if (VERSION_URL.StartsWith(SERVER_URL))
      VERSION_URL = VERSION_URL.Replace(SERVER_URL, inServerUrl);

    SERVER_URL = inServerUrl;
  }

  public static string GetAdId() {
    string adId = null;
    // Add AdID information for user restore if applicable
#if !UNITY_EDITOR
#if UNITY_IOS
    adId = NativeInfo.GetAdId();
#elif UNITY_ANDROID
    adId = CorePackage.CoreApi.GooglePlayAdId;
    if (adId == "none") // ScopelyPlatform.CheckForGooglAdInfo - GAID retrieval failed
      adId = null;
#endif
#endif

    return adId;
  }

  public static bool HasNewVersion(Version serverEditorVersion, Version serverIOSVersion, Version serverAndroidVersion) {
    bool result = false;
#if UNITY_EDITOR
    result = serverEditorVersion.CompareTo(GetClientVersion()) > 0;
#elif UNITY_IOS
      result = serverIOSVersion.CompareTo(GetClientVersion()) > 0;
#elif UNITY_ANDROID
      result = serverAndroidVersion.CompareTo(GetClientVersion()) > 0;
#else
      result = false;
#endif

    return result;
  }

  public static string ReadETag() {
    lock(ETagFile) {
      if(File.Exists(GetEtagFilePath(ETagFile)))
        return File.ReadAllText(GetEtagFilePath(ETagFile));
      return null;
    }
  }

  public static void SaveETag(string newETag) {
    //WHIP-31803
    if (KFFAssetBundleManager.Instance.IsDiskSpaceFull()) {
      return;
    }
    lock(ETagFile) {
      try {
        File.WriteAllText(GetEtagFilePath(ETagFile), newETag);
      } catch(Exception ex) {
        Debug.LogError(ex.Message);
      }
    }
  }

  public static void DeleteETagFile() {
    File.Delete(ETagFile);
  }

  public static bool IsGearLocalBlueprint() {
    #if GEAR_LOCAL_BLUEPRINT && !KFF_RELEASE
      return true;
    #endif

    return false;
  }

  public static string GetGameDataURL(string playerId) {
    return string.Format("{0}/{1}/{2}/", SERVER_URL, "ic_multiplayer/multiplayer_game", playerId);
  }

  public static string GetODLS3URL() {
    return ODL_VERSION_URL;
  }

  public static string GetCheckGameVersionURL() {
    return VERSION_URL;
  }

  public static string GetServerSettingsURL() {
    return SERVER_URL + GET_SERVER_SETTINGS;
  }

  public static string GetPreAuthURL() {
    return SERVER_URL + PRE_AUTH_URL;
  }

  public static string GetSetDeviceTokenURL() {
    return SERVER_URL + SET_DEVICE_TOKEN_URL;
  }

  public static string GetLoginURL() {
    return SERVER_URL + GC_LOGIN_URL;
  }

  #if UNITY_WEBGL
  public static string GetLoginDataURL() {
    return SERVER_URL + GC_GET_LOGIN_DATA_URL;
  }
  #endif

  public static string GetSetTrustDeviceURL() {
    return SERVER_URL + GC_AUTH_WITH_TRUST_DEVICE_URL;
  }
  public static string GetRequestDeleteAppleAccountURL() {
    return SERVER_URL + REQUEST_DELETE_APPLE_ACCOUNT;
  }
  
  public static string GetCloneAuthURL() {
    return SERVER_URL + CLONE_AUTH_URL;
  }

  public static string GetUserDataURL() {
    return SERVER_URL + GET_ALL_USER_DATA_URL;
  }

  public static string GetCheckAuthURL() {
    return SERVER_URL + CHECK_AUTH_URL;
  }

  public static string GetEtagFilePath(string fileName) {
    string path = GetPersistentAssetPath() + Path.DirectorySeparatorChar;
    CreateFolder(path);
    return path + fileName;
  }

  public static string GetLocalizationURL() {
    return SERVER_URL + "ic_blueprint/get_blueprints_zip_url/";
  }

  public static string GetBluePrintsURL() {
    return SERVER_URL + "ic_blueprint/get_blueprints_zip_url/";
  }

  public static bool IsLocalBluePrintExist() {
    return File.Exists(GetPersistentAssetPath() + Path.DirectorySeparatorChar + "blueprints.zip");
  }
  
  public static string GetLegalTextURL() {
    return SERVER_URL + LEGAL_TEXT_URL;
  }

  public static bool IsLocalBluePrintExist(string variantId) {
    return File.Exists(GetPersistentAssetPath() + Path.DirectorySeparatorChar + BlueprintAssetName(variantId));
  }

  public static string GetBlueprintChangelist() {
    string changelist = "";
    string blueprintFilePath = GetPersistentAssetPath() + Path.DirectorySeparatorChar + "blueprints.zip";
    byte[] Data = new byte[0];
    // extract the json in to the given buffer
    #if UNITY_WEBGL
    int result = SharZipLibUtil.CoppyToBuffer(blueprintFilePath, "blueprints/changelist.txt", ref Data);
    #else
    int result = lzip.entry2Buffer(blueprintFilePath, "blueprints/changelist.txt", ref Data);
    #endif
    changelist = Encoding.ASCII.GetString(Data);

    return changelist;
  }

  public static string GetLocalBluePrintPath() {
    return GetPersistentAssetPath() + Path.DirectorySeparatorChar + "blueprints" + Path.DirectorySeparatorChar;
  }

  public static bool IsLocalLocalizationExist() {
    return File.Exists(GetPersistentAssetPath() + Path.DirectorySeparatorChar + "localization.zip");
  }

  public static string GetLocalLocalizationPath() {
    return GetPersistentAssetPath() + Path.DirectorySeparatorChar + "localization" + Path.DirectorySeparatorChar;
  }

  public static string BlueprintAssetName(string variantId = null) {
    return variantId == null ? "blueprints.zip" : string.Format("blueprints@{0}.zip", variantId);
  }

  public static string BlueprintAssetEtag(string variantId = null) {
    return variantId == null ? "blueprints.etag" : string.Format("blueprints@{0}.etag", variantId);
  }

  private static string LocalizationCachePath() {
    return PERSISTENT_ASSETS_PATH + Path.DirectorySeparatorChar + "localization.zip";
  }

  private static string BlueprintsCachePath(string variantId = null) {
    return PERSISTENT_ASSETS_PATH + Path.DirectorySeparatorChar + BlueprintAssetName(variantId);
  }

  public static string GetLocalBluePrintFilePath(string variantId = null) {
    return GetPersistentAssetPath() + Path.DirectorySeparatorChar + BlueprintAssetName(variantId);
  }

  public static string GetLocalLocalizationFilePath() {
    return GetPersistentAssetPath() + Path.DirectorySeparatorChar + "localization.zip";
  }

  public static string GetLocalBluePrintEtagFilePath(string variantId = null) {
    return GetPersistentAssetPath() + Path.DirectorySeparatorChar + BlueprintAssetEtag(variantId);
  }

  public static string GetLocalLocalizationEtagFilePath() {
    return GetPersistentAssetPath() + Path.DirectorySeparatorChar + "localization.etag";
  }

  public static string GetLocalGamePath() {
    string path = GetPersistentAssetPath() + Path.DirectorySeparatorChar;
    CreateFolder(path);
    return path + Path.DirectorySeparatorChar;
  }

  public static void CreateFolder(string path) {
    if(!Directory.Exists(path)) {
      Directory.CreateDirectory(path);
    }
  }

#if !KFF_RELEASE && !DISABLE_LOG  
  public static void LogError(string message) {
    TFUtils.MyLogError(new object[] {
      "======Error======",
      message,
      Time.realtimeSinceStartup
    }, go: null, color: "red");
  }

  public static void LogException(Exception ex) {
    Debug.LogError("<color=red>======Exception======</color> \n" + (ex.Message ?? "") + " " + (ex.StackTrace ?? ""));
  }
# else
  [System.Diagnostics.Conditional("DEBUG_LEVEL_LOG")]  
  public static void LogException(Exception ex) {}
  
  [System.Diagnostics.Conditional("DEBUG_LEVEL_LOG")]  
  public static void LogError(string message) {}
#endif

  public static string GetPersistentAssetPath() {
    return PERSISTENT_ASSETS_PATH;
  }

  public static string GetStreamingAssetPath() {
    string path = Application.streamingAssetsPath + "/";
    if(Application.platform == RuntimePlatform.Android) {
      path = "jar:file://" + Application.dataPath + "!/assets/";
    } else if(Application.platform == RuntimePlatform.IPhonePlayer) {
      path = Application.dataPath + "/Raw/";
    }

    return path;
  }

  static public Version GetClientVersion() {
    if(_clientVersion == null) {
      string versionStr = "";
#if UNITY_EDITOR
      versionStr = UnityEditor.PlayerSettings.bundleVersion;
#elif UNITY_IOS
      versionStr = KFFCSUtils.GetShortVersionString();
#elif UNITY_ANDROID
      versionStr = KFFAndroidPlugin.GetManifestVersionName();
#else
      versionStr = "1.0";
#endif

      _clientVersion = new Version(versionStr);
    }

    return _clientVersion;
  }

  static public string GetClientVersionString() {
    if(string.IsNullOrEmpty(_clientVersionString)) {
#if UNITY_EDITOR
      _clientVersionString = UnityEditor.PlayerSettings.bundleVersion;
#elif UNITY_IOS
      _clientVersionString = KFFCSUtils.GetShortVersionString();
#elif UNITY_ANDROID
      _clientVersionString = KFFAndroidPlugin.GetManifestVersionName();
#else
      _clientVersionString = "1.0";
#endif
    }

    return _clientVersionString;
  }

  public static string clientNativeVersion {
    get {
      if(_clientNativeVersion == null) {
        _clientNativeVersion = NativeInfo.GetAppVersion(false);
      }

      return _clientNativeVersion;
    }
  }

  public static string clientBuildVersion {
    get {
      if(_clientBuildVersion == null) {
        _clientBuildVersion = GetClientBuildNumber();
      }

      return _clientBuildVersion;
    }
  }
  
  private static string _clientSessionUniqueID;
  public static string ClientSessionUniqueID {
    get {
      if(_clientSessionUniqueID == null) {
        _clientSessionUniqueID = Guid.NewGuid().ToString();
      }

      return _clientSessionUniqueID;
    }
  }

  public static string GetClientBuildNumber() {
    string json = TFUtils.GetAssetBundleSettingFileContents();
    Dictionary<string, object> data = (Dictionary<string, object>)MiniJSON.Json.Deserialize(json);

    // The URL we use for pulling versioned asset bundles actually contains the build number
    // It looks like this: "http://asset-bundles.whiplashapi.com/372256/"
    // We can perform some string manipulation to retrieve just the build number part
    string sAssetBundleURL = (string)data["asset_bundle_url"];

    // Ensure the string is in the format above
    if(sAssetBundleURL.StartsWith("http://") && sAssetBundleURL.EndsWith("/")) {
      // Remove trailing and leading '/' to make it easier
      sAssetBundleURL = sAssetBundleURL.Substring(0, sAssetBundleURL.Length - 1);
      string sBuildNumber = sAssetBundleURL.Substring(sAssetBundleURL.LastIndexOf('/'));
      sBuildNumber = sBuildNumber.TrimStart('/');
      return sBuildNumber;
    }
    return string.Empty;
  }

  public static void WriteData(string filePath, byte[] data) {
    File.WriteAllBytes(filePath, data);
  }

  public static byte[] ReadData(string filename) {
    return File.ReadAllBytes(filename);
  }

  public static T[] GetJsonAssetFromZipNewJson<T>(string FilePath, bool useLocal = false) {
#if !KFF_RELEASE
    if (useLocal) {
      return GetJsonAssetFromZipNewJsonLocal<T>(FilePath);
    }
#endif
    T[] dict = null;
    string[] directories = FilePath.Split(Path.DirectorySeparatorChar);
    if(directories.Length == 2 && directories[0] == "Blueprints") {
      string json_key = "blueprints/" + directories[1];
      string filePath;
      if(json_key == "blueprints/db_Localization.json") {
        filePath = LocalizationCachePath();
      } else {
        BlueprintsVariantLookup.TryGetValue(json_key, out string variantId);
        filePath = BlueprintsCachePath(variantId);
      }
      byte[] Data = new byte[0];
      // extract the json in to the given buffer
      // lzip doesn't support WebGL so we use Sharp.Zip
      #if UNITY_WEBGL
      int result = SharZipLibUtil.CoppyToBuffer(filePath, json_key, ref Data);
      #else
      int result = lzip.entry2Buffer(filePath, json_key, ref Data);
      #endif
      
      if (result != 1) {
        #if !UNITY_WEBGL
        GearDataLoadingManager.Instance.Fac.StartNew(() => {
          TFUtils.LogWarning("WARNING!!!!GetJsonAssetFromZipNewJson - JSON Zipfile could not extract " + json_key);
        });
        throw new Exception("GetJsonAssetFromZipNewJson - JSON Zipfile could not extract " + FilePath);
        #else
        TFUtils.LogWarning("WARNING!!!! JSON Zipfile could not extract " + json_key);
        return null;
        #endif
      }
      byte[] merge = new byte[First.Length + Data.Length + Last.Length];
      Buffer.BlockCopy(First, 0, merge, 0, First.Length);
      Buffer.BlockCopy(Data, 0, merge, First.Length, Data.Length);
      Buffer.BlockCopy(Last, 0, merge, First.Length + Data.Length, Last.Length);

      string strData = Encoding.ASCII.GetString(merge);
      // Free
      Data = null;
      merge = null;

      dict = JsonHelper.FromJson<T>(strData);
    }


    return dict;
  }
  
  /// <summary>
  /// JsonHelper.FromJson can not parse the nested list ex: List<List<string>>
  /// Use JsonHelper.FromJsonNewtonsoft instead
  /// </summary>
  /// <param name="FilePath"></param>
  /// <param name="useLocal"></param>
  /// <typeparam name="T"></typeparam>
  /// <returns></returns>
  /// <exception cref="Exception"></exception>
  public static T[] GetJsonAssetFromZipNewJsonNewtonsoft<T>(string FilePath, bool useLocal = false) {
#if !KFF_RELEASE
    if (useLocal) {
      string Data = TFUtils.GetJsonFileContent(FilePath);
      Data = Encoding.ASCII.GetString(First) + Data + Encoding.ASCII.GetString(Last);
      T[] dictLocal = JsonHelper.FromJsonNewtonsoft<T>(Data);
      return dictLocal;
    }
#endif
    T[] dict = null;
    string[] directories = FilePath.Split(Path.DirectorySeparatorChar);
    if(directories.Length == 2 && directories[0] == "Blueprints") {
      string json_key = "blueprints/" + directories[1];
      string filePath;
      if(json_key == "blueprints/db_Localization.json") {
        filePath = LocalizationCachePath();
      } else {
        BlueprintsVariantLookup.TryGetValue(json_key, out string variantId);
        filePath = BlueprintsCachePath(variantId);
      }
      byte[] Data = new byte[0];
      // extract the json in to the given buffer
      // lzip doesn't support WebGL so we use Sharp.Zip
#if UNITY_WEBGL
      int result = SharZipLibUtil.CoppyToBuffer(filePath, json_key, ref Data);
#else
      int result = lzip.entry2Buffer(filePath, json_key, ref Data);
#endif
      
      if (result != 1) {
#if !UNITY_WEBGL
        GearDataLoadingManager.Instance.Fac.StartNew(() => {
          TFUtils.LogWarning("WARNING!!!!GetJsonAssetFromZipNewJson - JSON Zipfile could not extract " + json_key);
        });
        throw new Exception("GetJsonAssetFromZipNewJson - JSON Zipfile could not extract " + FilePath);
#else
        TFUtils.LogWarning("WARNING!!!! JSON Zipfile could not extract " + json_key);
        return null;
#endif
      }
      byte[] merge = new byte[First.Length + Data.Length + Last.Length];
      Buffer.BlockCopy(First, 0, merge, 0, First.Length);
      Buffer.BlockCopy(Data, 0, merge, First.Length, Data.Length);
      Buffer.BlockCopy(Last, 0, merge, First.Length + Data.Length, Last.Length);

      string strData = Encoding.ASCII.GetString(merge);
      // Free
      Data = null;
      merge = null;
      dict = JsonHelper.FromJsonNewtonsoft<T>(strData);
    }


    return dict;
  }

  public static T GetJsonAssetFromZip<T>(string FilePath, bool useLocal = false, bool throwException = true) {
    object dict = null;
    #if !KFF_RELEASE
    if (useLocal) {
      string strData = TFUtils.GetJsonFileContent(FilePath);
      dict = MiniJSON.Json.Deserialize(strData);
      return (T)dict;
    }
    #endif
    string[] directories = FilePath.Split(Path.DirectorySeparatorChar);
    if(directories.Length == 2 && directories[0] == "Blueprints") {
      string json_key = "blueprints/" + directories[1];
      string filePath;
      if(json_key == "blueprints/db_Localization.json") {
        filePath = LocalizationCachePath();
      } else {
        BlueprintsVariantLookup.TryGetValue(json_key, out string variantId);
        filePath = BlueprintsCachePath(variantId);
      }
      byte[] Data = new byte[0];
      // extract the json in to the given buffer
      #if UNITY_WEBGL
      int result = SharZipLibUtil.CoppyToBuffer(filePath, json_key, ref Data);
      #else
      int result = lzip.entry2Buffer(filePath, json_key, ref Data);
      #endif

      if(result != 1) {
        TFUtils.LogWarning("WARNING!!!!GetJsonAssetFromZip - JSON Zipfile could not extract " + json_key);
        if (throwException) {
          throw new Exception("GetJsonAssetFromZip - JSON Zipfile could not extract " + FilePath);
        }
        return default(T);
      }

      string strData = Encoding.ASCII.GetString(Data);
      // Free
      Data = null;

      dict = MiniJSON.Json.Deserialize(strData);
    }


    return (T)dict;
  }

  public static byte[] GetByteArrayFromZip(string FilePath, bool useLocal = false, bool throwException = true) {
    byte[] Data = new byte[0];
    string[] directories = FilePath.Split(Path.DirectorySeparatorChar);
    if(directories.Length == 2 && directories[0] == "Blueprints") {
      string json_key = "blueprints/" + directories[1];
      string filePath;
      if(json_key == "blueprints/db_Localization.json" || json_key == "blueprints/db_Localization.data" || json_key == "blueprints/db_Localization.bin") {
        filePath = LocalizationCachePath();
      } else {
        BlueprintsVariantLookup.TryGetValue(json_key, out string variantId);
        filePath = BlueprintsCachePath(variantId);
      }
      // extract the json in to the given buffer
      #if UNITY_WEBGL
      int result = SharZipLibUtil.CoppyToBuffer(filePath, json_key, ref Data);
      #else
      var result = lzip.entry2Buffer(filePath, json_key, ref Data);
      if (result == -2 || result == -18) {
        if (throwException) {
          throw new Exception("GetByteArrayFromZip - JSON Zipfile could not extract " + FilePath);
        }
      }
      #endif
    }

    return Data;
  }

  public static string GetStringFromZip(string FilePath, string fileName, string password = null) {
    byte[] Data = new byte[0];
    // extract the json in to the given buffer
    #if UNITY_WEBGL
    int result = SharZipLibUtil.CoppyToBuffer(FilePath, fileName, ref Data);
    #else
    int result = lzip.entry2Buffer(FilePath, fileName, ref Data, null, password);
    #endif
    
    if (result != 1) {
      TFUtils.LogWarning("WARNING!!!! GetStringFromZip Zipfile could not extract " + fileName);
      return string.Empty;
    }
    return Encoding.ASCII.GetString(Data);
  }
  
  public static string GetStringFromZipBuffer(byte[] buffer, string fileName, string password = null) {
    #if UNITY_WEBGL
    byte[] Data = SharZipLibUtil.entry2BufferMerged(buffer, fileName);
    #else
    byte[] Data = lzip.entry2BufferMerged(buffer, fileName, password);
    #endif

    return Encoding.ASCII.GetString(Data);
  }

  /// <summary>
  /// Use to get list data enum from response of new system so we can reuse old response handler function
  /// </summary>
  /// <param name="dataAsText"></param>
  /// <returns></returns>
  public static List<object>.Enumerator GetResponseData(string dataAsText) {
    Dictionary<string, object> innerData = (Dictionary<string, object>)MiniJSON.Json.Deserialize(dataAsText);
    List<object> data = null;
    if (innerData.ContainsKey("data")) {
      data = (List<object>)innerData["data"];
    }
    if (data != null && data.Count > 0) {
      List<object> dataDeeper = (List<object>)data[0];

      List<object>.Enumerator dataEnumerator = dataDeeper.GetEnumerator();
      dataEnumerator.MoveNext();  //message code
      dataEnumerator.MoveNext();  //error code
      dataEnumerator.MoveNext();  //data needed

      return dataEnumerator;
    }
    return new List<object>.Enumerator();
  }
  
  /// <summary>
  /// Use to get list data enum from response of new system so we can reuse old response handler function
  /// </summary>
  /// <param name="dataAsByte"></param>
  /// <returns></returns>
  public static List<object>.Enumerator GetResponseData(byte[] dataAsByte) {
    Dictionary<string, object> innerData = FlexToDict(FlxValue.FromBytes(dataAsByte).AsMap);
    List<object> data = (List<object>)innerData["data"];
    List<object> dataDeeper = (List<object>)data[0];

    List<object>.Enumerator dataEnumerator = dataDeeper.GetEnumerator();
    dataEnumerator.MoveNext();  //message code
    dataEnumerator.MoveNext();  //error code
    dataEnumerator.MoveNext();  //data needed
    // System.IO.File.WriteAllText("get_user_data.json", BattleUtils.ConvertToJsonString(innerData));
    #if !KFF_RELEASE
    Debug.Log($"<color=yellow>RequestResponse {GET_ALL_USER_DATA_URL} {Newtonsoft.Json.JsonConvert.SerializeObject(innerData)}</color>");
    #endif
    return dataEnumerator;
  }

  /// <summary>
  /// Get List Enumerator from FlexBuffer data
  /// </summary>
  /// <param name="dataAsByte"></param>
  /// <param name="messageType"></param>
  /// <returns></returns>
  public static List<object>.Enumerator GetResponseData(List<object>data, ICMPMessageType messageType) {
    foreach (var item in data) {
      var deeperData = (List<object>)item;
      var messageCode = Convert.ToInt32(deeperData[0]);
      if (messageCode == (int)messageType) {
        var dataEnumerator = deeperData.GetEnumerator();
        dataEnumerator.MoveNext(); //message code
        dataEnumerator.MoveNext(); //error code
        dataEnumerator.MoveNext(); //data needed
        return dataEnumerator;
      }
    }

    // System.IO.File.WriteAllText("get_user_data.json", BattleUtils.ConvertToJsonString(innerData));
    return new List<object>.Enumerator();
  }

  public static Dictionary<string, object> FlexToDict(FlxMap map) {
    Dictionary<string, object> result = new Dictionary<string, object>();
    foreach (var flx in map) {
      if (!flx.Value.IsNull) {
        switch (flx.Value.ValueType) {
          case FlexBuffers.Type.Bool:
            result.Add(flx.Key, flx.Value.AsBool);
            break;
          case FlexBuffers.Type.String:
            result.Add(flx.Key, flx.Value.AsString);
            break;
          case FlexBuffers.Type.Int:
            result.Add(flx.Key, flx.Value.AsLong);
            break;
          case FlexBuffers.Type.Float:
            result.Add(flx.Key, flx.Value.AsDouble);
            break;
          case FlexBuffers.Type.Map:
            result.Add(flx.Key, FlexToDict(flx.Value.AsMap));
            break;
          case FlexBuffers.Type.Vector:
            result.Add(flx.Key, FlexToList(flx.Value.AsVector));
            break;
        }
      } else {
        result.Add(flx.Key, null);
      }
    }

    return result;
  }
  
  public static List<object> FlexToList(FlxVector list) {
    List<object> result = new List<object>();
    foreach (var flx in list) {
      if (!flx.IsNull) {
        switch (flx.ValueType) {
          case FlexBuffers.Type.Bool:
            result.Add(flx.AsBool);
            break;
          case FlexBuffers.Type.String:
            result.Add(flx.AsString);
            break;
          case FlexBuffers.Type.Int:
            result.Add(flx.AsLong);
            break;
          case FlexBuffers.Type.Float:
            result.Add(flx.AsDouble);
            break;
          case FlexBuffers.Type.Map:
            result.Add(FlexToDict(flx.AsMap));
            break;
          case FlexBuffers.Type.Vector:
            result.Add(FlexToList(flx.AsVector));
            break;
        }
      } else {
        result.Add(null);
      }
    }

    return result;
  }
  
#if !KFF_RELEASE
  public static T[] GetJsonAssetFromZipNewJsonLocal<T>(string FilePath) {
    string Data = TFUtils.GetJsonFileContent(FilePath);
    Data = Encoding.ASCII.GetString(First) + Data + Encoding.ASCII.GetString(Last);
    T[] dict = JsonHelper.FromJson<T>(Data);
    return dict;
  }
#endif
  
  public static string GetJsonPath(string filePath) {
    #if UNITY_WEBGL && !UNITY_EDITOR
    return GetRrequest(filePath);
    #endif
    
    WWW www = null;
    www = new WWW(filePath);

    while (www.isDone == false) {
      // do nothing
    }

    return www.text;
  }
}