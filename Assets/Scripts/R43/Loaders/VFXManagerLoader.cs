using UnityEngine;

public class VFXManagerLoader : Singleton<VFXManagerLoader> {
  [SerializeField] private string prefabPath = "Prefabs/VFXManager";
  private VFXManager _controller;
  public VFXManager Controller {
    get {
      if(_controller == null) {
        GameObject prefab = KFFResourceManager.Instance.LoadResource(prefabPath) as GameObject;
        GameObject prefabGO = gameObject.InstantiateAsChild(prefab);
        if(prefabGO != null) {
          _controller = prefabGO.GetComponent<VFXManager>();
        }
      }
      return _controller;
    }
  }
}
