using Microsoft.AspNetCore.SignalR;
using Microsoft.AspNetCore.SignalR.Protocol;
#nullable disable
using System;
using System.Buffers;
using System.Collections.Generic;
using MessagePack;
using MessagePack.Formatters;
using MessagePack.Resolvers;
using Microsoft.AspNetCore.Connections;
using Microsoft.Extensions.Options;

namespace Gear.MsgPack.Protocol {
    
    public class MsgPackGearHubProtocol:IHubProtocol
    {
        private static readonly string ProtocolName = "messagepack";
            private static readonly int ProtocolVersion = 1;
            private readonly MsgPackGearHubProtocolWorker _worker;

            /// <inheritdoc />
            public string Name => ProtocolName;

            /// <inheritdoc />
            public int Version => ProtocolVersion;

            /// <inheritdoc />
            public TransferFormat TransferFormat => TransferFormat.Binary;

            /// <summary>
            /// Initializes a new instance of the <see cref="MsgPackGearHubProtocol"/> class.
            /// </summary>
            public MsgPackGearHubProtocol()
                : this(Options.Create(new MessagePackHubProtocolOptions()))
            { }

            /// <summary>
            /// Initializes a new instance of the <see cref="MsgPackGearHubProtocol"/> class.
            /// </summary>
            /// <param name="options">The options used to initialize the protocol.</param>
            public MsgPackGearHubProtocol(IOptions<MessagePackHubProtocolOptions> options)
            {
                if (options is null)
                {
                    throw new ArgumentNullException(nameof(options));
                }

                _worker = new MsgPackGearHubProtocolWorker(options.Value.SerializerOptions);
            }

            /// <inheritdoc />
            public bool IsVersionSupported(int version)
            {
                return version == Version;
            }

            /// <inheritdoc />
            public bool TryParseMessage(ref ReadOnlySequence<byte> input, IInvocationBinder binder, out HubMessage message)
                => _worker.TryParseMessage(ref input, binder, out message);

            /// <inheritdoc />
            public void WriteMessage(HubMessage message, IBufferWriter<byte> output)
                => _worker.WriteMessage(message, output);


            /// <inheritdoc />
            public ReadOnlyMemory<byte> GetMessageBytes(HubMessage message)
                => _worker.GetMessageBytes(message);

            internal static MessagePackSerializerOptions CreateDefaultMessagePackSerializerOptions() =>
                MessagePackSerializerOptions
                    .Standard
                    .WithResolver(SignalRResolver.Instance)
                    .WithSecurity(MessagePackSecurity.UntrustedData);

            internal class SignalRResolver : IFormatterResolver
            {
                public static readonly IFormatterResolver Instance = new SignalRResolver();

                public static readonly IReadOnlyList<IFormatterResolver> Resolvers = new IFormatterResolver[]
                {
                    DynamicEnumAsStringResolver.Instance,
                    ContractlessStandardResolver.Instance,
                };

                public IMessagePackFormatter<T> GetFormatter<T>()
                {
                    return Cache<T>.Formatter;
                }

                private static class Cache<T>
                {
                    public static readonly IMessagePackFormatter<T> Formatter;

                    static Cache()
                    {
                        foreach (var resolver in Resolvers)
                        {
                            Formatter = resolver.GetFormatter<T>();
                            if (Formatter != null)
                            {
                                return;
                            }
                        }
                    }
                }
            }
    }
}

