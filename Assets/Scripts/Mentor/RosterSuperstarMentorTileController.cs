using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using System.Collections;

public class UnlockMentorManager {
  public int unlockStar;
  public WrestlerTier unlockTier;
  public static bool IsFirstTimeUnlockMentor {
    get => bool.Parse(PlayerPrefs.GetString("UnlockMentor", "true"));
    set => PlayerPrefs.SetString("UnlockMentor", value.ToString());
  }

  public void UpdateConditionSlotMentor() {
    string eSlotSetID = MiscParams.Instance.EntourageSlotSetIDForMale;
    EntourageSlotSetData etrSlotSetData = EntourageSlotSetDataManager.Instance.GetData(eSlotSetID);
    string etrSlotID;
    for (int i = 0; i < etrSlotSetData.entourageSlots.Length; i++) {
      etrSlotID = etrSlotSetData.entourageSlots[i];
      EntourageSlotData etrSlotData = EntourageSlotDataManager.Instance.GetData(etrSlotID);
      if (etrSlotData.EntourageType == TrainerData.EntourageType.Mentor) {
        unlockStar = etrSlotData.UnlockStar;
        unlockTier = etrSlotData.UnlockTier;
      }
    }
  }
}

public class RosterSuperstarMentorTileController : MonoBehaviour {
  [SerializeField] private GameObject leagueIcon;
  [SerializeField] private GameObject rarityIcon;
  [SerializeField] private GameObject unlockIcon;
  [SerializeField] private GameObject lockedIcon;
  [SerializeField] private GameObject objUnlockMentor;
  [SerializeField] private Image imgBGMentor;
  [SerializeField] private Sprite BGUnlockSprite;
  [SerializeField] private Sprite BGLockedSprite;
  [SerializeField] private Text TxtUnlockMentor;
  [SerializeField] private Animator mentorUnlockAnimator;
  [SerializeField] private Transform replaceMentorPopupPos;
  [SerializeField] private GameObject replacePopupPrefab;
  [SerializeField] private GameObject BtnTapClickMentor;
  [SerializeField] private GameObject newFlagAnim;
  [SerializeField] private Text txtLeague;

  [SerializeField] private GameObject WrestlerTilePrefab;
  [SerializeField] private GameObject WrestlerTileRoot;
  [SerializeField] private GameObject[] rarityStars;
  [SerializeField] private GameObject iconLockLeague;
  [SerializeField] private AudioClip SFXCountDown;
  
  //R59
  [SerializeField] private Transform superStarTransform;
  [SerializeField] private GameObject textBackground;
  [SerializeField] private KFFText breakWrestlerPriceText;
  [SerializeField] private UITweenController ShowInfoTween;
  [SerializeField] private UITweenController HideInfoTween;
  [SerializeField] private Text infoText;
  
  private WrestlerTileScript mTile = null;

  private RosterSuperstarTrainersController rosterSuperstar = new RosterSuperstarTrainersController();
  private int currentLeagueLevel;
  private int unlockStar;
  private WrestlerTier unlockTier;
  private UnlockMentorManager UnlockMentor = new UnlockMentorManager();
  private PlayerLeagueConfigDataManager config = PlayerLeagueConfigDataManager.Instance;
  // private bool IsTierCondition { get { return (int)rosterSuperstar.wrestler.StartedTier >= tier.IndexOf(unlockTier); } }
  private bool IsReachTierRarityCondition => rosterSuperstar.wrestler.Form.TierRarityToInt() >= WrestlerDataManager.TierRarityToInt(unlockStar, unlockTier);
  private bool IsLeagueCondition { get { return currentLeagueLevel >= config.UnlockMentorLeague; } }
  private WrestlerItem wrestlerItem;
  private WrestlerItem mentorItem;
  
  //r59
  public const string INFO_TRAINER_TILE = "trainerTile";
  public const string INFO_TRAINER_CONTROLLER = "trainerController";
 
  public enum State {
    Unlock,
    Locked,
    Filled
  }
  private State m_State;
  public State state {
    get {
      return m_State;
    }
    private set {
      m_State = value;
      
      switch (state) {
        case State.Unlock:
          TxtUnlockMentor.text = KFFLocalization.Get("!!R49_MENTOR_UNLOCK_DEC");
          imgBGMentor.sprite = BGUnlockSprite;
          objUnlockMentor.SetActive(false);
          unlockIcon.SetActive(true);
          BtnTapClickMentor.SetActive(true);
          break;
        case State.Locked:
          SetRarityStars(wrestlerItem, rarityStars);
          txtLeague.text = config.UnlockMentorLeague.ToString();
          TxtUnlockMentor.text = KFFLocalization.Get("!!R49_MENTOR_LOCKED_DEC");
          imgBGMentor.sprite = BGLockedSprite;
          objUnlockMentor.SetActive(true);
         // BtnTapClickMentor.SetActive(false);
          iconLockLeague.SetActive(MiscParams.Instance.EnableLockedMentorByLeagueLvl);
          if (IsReachTierRarityCondition) {
            rarityIcon.SetActive(false);
          }
          if (IsLeagueCondition) {
            leagueIcon.SetActive(false);
          }
          lockedIcon.SetActive(true);
          break;
        case State.Filled:
          unlockIcon.SetActive(false);
          break;
      }
    }
  }

  public void Populate(RosterSuperstarTrainersController mRosterSuperstar) {
    rosterSuperstar = mRosterSuperstar;
    wrestlerItem = rosterSuperstar.wrestler;
    var mentor = wrestlerItem.GetMentor();
    if (mentor != null) {
      mentorItem = mentor.wrestler;
    } else {
      mentorItem = null;
    }
    currentLeagueLevel = PlayerInfoScript.Instance.SaveData.ResourcesData.PlayerLeagueManager.CurrentLeagueLevel;
    UnlockMentor.UpdateConditionSlotMentor();
    unlockStar = UnlockMentor.unlockStar;
    unlockTier = UnlockMentor.unlockTier;
    UnlockSlotMentor();
    TriggerFTUEMentor();
    //mentorUnlockAnimator.SetTrigger("Unlock");
    PopulateSS();
    TriggerNewFlagAnim();
    
  }
  public void PopulateInEntourageScreen() {
    if (textBackground != null && superStarTransform != null && TxtUnlockMentor != null) {
      textBackground.SetActive(false);
      TxtUnlockMentor.gameObject.SetActive(false);
      superStarTransform.localPosition = Vector3.zero;
      transform.localPosition = Vector3.zero;
    }
    if (mentorItem != null && breakWrestlerPriceText != null) {
      breakWrestlerPriceText.text = UnityUtils.FormatTruncateLargeNum(mentorItem.Form.ContractBreakCost);
    }
  }

  private void PopulateSS() {
    if (mentorItem != null) {
      state = State.Filled;
      WrestlerTileRoot.gameObject.SetActive(true);
      GameObject tile = null;
      if (mTile == null && WrestlerTileRoot != null) {
        tile = WrestlerTileRoot.InstantiateAsChild(WrestlerTilePrefab);
        tile.transform.localScale = new Vector3(1.34f, 1.34f, 1);
        if (tile != null) {
          mTile = tile.GetComponent<WrestlerTileScript>();
        }
      }
      //mTile.TileMode = WrestlerTileMode.Default;
      mTile.Populate(mentorItem);
      mTile.SetTalentGroup(true);
      mTile.SetButtonInteractability(false);
      mTile.SetClassTierActive(false);
      mTile.SetWrestlerStatusGroup(false);
      var trainer = TrainerDataManager.Instance.GetActiveBuffsAsTrainerR30(mentorItem.Form.GroupID, mentorItem.Talent, TrainerData.EntourageType.Mentor, true);
      if (trainer.Count > 0) {
        TxtUnlockMentor.text = trainer[0].Desc;
      } else {
        TxtUnlockMentor.text = string.Empty;
      }
    } else {
      WrestlerTileRoot.gameObject.SetActive(false);
    }
  }

  private void TriggerNewFlagAnim() {
    if (TrainerDataManager.Instance.CheckNewMentor() == true) {
      newFlagAnim.SetActive(true);
      mentorUnlockAnimator.SetTrigger("NewFlag");
    } else {
      newFlagAnim.SetActive(false);
    }
  }
  private void UnlockSlotMentor() {
    if (IsLeagueCondition && IsReachTierRarityCondition && !UnlockMentorManager.IsFirstTimeUnlockMentor) {
      state = State.Unlock;
    } else {
      state = State.Locked;
    }
  }
  
  public void TriggerAmin() {
    Invoke("TriggerAminUnlock", 0.5f);
  }

  private void TriggerAminUnlock() {
    mentorUnlockAnimator.SetTrigger("Unlock");
  }

  private void TriggerFTUEMentor() {
    if (IsLeagueCondition && IsReachTierRarityCondition && UnlockMentorManager.IsFirstTimeUnlockMentor) {
      var info = new UIScreen.ScreenInfo();
      info.Add(MentorTutorialUnlock_R49.MentorController, this);
      UIManager.Instance.QueuePush(MentorTutorialUnlock_R49.PrefabId ,info);

      UnlockMentorManager.IsFirstTimeUnlockMentor = false;
    }
  }

  private void OnTrggerAnim() {
    state = State.Unlock;
    lockedIcon.SetActive(false);
  }

  public void OnClickEquipMentor() {

    var mentor = mentorItem;
    if (state == State.Locked) {
      if (!string.IsNullOrEmpty(GearSessionManager.Instance.SCSUserAttributes.BundleOfferID)) {
        var offerData = OffersDataManager.Instance.GetData(GearSessionManager.Instance.SCSUserAttributes.BundleOfferID);
        int leagueLevel = PlayerInfoScript.Instance.SaveData.ResourcesData.PlayerLeagueManager.CurrentLeagueLevel;
        int unlockMentorLeague = PlayerLeagueConfigDataManager.Instance.UnlockMentorLeague;
        if (offerData != null && leagueLevel < unlockMentorLeague && leagueLevel < offerData.Leagues[2] && unlockMentorLeague <= offerData.Leagues[2]) {
          offerData.ShowBundleOffer(PlayerLeagueConfigDataManager.Instance.UnlockMentorLeague,ScreenCategory.roster,"roster_mentor_locked_feature");
        } else {
          ShowLockedNotice();
        }
      } else {
        ShowLockedNotice();
      }

    } else {
      if (mentor == null) {
        //r59 function
        OpenEntourageSelectPopup();
        //old function
        //EquipMentorController_R49.ShowEquipMentor(wrestlerItem, true, SetMentorCallback);
      } else {
        ShowMentorFunction();
      }
    }
  }
  private void ShowLockedNotice() {
    string text = "";
    if (IsLeagueCondition) {
      if (!(IsReachTierRarityCondition)) {
        text = string.Format(KFFLocalization.Get("!!R59_MENTOR_UNLOCK_REQUIREMENT_TIER"), unlockStar, unlockTier);
      }
    } else {
      if (IsReachTierRarityCondition) {
        text = string.Format(KFFLocalization.Get("!!R59_MENTOR_UNLOCK_REQUIREMENT_LEAGUE"), PlayerLeagueConfigDataManager.Instance.UnlockMentorLeague);
      } else {
        text = string.Format(KFFLocalization.Get("!!R59_MENTOR_UNLOCK_REQUIREMENT_ALL"), unlockStar, unlockTier, PlayerLeagueConfigDataManager.Instance.UnlockMentorLeague);
      }
    }
    UIToolTipController.Config config = new UIToolTipController.Config();
    config.tailDirection = UIToolTipController.TailDirection.Down;
    config.timeout = 1.0f;
    UIToolTipController.Instance.Show(gameObject, config, text);
    KFFSoundPlayer.Instance.PlayErrorSound();
  }
  private void OpenEntourageSelectPopup() {
    var info = new UIScreen.ScreenInfo();
    info.Add(INFO_TRAINER_CONTROLLER, rosterSuperstar);
    info.Add(LoadoutListPopupController_R59.CURRENT_WRESTLER, wrestlerItem);
    // info.Add(INFO_TRAINER_CONTROLLER, trainerCtrl);
    UIManager.Instance.QueuePush((EntourageSelectionController_R59.PREFAB_NAME), info);
  }
  public void SetMentorCallback() {
    wrestlerItem = rosterSuperstar.wrestler;
    var mentor = wrestlerItem.GetMentor();
    if (mentor != null) {
      mentorItem = mentor.wrestler;
    } else {
      mentorItem = null;
    }
    PopulateSS();
  }

  private void ShowMentorFunction() {
    var buttonContexts = new List<SimplePopupController.NewButtonContext>();
    SimplePopupController.NewButtonContext buttonContextTip;
    // Create Tip Style Text Button.
    buttonContextTip = new SimplePopupController.NewButtonContext();
    buttonContextTip.Title = TxtUnlockMentor.text;
    //buttonContextTip.OnClickCallback = hideR26Button.Play;
    buttonContextTip.BtnColor = SimplePopupController.NewButtonContext.ButtonColor.TipText;
    buttonContexts.Add(buttonContextTip);

    //SimplePopupController.Instance.ShowNewContext(GetButtonPosition(), buttonContexts.ToArray());

    SimplePopupController.NewButtonContext buttonContextManage;
    // R26 - level deeper trainer
    // Create Manage Button.
    buttonContextManage = new SimplePopupController.NewButtonContext();
    // unblock
    buttonContextManage.Title = KFFLocalization.Get("!!BUTTON_MANAGE");
    buttonContextManage.OnClickCallback = OnClickManager;
    buttonContextManage.BtnColor = SimplePopupController.NewButtonContext.ButtonColor.Green;
    buttonContexts.Add(buttonContextManage);
    // R26 - END
    // Create Replace Button.
    buttonContextManage = new SimplePopupController.NewButtonContext();
    // unblock
    buttonContextManage.Title = KFFLocalization.Get("!!BUTTON_REPLACE");
    buttonContextManage.OnClickCallback = OpenEntourageSelectPopup;
    buttonContextManage.BtnColor = SimplePopupController.NewButtonContext.ButtonColor.Orange;
    buttonContexts.Add(buttonContextManage);

    SimplePopupController.Instance.ShowNewContext(GetButtonPosition(), buttonContexts.ToArray());

    return;
    //old logic before R59
    var functionPopup = SimplePopupController.Instance.ShowNewContext(GetButtonPosition(), replacePopupPrefab);
    var component = functionPopup.GetComponent<BreakReplaceMentor_R49>();
    if (component != null) {
      long cost = mentorItem != null ? mentorItem.Form.ContractBreakCost : 0;
      component.UpdateUI(cost, OnClickManager, OnClickReplace, OnClickRemove);
    }
  }

  private void OnClickManager() {
    //SimplePopupController.Instance.OnNewContextCommonClick();
    var rosterHubController = UIManager.Instance.GetScreen<RosterSuperstarHubController>();
    var mentor = mentorItem;

    if (rosterHubController == null) {
      //WHIP-34537 NRE appears when managing the promoter through the whistle icon in Pre-match screen.
      if (mentor != null) {
        EntouragePopupController_R60 entouragePopupController = UIManager.Instance.GetScreen<EntouragePopupController_R60>();
        if (entouragePopupController != null) {
          entouragePopupController.Close();
        }
        
        UIScreen.ScreenInfo info = new UIScreen.ScreenInfo();
        info.Add("wrestler", mentor);
        info.Add("isOpenFromTrainer", true);
        UIManager.Instance.QueuePush(("frontEnd_roster_ss"), info);
      }
      return;
    }
    
    if (mentor != null ) {
      UIManager.Instance.StartCoroutine(rosterHubController.CoMoveToSSManageUI(mentor));
      //rosterHubController.OnEnterFromTrainer(mentor);
    }
  }

  public WrestlerItem GetMentorItem() {
    return mentorItem;
  }

  private void OnClickReplace() {
    //SimplePopupController.Instance.OnNewContextCommonClick();
    EquipMentorController_R49.ShowEquipMentor(wrestlerItem, false, SetMentorCallback);
  }

  private void OnClickRemove() {
   // SimplePopupController.Instance.OnNewContextCommonClick();
    OnClickUnset();
  }

  protected Vector3 GetButtonPosition() {
    Vector3 pos = replaceMentorPopupPos.position + Vector3.up * 1.2f + Vector3.left * 1.0f;
    return pos;
  }

  public void OnClickUnset() {
    if (mentorItem == null) {
      return;
    }
    string body = string.Format(KFFLocalization.Get("!!R49_CONFIRM_BREAK_MENTOR_CONTENT"), mentorItem.Form.Name);

    long cost = mentorItem.Form.ContractBreakCost;
    // Use SC for breaking contract. This is final now.
    SimplePopupController.Instance.ShowSoftCurrencyPurchasePrompt(
      body,
      KFFLocalization.Get("!!TRAINERS_NOT_ENOUGH_TO_BREAKCONTRACT"),
      cost,
      ConfirmUnset,
      true,
      KFFLocalization.Get("!!R49_CONFIRM_BREAK_MENTOR_TITLE"),
      KFFLocalization.Get("!!TRAINERS_NOT_ENOUGH_TO_BREAKCONTRACT_TITLE"));
  }

  private void ConfirmUnset() {
    var dict = new Dictionary<string, string[]>();
    int index = 4;
    // remove trainer from slot
    WrestlerItem wrestler = wrestlerItem;
    string[] trainerSlots = wrestler.GetTrainerIDs();

    // Send Unset Trainer Event.
    if (wrestler.Trainers.Length > index) {
      WPAnalyticsManager.Instance.TrackTrainer(wrestler, wrestler.Trainers[index].wrestler, false);
      // R49 - Mentor: Send Entourage event
      WPAnalyticsManager.Instance.TrackEntourageEvent(
        WPAnalyticsManager.EntourageEvents.BreakContract,
        wrestler.Trainers[index].wrestler,
        wrestler,
        TrainerDataManager.Instance.IsMentorEffectiveOnSS(wrestler, TrainerDataManager.TrainerBuffs[wrestler.Trainers[index].wrestler.Form.GroupID][0]));
    }

    trainerSlots[index] = "";
    dict.Add(wrestler.Form.ID, trainerSlots);

    //TFServerOp op = Multiplayer.Multiplayer.UpdateEntourage(null, dict, UnsetCallback);
    TFServerOp op = Multiplayer.Multiplayer.UpdateEntourageR45(null, wrestler.Form.ID, trainerSlots, null, -1, UnsetCallback);
    ICMultiplayer.Instance.BlockTillMessage(ICMPMessageType.UPDATE_ENTOURAGE, null, op);
    op.Execute();
  }

  public void SetRarityStars(WrestlerItem _wrestler, GameObject[] _RarityStars) {
    Image[] rarityStarImages = new Image[_RarityStars.Length];
    for (int i = 0; i < _RarityStars.Length; i++) {
      if (_RarityStars[i] != null)
        if (_RarityStars[i].GetComponent<Image>() != null)
          rarityStarImages[i] = _RarityStars[i].GetComponent<Image>();
    }

    SetRarityStars(_wrestler, rarityStarImages);
  }

  public void SetRarityStars(WrestlerItem _wrestler, Image[] _RarityStars) {
    WrestlerTier tier = WrestlerTier.Gold;
    int rarity = 0;
    // if (!Enum.TryParse(UnlockMentor.unlockTier, ignoreCase: true, out WrestlerTier tierRequire)) {
    WrestlerTier tierRequire = UnlockMentor.unlockTier;
    // }

    if (_wrestler != null) {
      tier = tierRequire;
      rarity = unlockStar;
    }

    int star_count = 0;
    for (star_count = 0; star_count < rarity; star_count++) {
      if (star_count >= _RarityStars.Length)
        return;
      if (_RarityStars[star_count] == null)
        continue;
      _RarityStars[star_count].sprite = SharedSpritesHolder.Instance.GetRarityStarSprite(tier);
      _RarityStars[star_count].gameObject.SetActive(true);
    }

    rarity = _wrestler.Form.Rarity;
    string original_id = _wrestler.GetOriginalWrestlerID();
    if (string.IsNullOrEmpty(original_id) == false) {
      for (; star_count < rarity; star_count++) {
        if (star_count >= _RarityStars.Length)
          return;
        if (_RarityStars[star_count] == null)
          continue;

        _RarityStars[star_count].sprite = SharedSpritesHolder.Instance.GetBlankRarityStarSprite(tier);
        _RarityStars[star_count].gameObject.SetActive(true);
      }
    }

    for (; star_count < _RarityStars.Length; star_count++) {
      if (star_count >= _RarityStars.Length)
        return;
      if (_RarityStars[star_count] == null)
        continue;
      _RarityStars[star_count].gameObject.SetActive(false);
    }
  }

  private void UnsetCallback(bool success, Dictionary<string, object> dict) {
    if (success) {
      //R59 remove VFX for Mentor
      //mentorUnlockAnimator.SetTrigger("Dismiss");
      //KFFSoundPlayer.Instance.PlayOneShot(SFXCountDown);
      wrestlerItem = rosterSuperstar.wrestler;
      state = State.Unlock;
      var mentor = wrestlerItem.GetMentor();
      if (mentor != null) {
        mentorItem = mentor.wrestler;
      } else {
        mentorItem = null;
      }
      PopulateSS();
      rosterSuperstar.OnMentorSet();
    }
  }

  private void PopulateBoosts() {
    infoText.text = TxtUnlockMentor.text;
  }
  
  public void ShowBoostToolTip() {
  
    PopulateBoosts();
  
    ShowInfoTween.Play();
  }
  
  public void HideBoostToolTip() {
    HideInfoTween.Play();
  }
}