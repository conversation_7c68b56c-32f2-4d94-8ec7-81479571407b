using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public partial class TutorialController {


  public void R44ShowFreeSSTut() {
    RBEEventInfoController_R44 eventInfo = UIManager.Instance.GetScreen<RBEEventInfoController_R44>();
    if (eventInfo != null && eventInfo.rbeInfoController != null) {
      RBEUser user = RBEManager.Instance.GetRBEUser(eventInfo.rbeInfoController.rbeEventData.ID);
      HideShade();
      ShowShadeRectTransfrom(eventInfo.rbeInfoController.freeSSRow);
      if (!RBEManager.Instance.cssManager.IsRBEHasFreeCSS(eventInfo.rbeInfoController.rbeEventData.ID) || user.ClaimedSS) {
        AdvanceToState("NewContestR44_Step4");
        HideShade();
      }
    }
  }

  public void R44ShowShadeDisable() {
    ShowShadeDisableRaycast("0,530,3000,900");
    ShowShadeDisableRaycast2("0,-530,3000,900");
  }

  public void R44ScrollToTabRow() {
    RBEEventInfoController_R44 eventInfo = UIManager.Instance.GetScreen<RBEEventInfoController_R44>();
    if (eventInfo != null) {
      eventInfo.ScrollToTaskTab();
      ForceTapButtonWithSpawnWait("RBEEventInfoTabButton_0, TapEnhancedTopToBottom");
    }
    HideShade();
  }

  public void R44ShowCompetitionTut() {
    RBEEventInfoController_R44 eventInfo = UIManager.Instance.GetScreen<RBEEventInfoController_R44>();
    if (eventInfo != null && eventInfo.rbeInfoController != null) {
      if (eventInfo.rbeInfoController.rbeEventData.HasFeeder) {
        ForceTapButtonWithSpawnWait("RBEEventInfoTabButton_1, TapEnhancedBottomToTop");
      } else {
        AdvanceToState("NewContestR44_Step7");
      }
    }
    HideShade();
  }

  public void R44ShowContestSSTut() {
    RBEEventInfoController_R44 eventInfo = UIManager.Instance.GetScreen<RBEEventInfoController_R44>();
    if (eventInfo != null && eventInfo.rbeInfoController != null && eventInfo.rbeInfoController.rbeEventData!= null) {
      if (RBEManager.Instance.cssManager.IsRBEHasFreeCSS(eventInfo.rbeInfoController.rbeEventData.ID) ||
        RBEManager.Instance.cssManager.IsRBEHasCSS(eventInfo.rbeInfoController.rbeEventData.ID)) {
        ForceTapButtonWithSpawnWait("RBEEventInfoTabButton_2, TapEnhancedBottomToTop");
      } else {
        AdvanceToState("NewContestR44_Step9");
      }
    }
    HideShade();
  }

  public void R44Step9(string tabIndex) {
    int index = -1;
    int.TryParse(tabIndex, out index);
    ContestTurorialPopup_R44 contestTut = UIManager.Instance.GetScreen<ContestTurorialPopup_R44>();
    if (contestTut == null) {
      UIScreen.ScreenInfo info = new UIScreen.ScreenInfo();
      info.Add("tab", index);
      UIManager.Instance.QueuePush("frontEnd_tutorial_contest_r44", info);
    } else {
      contestTut.ShowStep(index);
    }
  }

  public void R44EndTutorial() {
    ContestTurorialPopup_R44 contestTut = UIManager.Instance.GetScreen<ContestTurorialPopup_R44>();
    if (contestTut != null) {
      contestTut.CloseScreen();
    }
    RBEEventListController eventList = UIManager.Instance.GetScreen<RBEEventListController>();
    if(eventList!= null && eventList.scrollRectEnsure != null) {
      eventList.SetScrollActive(true);
    }
    RBEEventInfoController_R44 rbeInfo = UIManager.Instance.GetScreen<RBEEventInfoController_R44>();
    if (rbeInfo != null && rbeInfo.rbeInfoController!= null) { 
      rbeInfo.rbeInfoController.SetScrollActive(true);
    }
  }

  public void R42FocusOnLeagueTile() {
    GameObject obj = GameObject.Find("UI_FrontEnd_League_Objective_Tile_r42(Clone)");
    if (obj.activeInHierarchy) {
      HideShade();
      ShowShadeRectTransfrom(obj.GetComponent<RectTransform>());
    }
  }
}
