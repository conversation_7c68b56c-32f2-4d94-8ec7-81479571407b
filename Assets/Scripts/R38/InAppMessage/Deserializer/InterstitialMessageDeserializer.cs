using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class InterstitialMessageDeserializer : InAppMessageDeserializer
{
  public void Deserializer(Dictionary<string, object> data, BaseInAppMessagePopup messagePopup)
  {
    object outputText;

    // Get background image url
    if (data.TryGetValue("bg_image", out outputText)) {
      messagePopup.PopulateBackgroundImage((string)outputText);
    }

    string[] buttonData;

    // Get primary button data
    // example: "CLICK HERE|deeplink://actionlink"
    if (data.TryGetValue("primary_button", out outputText)) {
      if (outputText is string && !string.IsNullOrEmpty(outputText as string)) {
        buttonData = (outputText as string).Split('|');
        if (buttonData != null && buttonData.Length >= 2) {
          messagePopup.PopuplateButtonActionText(1, KFFLocalization.Get(buttonData[0]));
          messagePopup.PopulateButtonActionLink(1, buttonData[1]);
        }
      }
    }

    if (data.TryGetValue("secondary_button", out outputText)) {
      if (outputText is string && !string.IsNullOrEmpty(outputText as string)) {
        buttonData = (outputText as string).Split('|');
        if (buttonData != null && buttonData.Length >= 2) {
          messagePopup.PopuplateButtonActionText(0, KFFLocalization.Get(buttonData[0]));
          messagePopup.PopulateButtonActionLink(0, buttonData[1]);
        }
      }
    }

    if (data.TryGetValue("timer", out outputText)) {
      if (outputText is string && !string.IsNullOrEmpty(outputText as string)) {
        int timer = System.Int32.Parse(outputText as string);
        if (timer != 0) {
          (messagePopup as SimpleInAppMessagePopup).ShowRemainingMessageTime();
        }
      }
    }
  }
}
