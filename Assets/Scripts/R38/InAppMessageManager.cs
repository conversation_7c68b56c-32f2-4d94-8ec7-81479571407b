using System.Collections;
using System.Collections.Generic;
using Scopely;
using UnityEngine;
using System.Linq;
using System;
using Scopely.InAppMessaging;

public class InAppMessageManager
{
  public const string MESSAGE_PLACEMENT_HOME = "HOME";
  public const string MESSAGE_PLACEMENT_ROSTER = "ROSTER";
  public const string MESSAGE_PLACEMENT_GACHA = "GACHA";
  public const string MESSAGE_PLACEMENT_CONTEST = "CONTEST";
  public const string MESSAGE_PLACEMENT_CONTEST_DETAILS = "CONTEST_DETAILS";
  public const string MESSAGE_PLACEMENT_MANAGE = "MANAGE";
  public const string MESSAGE_PLACEMENT_PVE_POSTMATCH = "PVE_POSTMATCH";
  public const string MESSAGE_PLACEMENT_PVE_POSTMATCH_WIN = "PVE_POSTMATCH_WIN";
  public const string MESSAGE_PLACEMENT_PVE_POSTMATCH_LOSS = "PVE_POSTMATCH_LOSS";
  public const string MESSAGE_PLACEMENT_FACTION = "FACTION";
  public const string MESSAGE_PLACEMENT_GB = "GB";
  public const string MESSAGE_PLACEMENT_MESSAGE = "MESSAGE";
  public const string MESSAGE_PLACEMENT_DAILY_CLAIM = "DAILY_CLAIM";
  protected static InAppMessageManager instance;
  public static InAppMessageManager Instance 
  {
    get {
      if (instance == null) {
        instance = new InAppMessageManager();
      }
      return instance;
    }
    private set {
      instance = value;
    }
  }
  
  /// Contains registered in-app message popup
  /// The key is template id
  protected Dictionary<string, BaseInAppMessagePopup> MessagePopupMap = new Dictionary<string, BaseInAppMessagePopup>();

  protected bool didStartService = false, didFetchMessage = false;
  protected BaseInAppMessagePopup currentShowingPopup;
  protected Dictionary<string, Action[]> buttonCallbackMap = new Dictionary<string, Action[]>();

  public List<string> ListChainedMessages = new List<string>();

  #region Public Function

  /// <summary>
  /// Register a popup with specific template id
  /// </summary>
  public static void RegisterPopup(string templateID, BaseInAppMessagePopup popup){
    if (Instance.MessagePopupMap.ContainsKey(templateID) && Instance.MessagePopupMap[templateID] != null) {
      GameObject.Destroy(Instance.MessagePopupMap[templateID].gameObject);
    }
    Instance.MessagePopupMap[templateID] = popup;
  }

  [RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.BeforeSplashScreen)]
  private static void SetupMessagingConnection()
  {
      InAppMessagingPackage.EnableServerConnection();
      Instance.didStartService = true;
      Debug.Log("=====InAppMessagingPackage.EnableServerConnection");
  }

  /// <summary>
  /// Get a popup instance by type name
  /// return null if there's no matching type
  /// </summary>
  public static BaseInAppMessagePopup GetPopupByTemplateID(string templateID) {
    StartMessagingService();
    if (!Instance.MessagePopupMap.ContainsKey(templateID)) {
      return null;
    }

    return Instance.MessagePopupMap[templateID];
  }

  /// <summary>
  /// Trigger a message
  /// Return false if there's no suitable popup for this message
  /// </summary>
  public static void TriggerMessage(string placement, Action[] callbacks = null) {
    StartMessagingService();
    
    InAppMessagingPackage.InAppMessageApi.FetchInAppMessages();
    if (!Instance.didFetchMessage) {
      InAppMessagingPackage.InAppMessageApi.OnInAppMessagesRetrieved(messages => {
        Instance.UpdateAvailableTemplate(new HashSet<string>(messages.Select(msg => msg.Id)));
        if (!Instance.didFetchMessage) {
          InAppMessagingPackage.InAppMessageApi.TriggerInAppMessage(placement);
          Instance.didFetchMessage = true;
        }
      });
    } else {
      InAppMessagingPackage.InAppMessageApi.TriggerInAppMessage(placement);
    }
    
    InAppMessagingPackage.InAppMessagePollerApi.StopInAppMessagePoller();

    if (callbacks != null) {
      Instance.buttonCallbackMap[placement] = callbacks;
    }
  }

  public static Action[] GetButtonCallback(string placement) {
    if (Instance.buttonCallbackMap.ContainsKey(placement)) {
      return Instance.buttonCallbackMap[placement];
    }
    return null;
  } 
  
  /// <summary>
  /// Need to be called when a popup is shown up
  /// </summary>
  /// <param name="popup"></param>
  public static void OpenPopup(BaseInAppMessagePopup popup) {
    Instance.currentShowingPopup = popup;
  }

  /// <summary>
  /// Need to be called when a popup is closed
  /// </summary>
  /// <param name="popup"></param>
  public static void ClosePopup(BaseInAppMessagePopup popup) {
    if (Instance.currentShowingPopup == popup) {
      Instance.currentShowingPopup = null;
    }
  }

  public static BaseInAppMessagePopup GetCurrentShowingPopup() {
    return Instance.currentShowingPopup;
  }
  #endregion

  protected static void StartMessagingService() {
    if (!Instance.didStartService) {
      InAppMessagingPackage.EnableServerConnection();
      Instance.didStartService = true;
    }
  }

  protected void UpdateAvailableTemplate(HashSet<string> messageIDs) {
    //InAppMessagingPackage.InAppMessageApi.ChangeAvailableTemplates(new HashSet<string>(MessagePopupMap.Keys));
    InAppMessagingPackage.InAppMessageApi.ChangePrefetchedMessages(messageIDs);
  }
}