using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class RestrictionChangedPopupControllerR62 : UIScreen
{
  public const string Prefab_Name = "restriction_changed_popup_r62";
  
  [SerializeField] private Toggle checkmarkNeverShowToggle;

  public override void Setup() {
  }
  public static void ShowRestrictionChangedPopup(string tourneyID, uint startTime) {
    PVPHubController.Instance.ShowPVPLoadoutChangePopup(tourneyID, startTime);
    ScreenInfo info = new ScreenInfo();
    UIManager.Instance.QueuePush(Prefab_Name, info, true);
  }
  public override IEnumerator PushRoutine(ScreenInfo info) {
    Populate();
    return base.PushRoutine(info);
  }
  public void Populate() {
    checkmarkNeverShowToggle.onValueChanged.AddListener(delegate {
      SetNeverShowState();
    });
  }
  private void SetNeverShowState() {
    PlayerInfoScript.Instance.SaveData.PlayerData.DisableWarnings[(int)WPPlayerData.DisableWarningType.RestrictionChanged] = checkmarkNeverShowToggle.isOn;
  }
  
  public static bool ShouldShowRestrictionChangedPopup() {
    return !PlayerInfoScript.Instance.SaveData.PlayerData.DisableWarnings[(int)WPPlayerData.DisableWarningType.RestrictionChanged];
  }
  public void OnButtonConfirmClick() {
    ToastSlotRestictionChanged();
    CloseScreen();
  }

  public void OnCancelButtonClick() {
    ToastSlotRestictionChanged();
    CloseScreen();
  }
  private void ToastSlotRestictionChanged() {
    ToastController.ShowInformationalToast(KFFLocalization.Get("!!R62_TEAM_CHANGED_TOAST_CONTENT"));
    UIManager.Instance.TryGetScreen<PVPHubPanel>()?.ShowLineUpChangedVFX(true);
  }
}
