using System.Collections;
using System.Collections.Generic;
using UnityEngine;
public class PTBWeightData : ILoadableData {
  private string _ID;

  public string ID {
    get {
      return _ID;
    }
  }
  public string[] BoostID { get; private set; }
  public int[] BoostWeight { get; private set; }

  public void PopulateCsv(List<string> row, Dictionary<string, int> headersDict) {
    
  }

  public void Populate(Dictionary<string, object> dict) {
    //We will use the new method PopulateNewJson so nothing to do in here. But still need to put this method cause of interface requirement
  }
  public void PopulateNewJson(DataRaw raw) {
    PTBWeightDataRaw dataRaw = raw as PTBWeightDataRaw;
    _ID = dataRaw.TableID;
    BoostID = dataRaw.BoostID;
    BoostWeight = dataRaw.BoostWeight;
  }

  public void PopulateProtobuf(DataRaw raw)
  {
    throw new System.NotImplementedException();
  }
  
  public void LoadDependencies() { }
}

public class PTBWeightDataManager : DataManager<PTBWeightData> {

  protected static PTBWeightDataManager _instance = null;
  public static PTBWeightDataManager Instance {
    get {
      if (_instance == null) {
        string filePath = System.IO.Path.Combine("Blueprints", "db_PTBWeightTables.json");
        _instance = new PTBWeightDataManager(filePath);
      }

      if (!_instance.IsLoaded) {
        _instance.LoadImmediate();
      }

      return _instance;
    }
  }

  public PTBWeightDataManager(string path) {
    FilePath = path;
  }

  public override DataRaw[] GetJsonDataFromZip(string FilePath, bool useLocal = false) {
    return GearLoadingUtils.GetJsonAssetFromZipNewJson<PTBWeightDataRaw>(FilePath, useLocal);
  }
}