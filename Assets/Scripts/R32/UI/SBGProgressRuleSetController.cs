using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class SBGProgressRuleSetController : UIScreen {

  public static string prefabName = "frontEnd_sbg_rule_set";
  public GameObject pfbSBGRuleSetItem;
  public RectTransform sbgRuleSetParent;
  public RectTransform ruleSetRoot;
  string[] sbgEventRules;

  public override void Setup() {
  }

  public override IEnumerator PushRoutine(ScreenInfo info) {
    if (info != null) {
      if (info.KeyExists("SBGEventRules")) {
        sbgEventRules = info.Get<string[]>("SBGEventRules");
        Populate();
        yield return new WaitForEndOfFrame();
        ruleSetRoot.SetHeight(sbgRuleSetParent.GetHeight() + 180);
      }
    }
    yield return base.PushRoutine(info);
  }

  public void Populate() {
    if (sbgEventRules == null)
      return;
    foreach (string ruleID in sbgEventRules) {
      GameObject ruleSetItem = Instantiate(pfbSBGRuleSetItem, sbgRuleSetParent);
      SBGPointRuleData ruleSetData = SBGPointRuleDataManager.Instance.GetData(ruleID);
      SBGProgressRuleSetItemController ruleController = ruleSetItem.GetComponent<SBGProgressRuleSetItemController>();
      if (ruleController != null)
        ruleController.Populate(ruleSetData);
    }

  }
}
