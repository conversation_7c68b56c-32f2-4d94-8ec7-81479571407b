using System;
using System.Collections.Generic;
using System.Linq; 

namespace Commands
{
	public class GenericSerialCommand<T> : Command where T : Command
	{
		protected List<T> commands;
		protected int completeCount = 0;

		public GenericSerialCommand(float seconds, params T[] commands) : base(seconds)
		{
			this.commands = commands.ToList();
		}

		public GenericSerialCommand(params T[] commands) : this(0f, commands)
		{
		}

		protected sealed override void Execute()
		{
			completeCount = 0;
			if (commands.Count <= 0)
			{
				Complete();
			}
			else
			{
				commands[0].onComplete += OnSubcommandComplete;
				commands[0].Start();
			}
		}

		protected virtual void OnSubcommandComplete(Command target)
		{
			target.onComplete -= OnSubcommandComplete;
			completeCount++;

			if (completeCount == commands.Count)
			{
				Complete();
			}
			else
			{
				commands[completeCount].onComplete += OnSubcommandComplete;
				commands[completeCount].Start();
			}
		}

		public override void SkipToEnd()
		{
			SkipWait();

			foreach (var command in commands)
			{
				command.onComplete -= OnSubcommandComplete;
				command.SkipToEnd();
			}

			Complete();
		}
		public  void SkipToLastCommand()
		{
			SkipWait();
			if (completeCount < 0 || commands.Count <= completeCount)
				return;

			commands[completeCount].onComplete -= OnSubcommandComplete;
			for (; completeCount < commands.Count - 1; completeCount++)
				commands[completeCount].SkipToEnd();

			commands[completeCount].onComplete += OnSubcommandComplete;
			commands[completeCount].Start();
		}
			
		public override void Stop()
		{
			if (inProgress)
			{
				SkipWait();

				commands[completeCount].onComplete -= OnSubcommandComplete;
				commands[completeCount].Stop();
			}
		}
	}

	public class SerialCommand : GenericSerialCommand<Command>
	{
		public SerialCommand(float seconds, params Command[] commands) : base(seconds)
		{
			this.commands = commands.ToList();
		}

		public SerialCommand(params Command[] commands) : this(0f, commands)
		{
		}
	}
}

