using System.Collections.Generic;
using System.Linq;
using UnityEngine;

[System.Serializable]
public class SFXPitchGroup {
  public string group_name;

  public float start_pitch = 1.0f;
  public float pitch = 1.0f;
  public float pitch_max = 3.0f;
  public float pitch_step = 0.1f;

  public void SetOriginal() {
    start_pitch = pitch;
  }

  public void ResetPitch() {
    pitch = start_pitch;
  }

  public void IncrementPitch() {
    pitch += pitch_step;
  }

}

[System.Serializable]
public class SoundArray {
  public string group_name;
  public AudioClip[] sounds;
}

public class KFFSoundPlayer : MonoBehaviour {
  public List<AudioSource> AudioSources = new List<AudioSource>();
  public List<AudioSource> LoopAudioSources = new List<AudioSource>();
  public List<AudioSource> SpecificSounrces = new List<AudioSource>();

  public AudioClip ErrorSound;
  public AudioClip DisableButtonSound;
  public AudioClip[] ImpactSoundsForRandom;

  public AudioClip[] GenericSoundList;
  public SoundArray[] SoundArrayList;

  public SFXPitchGroup[] SFXPitchGroupList;

  public AudioClip ExpLoopSound;

  public const float VolumeMinimumValue = 0f;
  public const float VolumeMaximumValue = 1f;
  public const float VolumeDefaultValue = 1f;

  public const string SFX_VOLUME_KEY = "SFX_Volume";
  public bool enable_sound_array_list;

  /// R43 Optimization
	public static KFFSoundPlayer Instance {
    get {
      if(KFFSoundPlayerLoader.HasInstance) {
        return KFFSoundPlayerLoader.Instance.Controller;
      }
      return null;
    }
  }

  public static bool HasInstance {
    get {
      return Instance != null;
    }
  }

  private bool isStartFuncCalled;
  public void ForceStart() {
    if(!isStartFuncCalled) {
      Start();
      isStartFuncCalled = true;
    }
  }
  //===================

  void Start() {
    if(isStartFuncCalled) {
      return;
    }
    var sources = GetComponents<AudioSource>();
    var list = sources.ToList();
    AudioSources = list;
  }

  public float GetVolumeInSetting() {
    float SfxVolume = PlayerPrefs.GetFloat(SFX_VOLUME_KEY, VolumeDefaultValue);
    return SfxVolume;
  }

  public void PlayOneShot(AudioClip clip, bool randomPitch = false) {
    if (clip == null) {
      return;
    }

    var audio = GetAvailableSource();
    if (audio == null) {
      audio = gameObject.AddComponent<AudioSource>();
      AudioSources.Add(audio);
      audio.outputAudioMixerGroup = WPAudioMixer.Instance.MasterAudioMixer.FindMatchingGroups("SFX")[0];
    }
    
    if (randomPitch)
      audio.pitch = Random.Range(.5f, 1.5f);
    audio.PlayOneShot(clip);
  }

  public void Play(AudioClip clip, bool randomPitch = false) {
    //Nate: This is not a good solution as it recycles unrelated AudioSources to unrelated clips.
    // Which lead to the hacky isAlreadyPlaying() solution below.
    // Yes, we pass in null clips, I don't know why. Likely old crufty sound calls with sounds that were removed.
    if (clip == null || (clip != null && isAlreadyPlaying(clip.name))) {
      // If the clip is null or if the sound is playing, get out. We don't allow overlapping sounds in Play() due to for loop implementations.
      return; 
    }
    
    var audio = GetAvailableSource();
    if (audio == null) {
      audio = gameObject.AddComponent<AudioSource>();
      AudioSources.Add(audio);
      audio.outputAudioMixerGroup = WPAudioMixer.Instance.MasterAudioMixer.FindMatchingGroups("SFX")[0];
    }
    audio.clip = clip;
    audio.Play();
  }

  public bool isAlreadyPlaying(string clipName) {
    //Nate: A hacky solution for an already terrible sound system.
    //      This "solution" works with all the bad implementations of Play(), like being called in a for loop.
    foreach (var audio in AudioSources) {
      if (audio.clip != null && audio.clip.name == clipName && audio.isPlaying) {
        return true;
      }
    }
    return false;
  }

  public void StopSound(AudioClip clip) {
    foreach (var audio in AudioSources) {
      if (audio.clip == clip) {
        audio.Stop();
      }
    }
  }

  public void PlayLoopSound(AudioClip clip) {
    var audio = GetAvailableLoopSource();
    if (audio == null) {
      audio = gameObject.AddComponent<AudioSource>();
      LoopAudioSources.Add(audio);
      audio.outputAudioMixerGroup = WPAudioMixer.Instance.MasterAudioMixer.FindMatchingGroups("SFX")[0];
      audio.loop = true;
    }
    audio.clip = clip;

    audio.Play();
  }

  public void StopLoopSound(AudioClip clip) {
    foreach (var audio in LoopAudioSources) {
      if (audio.clip == clip) {
        audio.Stop();
      }
    }
  }

  public void PlaySpecificSound(AudioClip clip, float volume = 1, bool duplicatable = false) {
    if (!duplicatable && SpecificSounrces.Exists(e => e.clip == clip && e.isPlaying)) {
      return;
    }
    var audio = GetAvailableSpecificSource();
    if (audio == null) {
      audio = gameObject.AddComponent<AudioSource>();
      SpecificSounrces.Add(audio);
      audio.outputAudioMixerGroup = WPAudioMixer.Instance.MasterAudioMixer.FindMatchingGroups("SFX")[0];
    }

    audio.clip = clip;
    audio.volume = volume;
    audio.Play();
  }

  public void StopAllSpecificSound() {
    SpecificSounrces.ForEach((e) => {
        if (e.isPlaying) {
          e.Stop();
        }
      });
  }

  public void PlayIndexedGenericSound(int index) {
    // first check our array list
    if (SoundArrayList.Length > index && enable_sound_array_list) {
      // now make sure the array it carries exists
      if (SoundArrayList[index].sounds.Length > 0) {
        int rnd = Random.Range(0, SoundArrayList[index].sounds.Length);

        // play one shot
        PlayOneShot(SoundArrayList[index].sounds[rnd]);
        
        return;
      }
    }

    // make the index we want exists
    if (GenericSoundList.Length > index) {
      // play one shot
      PlayOneShot(GenericSoundList[index]);
    } else {
      // index requested doesnt exist - debug log
      Debug.Log("WARNING - index " + index + " does not exist in the Generic Sound List");
    }
  }

  public void PlayRandomImpactSound() {
    int rnd = Random.Range(0, ImpactSoundsForRandom.Length);
    PlayOneShot(ImpactSoundsForRandom[rnd]);
  }

  private AudioSource GetAvailableSource() {
    foreach (var audio in AudioSources) {
      if (!audio.isPlaying)
        return audio;
    }
    return null;
  }

  private AudioSource GetAvailableLoopSource() {
    foreach (var audio in LoopAudioSources) {
      if (!audio.isPlaying)
        return audio;
    }
    return null;
  }

  private AudioSource GetAvailableSpecificSource() {
    foreach (var audio in SpecificSounrces) {
      if (!audio.isPlaying)
        return audio;
    }
    return null;
  }

  public void PlayErrorSound() {
    PlayOneShot(ErrorSound);
  }

  public void PlayDisableButtonSound() {
    PlayOneShot(DisableButtonSound);
  }

  public void PlayExpBarSound(bool trigger) {
    if (trigger)
      PlayLoopSound(ExpLoopSound);
    else
      StopLoopSound(ExpLoopSound);
  }

  public float GetSFXPitch(int index) {
    // make sure the index is legal
    if (SFXPitchGroupList.Length <= index) {
      Debug.Log("WARNING SFXPitchGroupList does not contain index " + index);
      return 1.0f;
    }

    // check if max reached
    if (SFXPitchGroupList[index].pitch > SFXPitchGroupList[index].pitch_max) {
      SFXPitchGroupList[index].ResetPitch();
    }

    float current_pitch = SFXPitchGroupList[index].pitch;
    // determine step size
    SFXPitchGroupList[index].IncrementPitch();

    return current_pitch;
  }

  public void ResetSFXPitchList() {
    for (int i = 0; i < SFXPitchGroupList.Length; i++)
      SFXPitchGroupList[i].ResetPitch();
  }

  public void ResetRandomPitch() {
    foreach (var audio in AudioSources)
      audio.pitch = 1f;
  }

  public bool DebugPrintAudio;

#if UNITY_EDITOR
  void Update() {
    if (DebugPrintAudio) {
      var sources = FindObjectsOfType(typeof(AudioSource)) as AudioSource[];
      foreach (AudioSource audioSource in sources) {
        if (audioSource.gameObject.name == "KFFMusicController")
          return;

        if (audioSource.isPlaying) {
          Debug.Log(audioSource.name + " is playing " + audioSource.clip.name, audioSource.gameObject);
          Debug.Log("---------------------------"); //to avoid confusion next time
        }
      }
    }
  }
#endif
}