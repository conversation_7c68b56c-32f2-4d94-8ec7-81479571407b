using UnityEngine;
using System.Collections;

public class SwizzleCrypto
{
	private int[] swizzleMap;

	public SwizzleCrypto( int[] inSwizzleMap )
	{
		swizzleMap = inSwizzleMap;

#if DEBUG || UNITY_EDITOR
		// Validate the map...
		int bits = 0;
		foreach( int bitIndex in swizzleMap )
		{
			int bitMask = 0x1 << bitIndex;
			TFUtils.Assert( (bits & bitMask) == 0, "Bad swizzle map - has overlapping swizzle bits" );
			bits |= bitMask;
		}

		// Validate the encrypt/decrypt
		var num = (byte)12;
		var enc = this.Encrypt(num);
		var dec = this.DecryptByte(enc);
		TFUtils.Assert (dec == num, "Something up with the swizzle encrypt/decrypt");

#endif // DEBUG || UNITY_EDITOR
	}

	public uint Encrypt( byte key )
	{
		TFUtils.Assert( swizzleMap.Length >= 8, "Bad swizzle operation - swizzle map not large enough to handle key" );
		uint encryptedKey = (uint) (UnityEngine.Random.value * uint.MaxValue);
		for( int bitIndex = 0, bitCount = swizzleMap.Length; bitIndex < bitCount; bitIndex++ )
		{
			int swizzleBit	= swizzleMap[ bitIndex ];
			int swizzleMask = ~(0x1 << swizzleBit);
			encryptedKey 	=  (uint)(encryptedKey & swizzleMask) | (uint)( ((key >> bitIndex) & 0x1) << swizzleBit );
		}
		
		return encryptedKey;
	}

	public byte DecryptByte( uint key )
	{
		TFUtils.Assert( swizzleMap.Length >= 8, "Bad unswizzle operation - swizzle map not large enough to handle key" );
		byte decryptedKey = 0;
		for( int bitIndex = 0, bitCount = swizzleMap.Length; bitIndex < bitCount; bitIndex++ )
		{
			int swizzleBit	= swizzleMap[ bitIndex ];
			decryptedKey	|= (byte) (( (key >> swizzleBit) & 0x1 ) << bitIndex);
		}
		
		return decryptedKey;
	}
}

