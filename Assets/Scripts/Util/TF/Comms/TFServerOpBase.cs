using System;
using System.Collections.Generic;
using System.Net;

public abstract class TFServerOpBase<T> : TFServerOp {
  protected TFServer server;
  protected string url;
  protected T callback;

  protected HttpStatusCode statusCode;
  protected string response;
  protected bool completed = false;
  protected bool blocking = false;

  protected int numTries = 0;
  protected bool m_AddUploadProgress;
  protected bool m_IsSendRequestSuccess;

  /// <summary>
  /// Whether this op has been sent or blocked by network not available
  /// </summary>
  protected bool m_IsSendRequest;

  protected Action<TFServerOp, bool> m_OnTimeoutOverride;

  public bool Completed() {
    return completed;
  }

  public void SetNotCompleted() {
    completed = false;
  }

  public void SetBlocking() {
    blocking = true;
  }

  public HttpStatusCode Status() {
    return statusCode;
  }

  public string Response() {
    return response;
  }

  public virtual bool HasTimeoutOverride() {
    return m_IsSendRequest && m_OnTimeoutOverride != null;
  }

  public virtual void OnTimeoutErrorOverride() {
    m_OnTimeoutOverride?.Invoke(this, m_IsSendRequestSuccess);
  }

  protected void OnSendRequest() {
    m_IsSendRequest = true;
  }

  public void Execute() {
    completed = false;
    m_IsSendRequest = false;
    response = "";
    numTries++;
    ExecuteInternal();
  }

  public bool Succeeded() {
    bool successCode = statusCode == System.Net.HttpStatusCode.OK
                       || statusCode == System.Net.HttpStatusCode.Continue
                       || statusCode == System.Net.HttpStatusCode.BadRequest;
    return completed && successCode;
  }

  public bool ShouldAutoRetry() {
    const int maxTries = 4;
    //This is a bit of a hacky patch for a 503 response while connected to certain 3G networks (namely those in Romania)
    //The patch is to retry a few times at increasing intervals
    return statusCode == System.Net.HttpStatusCode.ServiceUnavailable && numTries < maxTries;
  }

  protected abstract void ExecuteInternal();

  protected void OnUploadProgress(BestHTTP.HTTPRequest _originalRequest, long _uploaded, long _uploadLength) {
    if (_uploaded == _uploadLength) {
#if !KFF_RELEASE
      if (DebugMenuController.HasInstance && DebugMenuController.Instance.IsTrackingAPI(url)) {
        DebugMenuController.Instance.SetTrackAPIStatus("send_success");
      }
#endif
      m_IsSendRequestSuccess = true;
    }
  }

  protected TFServer.JsonResponseHandler GetResponseHandler(TFServer.JsonResponseHandler inCallback) {
    return Handler;

    void Handler(Dictionary<string, object> data, HttpStatusCode status) {
      KFFHttpHealthMonitor.UpdateStatus(status);

      ICMultiplayer.AddResponseCallback(() => {
        statusCode = status;
        completed = true;
        if (data != null) {
          response = data.TryGetValue("data", out var value) ? value.ToString() :
            data.TryGetValue("error", out var error) ? error.ToString() : "";
        }

        // TFUtils.MyLogError(new object[]{
        // 	"------GetResponseHandler", blocking, Succeeded()
        // }, go: null, color: "magenta");
        if (!blocking || Succeeded()) {
          inCallback(data, status);
        }
      });
    }
  }

  protected TFServer.JsonStringHandler GetStringHandler(TFServer.JsonStringHandler inCallback) {
    TFServer.JsonStringHandler handler = (data, status) => {
      KFFHttpHealthMonitor.UpdateStatus(status);

      ICMultiplayer.AddResponseCallback(() => {
        statusCode = status;
        response = data;
        completed = true;
        inCallback(data, status);
      });
    };

    return handler;
  }
}