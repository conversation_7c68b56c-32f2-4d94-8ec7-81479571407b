// using UnityEngine;
// using System.Collections;
// using System;
// using System.Collections.Generic;
// using System.Net;
// using System.IO;
// using System.Security.Cryptography;
// using System.Text;
// using Scopely.Analytics;
//
// public class SQServer {
//   /// <summary>
//   /// Be sure to keep this enum in sync with the server version found at /src/slots/ic_auth/models/user.py, class LoginType.
//   /// </summary>
//
//   public const string SECRET_KEY = "";
//
//   private TFServer _tfServer;
//
//   private string _nonce = "";
//
//   public SQServer(CookieContainer cookies) {
//     _tfServer = new TFServer(cookies);
//   }
// }
