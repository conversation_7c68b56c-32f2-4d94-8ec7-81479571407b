using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

[RequireComponent(typeof(KFFText))]
[RequireComponent(typeof(LayoutElement))]
[RequireComponent(typeof(KFFTextLockSize))]
[RequireComponent(typeof(ContentSizeFitter))]
public class KFFTextMaxWidthSize : MonoBehaviour {
  /* 
   * Utils class to set max width size for KFFText UI
   * Usage case to show currency icon next to the prize.
   * Require some components
   * be sure LayoutElement.preferredWidth must equal maxSize
   */

  [SerializeField]
  private int mMaxRectSize;
  [SerializeField]
  private int mOriginFontSize;
  [SerializeField]
  private float mPosXChangeOnChildActivated;
  private bool mUpdating;
  [SerializeField]
  private GameObject[] mChildren;
  private KFFText mText;
  private LayoutElement mLayout;
  private KFFTextLockSize mTextLock;
  private Vector2 mOriginPos;
  private RectTransform mRect;
  private string mPrevText = "";

  float PosXChange {
    get {
      if (mChildren == null)
        return 0f;
      foreach (var child in mChildren)
        if (child.activeInHierarchy)
          return mPosXChangeOnChildActivated;
      return 0f;
    }
  }

  void OnEnable() {
    if (!mText) //  get reference
      GetReferences();
    if (!mUpdating)
      StartCoroutine(OnTextChangeRoutine());
  }

  /// <summary>
  /// Handle the event Text Change if this instance is active in hierarchy.
  /// </summary>
  /// <param name="_maxSize">New Max size.</param>
  public void OnTextChange(int _maxSize = 0) {
    //  update new max size
    if (_maxSize != 0) {
      mMaxRectSize = _maxSize;
      if (!mText) //  get reference
        GetReferences();
      if (mLayout)
        mLayout.preferredWidth = _maxSize;
    }
    if (!mUpdating && gameObject.activeInHierarchy)
      StartCoroutine(OnTextChangeRoutine());
  }

  IEnumerator OnTextChangeRoutine() {
    // set text state
    if (mPrevText.Equals(mText.text))
      yield break;
    mPrevText = mText.text;
    mUpdating = true;
    yield return null;
    // reset layout state
    mTextLock.enabled = mLayout.enabled = false;
    mText.UpdateLockVar();
    mText.fontSize = mOriginFontSize;
    yield return null;
    // re-update layout
    var rect = mText.rectTransform;
    var size = (int)rect.GetWidth();
    mLayout.enabled = size > mMaxRectSize;
    mTextLock.enabled = !mLayout.enabled;
    mText.UpdateLockVar();
    mRect.anchoredPosition = new Vector2(mOriginPos.x + PosXChange, mOriginPos.y);
    LayoutRebuilder.ForceRebuildLayoutImmediate(rect);  //  must
    mUpdating = false;
  }

  void GetReferences() {
    mRect = transform as RectTransform;
    mOriginPos = mRect.anchoredPosition;
    mText = GetComponent<KFFText>();
    mLayout = GetComponent<LayoutElement>();
    mTextLock = GetComponent<KFFTextLockSize>();
  }

  /// <summary>
  /// Util method raises the text change event.
  /// </summary>
  /// <param name="_text">Text.</param>
  /// <param name="_maxSize">Max size.</param>
  public static void OnTextChangeUtil(Text _text, int _maxSize = 0) {
    var script = _text.GetComponent<KFFTextMaxWidthSize>();
    if (script)
      script.OnTextChange(_maxSize);
  }

  #if UNITY_EDITOR
  [ContextMenu("Set Text To 300")]
  void TestChangeTextToShortImme() {
    if (!Application.isPlaying)
      return;
    if (!mText) //  get reference
      GetReferences();
    mText.text = "300";
    OnTextChange();
  }

  [ContextMenu("Set Text To 300000000000")]
  void TestChangeTextToLongImme() {
    if (!Application.isPlaying)
      return;
    if (!mText) //  get reference
      GetReferences();
    mText.text = "300000000000";
    OnTextChange();
  }

  [ContextMenu("Test On Text Change")]
  void TestOnChangeText() {
    if (!Application.isPlaying)
      return;
    OnTextChange();
  }
  #endif
}
