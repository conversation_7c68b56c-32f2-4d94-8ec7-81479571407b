using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public enum InvasionTeamType {
  DefenseTeam,
  AttackTeam1,
  AttackTeam2
}

public class InvasionMapSlotItem_r28 : MonoBehaviour {
  [SerializeField] private Image SlotItem;
  [SerializeField] private Image SlotItemBorder;

  public void Populate(InvasionUserSlotData userSlotData = null, Sprite dotSprite = null) {
    /*Populate Defense State*/
    if (userSlotData == null) {
      SlotItem.sprite = SharedSpritesHolder_R28.Instance.GrayStickSlot;
      SlotItemBorder.gameObject.SetActive(false);
    } else {
      string userID = PlayerInfoScript.Instance.SaveData.PlayerData.ID;
      bool isOwner = userSlotData.SlotStateData.NodeStateData.ElementData.IsDefenseTeam && userID == userSlotData.UserID;
      SlotItemBorder.gameObject.SetActive(isOwner);
      SlotItem.sprite = dotSprite;
    }
  }
}