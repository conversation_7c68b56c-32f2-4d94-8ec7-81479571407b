using UnityEngine;
using System.Collections;
using System.Collections.Generic;

public class InvasionMapCylinderStackController_r28 : StackBaseController {

  #region On Map Data Change

  private void OnUpdateSingleBattleMapDataChange(string elementId) {
    if (mInvasionElementData != null && mInvasionElementData.ElementID == elementId) {
      InvasionMapCylinderLoader.Instance.GetController().RefreshAllNodesData();
      RestartInvasionUnderConstructionTimer();

      string myTeamId = "";
      if (FactionInvasionManager_R28.HasInstance
          && FactionInvasionManager_R28.Instance.userInvasion != null
          && FactionInvasionManager_R28.Instance.userInvasion.userTeam != null) {
        myTeamId = FactionInvasionManager_R28.Instance.userInvasion.userTeam.Id;
      }

      if (mInvasionElementData.IsUnderContructor && mInvasionElementData.owner_team_id != myTeamId) {
        // Show the alert popup
        var title = KFFLocalization.Get("!!INVASION_POPUP_CITY_DEFEATED_TITTLE");
        var body = string.Format(KFFLocalization.Get("!!INVASION_POPUP_CITY_DEFEATED_CONTENT"), mInvasionElementData.OwnerTeamName, mInvasionElementData.CityName);
        SimplePopupController.Instance.ShowMessage(
          title,
          body + "\n",
          () => {
            // Back To Map
            DeepLinkManager.Instance.HandleOnDeepLinkRequested(new System.Uri("whiplash://dat.Action.invasion"));
          },
          KFFLocalization.Get("!!CONTINUE"),
          SimplePopupController.PopupPriority.Normal,
          !BackButtonManager.HasInstance);
      }
    }
  }

  #endregion

  #region Start/End Battle Pubnub

  private void Pubnub_OnSlotDataChange(InvasionMapBattleSlotData_R28 invasionMapBattleData_R28) {
    this.UpdateNode(invasionMapBattleData_R28.node_id);
  }

  #endregion

  #region Set/Remove Defense

  private void Pubnub_OnDefenseSlotChange(InvasionSlotCoordinates slotCoord) {
    // Get current map node state, check if current node is done
    if (mInvasionElementData != null && slotCoord != null) {
      var nodeState = mInvasionElementData.Nodes[slotCoord.NodeIndex.ToString()];
      if (nodeState.MapNodeLayout.SlotCount == nodeState.FilledDefenseSlots) {
        // Current select slot
        int selectedNodeIndex = -1;
        InvasionMapSetDefensiveTeam_r28 setDefenseScreen = UIManager.Instance.TryGetScreen<InvasionMapSetDefensiveTeam_r28>();

        if (setDefenseScreen != null && setDefenseScreen.CurrentMapNodeState != null) {
          selectedNodeIndex = TFUtils.GetIntValue(setDefenseScreen.CurrentMapNodeState.NodeID);
        }
        if (setDefenseScreen != null && selectedNodeIndex == slotCoord.NodeIndex) {
          StartCoroutine(QueueTopScreenAndShowMessage(slotCoord.NodeIndex)); 
        }
      }
    }
    this.UpdateNode(slotCoord.NodeIndex.ToString());
  }

  #endregion

  IEnumerator QueueTopScreenAndShowMessage(int nodeIndex) {
    InputLock.Lock();
    yield return new WaitForSeconds(1.5f);
    InputLock.Unlock();

    UIManager.Instance.QueuePopToScreen<InvasionMapCylinderStackController_r28>();
    yield return new WaitUntil(() => UIManager.Instance.TopScreen() is InvasionMapCylinderStackController_r28);
    // Show Invasion MapCylinder
    //    Debug.LogError(string.Format("<color=yellow>HungTV.rm:</color> Filled Slots, Close to this screen and show message"));
    InvasionMapCylinderLoader.Instance.GetController().ShowInvasionCylinder();
    InvasionMapCylinderLoader.Instance.GetController().MoveToSelectNode(nodeIndex);
    yield return new WaitForSeconds(0.5f);

    string nodeIndexKey = nodeIndex.ToString();
    if (InvasionMapCylinderLoader.Instance.GetController().MapNodeScriptsDict != null
        && InvasionMapCylinderLoader.Instance.GetController().MapNodeScriptsDict.ContainsKey(nodeIndexKey)
        && InvasionMapCylinderLoader.Instance.GetController().MapNodeScriptsDict[nodeIndexKey].InvasionMapNodeData != null) {
      string nodeTitle = KFFLocalization.Get(InvasionMapCylinderLoader.Instance.GetController().MapNodeScriptsDict[nodeIndexKey].InvasionMapNodeData.Title);
      ToastController.ShowInformationalToast(string.Format(KFFLocalization.Get("!!INVASION_NOTICE_NODE_NUMBER_DEFENDED"), nodeTitle));
    }
  }

  private void UpdateNode(string nodeId) {
    // Prepare data
    List<string> nodeIds = new List<string>();
    nodeIds.Add(nodeId);

    // Update UI
    InvasionMapCylinderLoader.Instance.GetController().RefreshAllNodesData(nodeIds);
  }

  void OnEnable() {
//    Debug.LogError(string.Format("<color=yellow>HungTV.rm:</color> On Enable Map Cylinder"));
    ICMultiplayerEvent.onStartInvasionBattle += Pubnub_OnSlotDataChange;
    ICMultiplayerEvent.onEndInvasionBattle += Pubnub_OnSlotDataChange;
    ICMultiplayerEvent.onInvasionDoneSetDefenseSlot += Pubnub_OnDefenseSlotChange;
    ICMultiplayerEvent.onInvasionDoneRemoveDefenseSlot += Pubnub_OnDefenseSlotChange;
    ICMultiplayerEvent.onTakeOverEmptyCitySuccess += OnTakeOverEmptyCitySuccess;
    ICMultiplayerEvent.onCityTakenByAnotherTeamError += OnCityTakenByAnotherProcess;
    ICMultiplayerEvent.onUpdateSingleBattleMapData += OnUpdateSingleBattleMapDataChange;
  }

  void OnDisable() {
//    Debug.LogError(string.Format("<color=yellow>HungTV.rm:</color> On Disable Map Cylinder"));
    ICMultiplayerEvent.onStartInvasionBattle -= Pubnub_OnSlotDataChange;
    ICMultiplayerEvent.onEndInvasionBattle -= Pubnub_OnSlotDataChange;
    ICMultiplayerEvent.onInvasionDoneSetDefenseSlot -= Pubnub_OnDefenseSlotChange;
    ICMultiplayerEvent.onInvasionDoneRemoveDefenseSlot -= Pubnub_OnDefenseSlotChange;
    ICMultiplayerEvent.onTakeOverEmptyCitySuccess -= OnTakeOverEmptyCitySuccess;
    ICMultiplayerEvent.onCityTakenByAnotherTeamError -= OnCityTakenByAnotherProcess;
    ICMultiplayerEvent.onUpdateSingleBattleMapData -= OnUpdateSingleBattleMapDataChange;
  }

  private InvasionElementData_R28 mInvasionElementData;
  private Coroutine TakenTimeCountdown = null;

  public override void Setup() {
    
  }

  public override IEnumerator PushRoutine(ScreenInfo info) {
    this.type = info.Get<MenuType>("MenuType");
    mInvasionElementData = info.TryGet<InvasionElementData_R28>("InvasionElementData");

    if (mInvasionElementData != null) {
      PopulateMap();
      CheckTakenOver();

      RestartInvasionUnderConstructionTimer();
    }
    //input lock status was preventing the menu to be processed properly. 
    //Because of that, BottomMenuController.GetCurrentMenu() did not return proper value, and 
    //MapCylinderController.UnlockSequenceCheckSequence() to stack in yiedling. ignoreLock option set true.
    BottomMenuController.Instance.ShowSingleMenu(type, false, true);

    //show sidebar links
    //TODO ideally this should wait until map progress reveal finishes, if any
//    PVESidebarController.ShowSidebar(true, true);

    yield return null;
  }

  public void PopulateMap() {
    InvasionMapCylinderLoader.Instance.GetController().Populate(mInvasionElementData);
    InvasionMapCylinderLoader.Instance.GetController().ShowInvasionCylinder();

    // Control touch input. Disable touch input for cylinder map while tutorial is still going on.
    InvasionMapCylinderLoader.Instance.GetController().CylinderSpinScript.useTouchInput = true;
  }

  public override void Back() {
    if (TakenTimeCountdown != null) {
      StopCoroutine(TakenTimeCountdown);
    }

    InvasionMapCylinderLoader.Instance.GetController().CloseInvasionMap();
    base.Back();
  }

  //  void OnDestroy() {
  //    if (FactionEnvMovementController.HasInstance) {
  //      FactionEnvMovementController.Instance.ShowFaction();
  //    }
  //  }

  private void CheckTakenOver() {
    if (FactionInvasionManager_R28.Instance.curFactionInvasion.currentPhase != InvasionPhase.Battling || mInvasionElementData.IsDuringSetDefenseMode) {
      return;
    }

    if (!FactionInvasionManager_R28.HasInstance ||
      FactionInvasionManager_R28.Instance.userInvasion == null ||
      FactionInvasionManager_R28.Instance.userInvasion.userTeam == null ||
      string.IsNullOrEmpty(FactionInvasionManager_R28.Instance.userInvasion.userTeam.Id)) {
      // Data error. Team id is necessary.
      return;
    }

    // check empty city
    if (mInvasionElementData.IsDefenseTeamEmpty
        && mInvasionElementData.owner_team_id != FactionInvasionManager_R28.Instance.userInvasion.userTeam.Id
        && !mInvasionElementData.IsTakenOver) {

      if (!mInvasionElementData.isCanAttack) {
        return;
      }
      string elementId = mInvasionElementData.team_position.ToString() + ":" + mInvasionElementData.element_index.ToString();
#if ENABLE_INVASION
      TFServerOp op = Multiplayer.Multiplayer.TakeOverEmptyCity(elementId, null);
      ICMultiplayer.Instance.BlockTillMessage(ICMPMessageType.INVASION_TAKE_OVER_EMPTY, null, op);
      op.Execute();
#endif
      return;
    }
    // check city
    else if (mInvasionElementData != null && 
      mInvasionElementData.IsTakenOver && 
      PlayerInfoScript.Instance.StateData.JustTakenCity && 
      mInvasionElementData.owner_team_id_tmp == FactionInvasionManager_R28.Instance.userInvasion.userTeam.Id) {
      PlayerInfoScript.Instance.StateData.JustTakenCity = false;
      UIScreen.ScreenInfo info = new UIScreen.ScreenInfo();
      info.Add("city", mInvasionElementData);
      info.Add("displayType", InvasionTakeOverCityController_R28.TakeOverDisplayType.TakenFinalNode);
      UIManager.Instance.QueuePush(InvasionTakeOverCityController_R28.PREFAB_NAME, info);

      TakenTimeCountdownProcess();
    } 
    // check node
    else if (PlayerInfoScript.Instance.StateData.InvasionSlot != null &&
             PlayerInfoScript.Instance.StateData.InvasionSlot.NodeStateData != null &&
             PlayerInfoScript.Instance.StateData.InvasionSlot.NodeStateData.IsTakenOver &&
             PlayerInfoScript.Instance.StateData.JustTakenNode) {

      GameStateData state = PlayerInfoScript.Instance.StateData;
      state.JustTakenNode = false;

      string nodeIndex = "0";
      if (state.InvasionSlot != null &&
          state.InvasionSlot.NodeStateData != null &&
          !string.IsNullOrEmpty(state.InvasionSlot.NodeStateData.NodeID)) {
        nodeIndex = state.InvasionSlot.NodeStateData.NodeID;  
      }

      if (mInvasionElementData.Nodes.ContainsKey(nodeIndex)) {
        ToastController.ShowInformationalToast(string.Format(KFFLocalization.Get("!!INVASION_NOTICE_NODE_NUMBER_DEFEATED"), mInvasionElementData.Nodes[nodeIndex].MapNodeLayout.Title));
      }
    }
  }

  private void TakenTimeCountdownProcess() {
    Dictionary<string, int> cityTakenTime = PlayerInfoScript.Instance.StateData.CityTakenTime;
    if (cityTakenTime == null) {
      cityTakenTime = new Dictionary<string, int>();
    }

    long takenTime = mInvasionElementData.ClientTakenTime;
    long currentTime = TFUtils.ServerTimeUtc.UnixTimestamp();
    int totalSeconds = takenTime <= currentTime ? 0 : (int)(takenTime - currentTime);
    cityTakenTime[mInvasionElementData.ElementID] = totalSeconds < 0 ? 0 : totalSeconds;
    PlayerInfoScript.Instance.SaveWP();

    if (cityTakenTime.ContainsKey(mInvasionElementData.ElementID) && cityTakenTime[mInvasionElementData.ElementID] > 0 && mInvasionElementData.IsTakenOver) {
      if (TakenTimeCountdown != null) {
        StopCoroutine(TakenTimeCountdown);
      }
      TakenTimeCountdown = StartCoroutine(CR_TakenTimeCountdown(cityTakenTime[mInvasionElementData.ElementID]));  
    }
  }

  private IEnumerator CR_TakenTimeCountdown(int seconds) {
    yield return new WaitForSecondsRealtime(seconds);

    UIScreen.ScreenInfo info = new UIScreen.ScreenInfo();
    info.Add("city", mInvasionElementData);
    info.Add("displayType", InvasionTakeOverCityController_R28.TakeOverDisplayType.TakenCity);
    UIManager.Instance.QueuePush(InvasionTakeOverCityController_R28.PREFAB_NAME, info);
  }

  private void OnTakeOverEmptyCitySuccess(Dictionary<string, object> data) {
//    mInvasionElementData.UpdateData(data);
//    Debug.LogError(string.Format("<color=yellow>HungTV.rm:</color> Show Take Over Empty City Success"));
    UIScreen.ScreenInfo info = new UIScreen.ScreenInfo();
    info.Add("city", mInvasionElementData);
    info.Add("displayType", InvasionTakeOverCityController_R28.TakeOverDisplayType.TakenFinalNode);
    UIManager.Instance.QueuePush(InvasionTakeOverCityController_R28.PREFAB_NAME, info);
  }

  private void OnCityTakenByAnotherProcess() {
    UIScreen.ScreenInfo info = new UIScreen.ScreenInfo();
    info.Add("displayType", InvasionPopupController_r28.DisplayType.PlainText);
    info.Add("title", KFFLocalization.Get("!!INVASION_POPUP_CITY_IS_DOWN_TITTLE"));
    info.Add("description", KFFLocalization.Get("!!INVASION_POPUP_CITY_IS_DOWN_CONTENT"));
    UIManager.Instance.QueuePush(InvasionPopupController_r28.prefabName, info);
  }

  public override IEnumerator PopRoutine() {
    InvasionMapCylinderLoader.Instance.GetController().CloseInvasionMap();
    return base.PopRoutine();
  }

  public void OnApplicationPause(bool paused) {
    if (!paused) {
      if (mInvasionElementData != null) {
        RestartInvasionUnderConstructionTimer();
      }
    }
  }

  private void RestartInvasionUnderConstructionTimer() {
    if (mInvasionElementData.IsUnderContructor) {
//      Debug.LogError(string.Format("<color=yellow>HungTV.rm:</color> Start Underconstruction Count Timer"));
      if (checkUnderConstructionIE != null) {
        this.StopCoroutine(checkUnderConstructionIE);
      }
      checkUnderConstructionIE = this.StartCoroutine(CheckUnderConstruction());
    }
  }

  private Coroutine checkUnderConstructionIE = null;

  IEnumerator CheckUnderConstruction() {
    yield return new WaitUntil(() => {
        return mInvasionElementData.frozen_expire < TFUtils.ServerTimeUtc.UnixTimestamp();
      });
      
    if (ICMultiplayerEvent.onUpdateSingleBattleMapData != null && mInvasionElementData != null) {
//      Debug.LogError(string.Format("<color=yellow>HungTV.rm:</color> Stop Underconstruction Count Timer"));
      mInvasionElementData.UpdateAttackStatusForAllNodes();
      ICMultiplayerEvent.onUpdateSingleBattleMapData(mInvasionElementData.ElementID);
    }
  }
}
