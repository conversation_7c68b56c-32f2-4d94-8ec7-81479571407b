using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System.Linq;

/// <summary>
/// Invasion team data r28.
/// Used for battle phase
/// </summary>
public class InvasionTeamData_R28 {
  public int team_position_rotate = 0;

  //Faction Name
  public string factionName;

  //Multiple Value
  public int MultipleValue;

  //Point
  public int invasionPoint;

  //Rank
  public int invasionRank;

  public int controlledCity;

  //Team ID
  public string TeamId = "";
  public string FactionId = "";

  public Dictionary<int, InvasionElementData_R28> ListInvasionElementData_R28;

  public InvasionTeamData_R28(int _team_position_rotate) {
    team_position_rotate = _team_position_rotate;
    factionName = "";
    MultipleValue = 0;
    invasionPoint = 0;
    invasionRank = 1;
    controlledCity = 0;

    ListInvasionElementData_R28 = new Dictionary<int, InvasionElementData_R28>();

    int maxCountCity = GameConstants.MAX_CITY_EACH_TEAM;
    if (_team_position_rotate == GameConstants.TEAM_4) {
      maxCountCity = GameConstants.MAX_CITY_NEUTRAL_TEAM;
    }

    for (int i = 0; i < maxCountCity; i++) {
      InvasionElementData_R28 elementData = new InvasionElementData_R28();
      elementData.team_position = team_position_rotate;
      elementData.element_index = i;

      ListInvasionElementData_R28.Add(elementData.element_index, elementData);
    }
  }

  public InvasionTeamData_R28(Dictionary<string, object> dict) {
    factionName = TFUtils.LoadString(dict, "faction_name", "");
    invasionPoint = TFUtils.LoadInt(dict, "invasion_point", 0);
    MultipleValue = TFUtils.LoadInt(dict, "invasion_point_multiplier", 0);
    TeamId = TFUtils.LoadString(dict, "team_id", "");
    FactionId = TeamId.Split(':')[0];
    controlledCity = TFUtils.LoadInt(dict, "controlled_city", 0);
  }

  public void UpdateTeamId() {
    string temp_team_id = "";

    if (PlayerInfoScript.Instance.invasionMapData_R28.ListTeamIDRotate.ContainsKey(team_position_rotate)) {
      temp_team_id = PlayerInfoScript.Instance.invasionMapData_R28.ListTeamIDRotate[team_position_rotate];
    }

    //Set for team
    TeamId = temp_team_id;

    foreach (var data in ListInvasionElementData_R28) {
      if (string.IsNullOrEmpty(data.Value.team_id)) {
        data.Value.team_id = temp_team_id;
      }

      if (string.IsNullOrEmpty(data.Value.owner_team_id)) {
        data.Value.owner_team_id = temp_team_id;
      }
    }
  }

  public void ResetMultipleValue() {
    MultipleValue = 0;
  }

  public void ResetTransparentElementData() {
    foreach (var elementData in ListInvasionElementData_R28) {
      elementData.Value.isTransparent = false;
      elementData.Value.isCanAttack = false;
    }
  }

  private List<InvasionElementData_R28> ListElementChecked;
  private Queue<InvasionElementData_R28> QueueElementToCheck;

  public void CheckTransparentElementData() {
    ListElementChecked = new List<InvasionElementData_R28>();
    QueueElementToCheck = new Queue<InvasionElementData_R28>();

    //HQ team ID
    string teamID = ListInvasionElementData_R28[0].team_id;

    //Set to HQ
    ListInvasionElementData_R28[0].isTransparent = true;

    //Add Node HQ to queue.
    QueueElementToCheck.Enqueue(ListInvasionElementData_R28[0]);

    while (QueueElementToCheck.Count > 0) {
      //Dequeue
      InvasionElementData_R28 checkElement = QueueElementToCheck.Dequeue();
//      Debug.LogError("<color=orange>checkElement team: " + checkElement.team_position + " / " + checkElement.team_position_rotate + " / index:" + checkElement.element_index + " / ID: " + checkElement.team_id + " / " + checkElement.owner_team_id + " / CHECK </color>");

      //Add to checked list
      ListElementChecked.Add(checkElement);

      //Get near node
      int index = GameConstants.MAX_CITY_EACH_TEAM * (checkElement.team_position_rotate - 1) + checkElement.element_index_rotate;
      InvasionMapLayoutData_R28 layoutData = InvasionMapLayoutDataManager_R28.Instance.GetData(index.ToString());

      if (layoutData != null && layoutData.LinkTeam != null) {
        foreach (var linkData in layoutData.LinkTeam) {
          for (int i = 0; i < linkData.Value.Count; i++) {
            //Check each near node
            InvasionElementData_R28 nearNode = PlayerInfoScript.Instance.invasionMapData_R28.ListInvasionTeamData_R28[linkData.Key].ListInvasionElementData_R28[linkData.Value[i]];
//            Debug.LogError("<color=blue>nearNode team: " + nearNode.team_position + " / " + nearNode.team_position_rotate + " / index:" + nearNode.element_index + " / ID: " + nearNode.team_id + " / " + nearNode.owner_team_id + " / CHECK </color>");

            if (ListElementChecked.Contains(nearNode)) {
//              Debug.LogError("Return ListElementChecked.Contains(nearNode)");
              continue;
            }

            if (nearNode.owner_team_id == "" && nearNode.owner_team_id != GameConstants.NeutralTeamID) {
//              Debug.LogError("Return owner_team_id == \"\"");
              continue;
            }

            if (nearNode.owner_team_id == teamID) {
//              Debug.LogError("<color=green>nearNode team: " + nearNode.team_position + " / " + nearNode.team_position_rotate + " / index:" + nearNode.element_index + " / ID: " + nearNode.team_id + " / " + nearNode.owner_team_id + " / TRANSPARENT </color>");
              nearNode.isTransparent = true;

              //Add to queue
              QueueElementToCheck.Enqueue(nearNode);

              if (teamID == FactionInvasionManager_R28.Instance.userInvasion.userTeam.Id && !PlayerInfoScript.Instance.invasionMapData_R28.ListOwnerElementData.Contains(nearNode)) {
                PlayerInfoScript.Instance.invasionMapData_R28.ListOwnerElementData.Add(nearNode);
              }
            } else {
              //              Debug.LogError("<color=cyan> teamID: " + teamID + " / " + FactionInvasionManager_R28.Instance.userInvasion.userTeam.Id + "  </color>");
              if (teamID == FactionInvasionManager_R28.Instance.userInvasion.userTeam.Id) {
                nearNode.isCanAttack = true;
//                Debug.LogError("<color=cyan>nearNode team: " + nearNode.team_position + " / " + nearNode.team_position_rotate + " / index:" + nearNode.element_index + " / ID: " + nearNode.team_id + " / " + nearNode.owner_team_id + " / ATTACK </color>");
              }
            }
          }
        }
      }
    }
  }
}