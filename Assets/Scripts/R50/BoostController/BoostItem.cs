using System;
using System.Collections.Generic;
using UnityEngine;

[Serializable]
public class BoostItem {
  public string ID;
  public string name;
  public Boost_Duration_Type duration_type;
  public Boost_Event_Type event_type;
  public Boost_Bonus_Types bonus_type;
  public object value1;
  public string logo;
  public string description;
  public DateTime start;
  public DateTime end;
  public string groupID;
  public string boostGroup;
  public BuffData.DurationType durationType;
  public int durationNumb;
  public bool mitigating;

  //Suppresion
  public string moveType;
  public string moveName;
  public int numberTarget;

  // R24 Quan: cache boost data
  public BoostData bData;

  //R40 
  public bool isFlagBoost;

  public BuffData.BuffSource SourceOverride;
  public float scoutBoostValue;
  public float playerBoostValue;
  public string iconOvverride;
  public string descriptionOverride;
  public PsiBoostCategory psiBoostCategory { get; set; }
  public Sprite spriteIcon { get; set; }
  
  //R71 override val 2 if needed
  public string overrideVal2;
  public string TempTarget;
  public List<string> listSSIDLink = new List<string>();

  private void RegisterData(BoostData data) {
    if (data == null) {
      return;
    }
    
    this.bData = data;
    ID = data.ID;
    name = data.Name;
    value1 = ParseValData(data.BoostResultValue1);
    logo = "UI/ErasAndStables/UI_GroupLogo_Link_Gen"; //WHIP-40570 Change to load from ODL so need change from Era => Eras
    description = data.Description;
    start = data.StartTime;
    end = data.EndTime;
    groupID = data.WrestlerGroupID;
    boostGroup = data.BoostType;
    if ((!string.IsNullOrEmpty(data.durationType) && Enum.IsDefined(typeof(Boost_Bonus_Types), data.BoostResultType)) || data.BoostResultType == "Suppression") {
      if (data.BoostResultType != "Suppression") {
        durationType = (BuffData.DurationType)Enum.Parse(typeof(BuffData.DurationType), data.durationType);
        mitigating = data.mitigating;
      }
      durationNumb = data.durationNumb;
    }
    moveType = data.moveType;
    moveName = data.moveName;
    numberTarget = data.numberTarget;

    if (Enum.IsDefined(typeof(Boost_Bonus_Types), data.BoostResultType)) {
      bonus_type = (Boost_Bonus_Types)Enum.Parse(typeof(Boost_Bonus_Types), data.BoostResultType);
    }

    if (Enum.IsDefined(typeof(Boost_Event_Type), data.EventAffected)) {
      event_type = (Boost_Event_Type)Enum.Parse(typeof(Boost_Event_Type), data.EventAffected);
    }

    if (Enum.IsDefined(typeof(Boost_Duration_Type), data.DurationType)) {
      duration_type = (Boost_Duration_Type)Enum.Parse(typeof(Boost_Duration_Type), data.DurationType);
    }
  }

  private static object ParseValData(string input) {
    // check if the input is an integer
    if (Int32.TryParse(input, out Int32 i)) {
      return i;
    }

    // NOTE: GemType and GemModifier both contain the enumerator "None" but this will work fine
    // since the integral values of both enumerators are 0

    // check if the input is a GemType
    if (Enum.IsDefined((typeof(GemType)), input))
      return Enum.Parse((typeof(GemType)), input);

    // check if the input is a GemModifier
    if (Enum.IsDefined((typeof(GemModifier)), input))
      return Enum.Parse((typeof(GemModifier)), input);

    // check if the input is an AffectedPlayer
    if (Enum.IsDefined((typeof(GemModifierData.AffectedPlayer)), input))
      return Enum.Parse((typeof(GemModifierData.AffectedPlayer)), input);

    // check if the input is a SubmissionGemSelectionType
    if (Enum.IsDefined((typeof(GemSelectionType)), input))
      return Enum.Parse((typeof(GemSelectionType)), input);

    return input;
  }

  public BoostItem() {

  }

  public BoostItem(BoostData data) {
    RegisterData(data);
  }
  
  public BoostItem(Dictionary<string, object> dict) {
    Deserialize(dict);
  }

  public void SaveToDict(Dictionary<string, object> dict) {
    dict.Add("ID", ID);
    string format = @"MM\/dd\/yyyy HH:mm:ss tt";
    dict.Add("Start", start.ToString(format));
    dict.Add("End", end.ToString(format));
  }

  public void Deserialize(Dictionary<string, object> dict) {

    ID = TFUtils.LoadString(dict, "ID", "");
    // load start
    string s = TFUtils.LoadString(dict, "Start", "");
    if (s != "")
      start = DateTime.SpecifyKind(TFUtils.ParseDateTimeWithInvariantCulture(s), DateTimeKind.Utc);
    // load end
    s = TFUtils.LoadString(dict, "End", "");
    if (s != "")
      end = DateTime.SpecifyKind(TFUtils.ParseDateTimeWithInvariantCulture(s), DateTimeKind.Utc);

    BoostData data = null;

    // using the id find the json definition of our stuff
    for (int i = 0; i < BoostDataManager.Instance.GetDataCount(); i++) {
      if (BoostDataManager.Instance.GetData(i).ID == ID) {
        data = BoostDataManager.Instance.GetData(i);

        name = data.Name;
        value1 = ParseValData(data.BoostResultValue1);
        logo = "UI/ErasAndStables/UI_GroupLogo_Link_Gen"; //WHIP-40570 Change to load from ODL so need change from Era => Eras
        description = data.Description;
        groupID = data.WrestlerGroupID;

        break;
      }
    }
    if (data == null) {
      Debug.LogError("BOOST CONTROLLER - Cannot find ID " + ID);
    }
  }

  public bool IsTheSameBoost(BoostItem boost) {
    if (boost.bData != null && this.bData != null) {
      // same buff Type (ex: Gem_All_Dmg_Perc, Move_StartCharge_Abs)
      // same target (ex: Yellow, Strike, None)
      if (boost.bData.Buff == this.bData.Buff && boost.bData.BoostResultValue2 == this.bData.BoostResultValue2) {
        return true;
      }
    }
    return false;
  }
}