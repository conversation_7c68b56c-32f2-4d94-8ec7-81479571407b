using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class RoadBossPostMatchPanelLoader : Singleton<RoadBossPostMatchPanelLoader> {
  public Camera UICameraFromScene;
  private MonoBehaviour theController;
  public string prefabName;

  private void Start() {
    if (UIManager.HasInstance == true) {
      prefabName = "RoadBoss/UI_Battle_Roadboss_PostMatch_Main";
    }

    if (theController == null) {
      theController = GetComponentInChildren<RoadBossPostMatchController>();
    }

    if (theController != null) {
      var go = theController.gameObject;
      SetUpCamera(go);
    }
  }

  public T GetController<T>() where T : MonoBehaviour {
    if (null == theController) {
      GameObject prefab = KFFResourceManager.Instance.LoadResource(prefabName) as GameObject;
      var go = gameObject.InstantiateAsChild(prefab);
      SetUpCamera(go);
      theController = go.GetComponent<T>();
    }

    if (theController != null) {
      var canvas2 = theController.GetComponent<Canvas>();
      if (!canvas2.enabled)
        canvas2.enabled = true;

      RoadBossPostMatchController pmc = theController.GetComponent<RoadBossPostMatchController>();
      if (pmc != null) {
        pmc.Start();
      }
    }

    return (T)theController;
  }

  public bool HasController() {
    return theController != null;
  }

  public void DestroyController() {
    Destroy(theController.gameObject);
    theController = null;
  }

  void SetUpCamera(GameObject go) {
    if (UICameraFromScene != null) {
      var canvas = go.GetComponent<Canvas>();
      if (canvas != null)
        canvas.worldCamera = UICameraFromScene;
    }
  }
}
