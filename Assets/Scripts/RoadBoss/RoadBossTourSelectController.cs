using System.Collections;
using System.Collections.Generic;
using EnhancedUI.EnhancedScroller;
using UnityEngine;

public class RoadBossTourSelectController : UIScreen {
  public static string PREFAB_NAME = "frontEnd_roadboss_select";
  [SerializeField] ScrollerController Scroller;
  private List<RoadBossTourTileData> tourTileDatas = new List<RoadBossTourTileData>();
  private RoadBossEventDataManager roadBossEventDataManager => RoadBossEventDataManager.Instance;
  protected override void Awake() { }

  public override void Setup() { }

  public override IEnumerator PushRoutine(ScreenInfo info) {
    Populate();
    return base.PushRoutine(info);
  }

  /// <summary>
  /// Populate road boss chapter data
  /// </summary>
  void Populate() {
    if (!RoadBossEventDataManager.Instance.HasEventActive()) {
      return;
    }
    CreateListEventData();
    Scroller.SetData(tourTileDatas.ToArray());
    Scroller.Reset(false);
  }

  /// <summary>
  /// Create List Event Data
  /// </summary>
  void CreateListEventData() {
    tourTileDatas.Clear();
    List<RoadBossEventData> listEvent = roadBossEventDataManager.GetListActiveEvent();
    if (listEvent == null || listEvent.Count == 0) {
     return; 
    }
    for (int i = 0; i < listEvent.Count; i++) {
      RoadBossTourTileData roadBossData = new RoadBossTourTileData(listEvent[i]);
      roadBossData.DataPopulateCallback = PopulateTourTile;
      tourTileDatas.Add(roadBossData);
    }
  }

  /// <summary>
  /// Populate chapter tile data
  /// </summary>
  /// <param name="_cellview"></param>
  /// <param name="_data"></param>
  /// <param name="_go"></param>
  public void PopulateTourTile(EnhancedScrollerCellView _cellview, ScrollerDataBase _data, GameObject _go) {
    RoadBossTourTileData data = _data as RoadBossTourTileData;
    if (_go != null) {
      RoadBossTourTileScript tile = _go.GetComponent<RoadBossTourTileScript>();
      if (tile != null) {
        tile.Populate(this, data);
      }
    }
  }

  public override void OnFocus() {
    var listUIRoadBoss = Scroller.myScroller.GetActiveCells();
    if (listUIRoadBoss != null) {
      for (int i = 0; i < listUIRoadBoss.Length; i++) {
        var ui = listUIRoadBoss[i].GetComponent<RoadBossTourTileScript>();
        if (ui != null) {
          ui.UpdateBadge();
        }
      }
    }
    base.OnFocus();
  }
}
