using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

public class UIListItemSuperStarOnSSTabRow : UISuperStarAndGearTabRow {
  [SerializeField] List<UIItemSuperStarOnSSTab> listUIItem = new List<UIItemSuperStarOnSSTab>();
  private DataSuperStarOnSSTabRow data;
  private List<UIItemSuperStarOnSSTab> listUIActiveItem = new List<UIItemSuperStarOnSSTab>();

  public override void SetData(DataSuperStarOnSSTab _data = null) {
    base.SetData(_data);
    data = _data as DataSuperStarOnSSTabRow;
    listUIActiveItem.Clear();
    List<DataSuperStarItemOnSSTab> listSS = data.ListSS;
    int countSS = listSS.Count;
    if (countSS <= listUIItem.Count) {
      for (int i = 0; i < listUIItem.Count; i++) {
        UIItemSuperStarOnSSTab item = listUIItem[i];
        if (i < countSS) {
          item.gameObject.SetActive(true);
          var itemData = listSS[i];
          item.SetData(itemData);
          listUIActiveItem.Add(item);
        } else {
          item.gameObject.SetActive(false);
        }
      }
    }
  }

  public void PlaySuperStarHighLightInList() {
    if (listUIActiveItem.Count > 0) {
      int index = Random.Range(0, listUIActiveItem.Count);
      listUIActiveItem[index].PlayAnimHighLight();
    }
  }
}
