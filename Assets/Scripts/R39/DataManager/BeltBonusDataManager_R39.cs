using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class BeltBonusData: LoadableData, ILoadableData {
  public string ID { get; private set; }
  private string mName;
  public string Name {
    get {
      return KFFLocalization.Get(mName);
    }
  }
  public string EffectID { get; private set; }
  public string Texture { get; private set; }

  public void PopulateNewJson(DataRaw raw) { }

  public void PopulateProtobuf(DataRaw raw) { }

  public void PopulateCsv(List<string> row, Dictionary<string, int> headersDict) { 
    ID = GetCsvValue("ID", row, headersDict);
    mName = GetCsvValue("Name", row, headersDict);
    EffectID = GetCsvValue("EffectID", row, headersDict, "NONE");
    Texture = GetCsvValue("Texture", row, headersDict);
  }
  
  public void LoadDependencies() { }

  private NonBuffRuneEffectData _effectData;

  public NonBuffRuneEffectData EffectData {
    get {
      if (_effectData == null) {
        NonBuffRuneEffectData data = NonBuffRuneEffectDataManager_R39.Instance.GetData(EffectID);
        if (data != null) {
          _effectData = new NonBuffRuneEffectData(data);
          _effectData.OverideTexture(Texture);
        }
      }
      return _effectData;
    }
  }

  public void Populate(Dictionary<string, object> dict) {
    ID = TFUtils.LoadString(dict, "ID", "");
    mName = TFUtils.LoadString(dict, "Name", "");
    EffectID = TFUtils.LoadString(dict, "EffectID", "NONE");
    Texture = TFUtils.LoadString(dict, "Texture", "");
  }

  public BuffData GetBuffDataOfBeltBonus() {
    if (string.IsNullOrEmpty(EffectID) || !NonBuffRuneEffectDataManager_R39.Instance.ContainsKey(EffectID)) {
      return null;
    }

    if (EffectData != null) {
      // Check if has condition
      string conditionId = "";
      if (System.Enum.IsDefined(typeof(EffectConditionType), EffectData.condition) &&
        (EffectConditionType)System.Enum.Parse(typeof(EffectConditionType), EffectData.condition) != EffectConditionType.None) {
        conditionId += string.Format("BELT_BONUS_{0}_{1}", ID, EffectData.ID);
      }

      BuffData buff = new BuffData(EffectData, conditionId: conditionId);
      buff.NullifyBuffType = NullifyBuffType.StrapMedal;
      return buff;
    } else {
      return null;
    }
  }
}

public class BeltBonusDataManager_R39 : DataManager<BeltBonusData> {
  static BeltBonusDataManager_R39 mInstance = null;

  public static BeltBonusDataManager_R39 Instance {
    get {
      if (mInstance == null) {
        string filePath = System.IO.Path.Combine("Blueprints", "db_BeltBonuses.json");
        mInstance = new BeltBonusDataManager_R39(filePath);
      }

      if (!mInstance.IsLoaded) {
        mInstance.LoadImmediate();
      }

      return mInstance;
    }
  }

  public BeltBonusDataManager_R39(string _filePath) {
    FilePath = _filePath;
  }
}
