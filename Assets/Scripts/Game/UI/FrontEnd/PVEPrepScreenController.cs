using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Coffee.UIExtensions;
using UniRx.Async;
using UnityEngine;
using UnityEngine.UI;
using com.ootii.Messages;

public enum PrepType {
  Match_1v1,
  Match_1v1v1,
  Match_1v3,
  Match_2v2,
  Match_3v3,
  Match_2v1
}

[System.SerializableAttribute]
public class PVPLabels {
  public GameObject InfoGroup;
  public Image League;
  public Text PlayerName;
  public Text PlayerTalent;
}

public class PVEPrepScreenController : UIScreen, IMentorSlotHolderScreen_R49 {
  private QuestData m_Quest;

  public QuestData Quest {
    get {
      return m_Quest;
    }
    set {
      m_Quest = value;

      // need to re-search for the matching slot scripts next time get is called on it
      mSlotScripts = null;
    }
  }

  public const string prefabName = "frontEnd_event";
  public const string infoQuest = "Quest";
  public const string infoIsPVE = "isPVE";
  public const string infoIsFactionFeud = "isFactionFeud";
  public const string infoIsRoadBoss = "isRoadBoss";
  public const string infoPlayedTurnCount = "playedTurnCount";
  public const string infoWrestlerToAttack = "factionFeudWrestler";
  public const string infoWrestlerRoadBossToAttack = "roadBossWrestler";
  public const string infoPVPCurrentTournament = "pvpCurrentTournament";
  public const string infoRoadBossNode = "infoRoadBossNode";
  public const string infoRoadBossEventNodeData = "infoRoadBossEventNodeData";

  [Header("PVE Prep Screen")]
  public UITweenController TapToChangeTween;
  public UITweenController ShowManageWidgetTween1v1;
  public UITweenController ShowManageWidgetTween2v2Slot1;
  public UITweenController ShowManageWidgetTween2v2Slot2;

  public PrepType MatchPrepType = PrepType.Match_1v1;

  public Text StaminaCost;

  public GameObject StartButton;
  public Button StartRoadBossButton;
  public GearTextMeshProUGUI turnLimitText;
  public GameObject BattleStartVFX;

  //public GridLayoutGroup TileGrid;
  public GameObject TilePrefab;
  public GameObject RPSAdvantagePrefab;
  public GameObject RPSAdvantagePrefab_lowres;

  //public Image IconArena;
  //public Text ArenaName;
  public Text MatchName;
  public Text MatchType;
  //public Text WinCondition;

  // public Text Label_Reward;

  public GameObject BoostsPrefab;
  public LayoutGroup BoostsParent;

  public PVPLabels[] PVPPlayerInfo = new PVPLabels[2];
  public GameObject PVPRankInfoGroup;
  //public GameObject PVPBackgroundGroup;
  public Text RankToEarn;
  public Text RankToLose;
  public GameObject PVEMatchInfoGroup;

  public GameObject LinkListGroup;
  public GameObject LinkListGroup_P2;

  public Image LinkArt;
  public GameObject WarningImage;
  [SerializeField] private GameObject[] LinkArtObject;
  public Image OpponentLinkArt;
  [SerializeField] private GameObject OpponentLinkArtObject;

  [SerializeField] private GameObject LootRoot;
  [SerializeField] private GameObject FeudWrestlerSelectionRoot;

  public Text val_FeudSuperStars;

  [SerializeField] private GameObject mPrepRoot;
  [SerializeField] private GameObject mRewardTilePrefab;

  [SerializeField] private GameObject[] mLootRoots;
  [SerializeField] private GameObject[] healthLoots;
  [SerializeField] private Image healthImage;
  [SerializeField] private KFFText[] healthValueTxt;
  // [SerializeField] private GameObject mOneTimeLootRoot;
  [SerializeField] private GameObject oneTimeEarnedCheck;
  [SerializeField] private AnimationCurve mLootScaleCurve;

  public PVEPrepScreenWrestlerLayoutTile layoutTile { get; set; }
  [SerializeField] private GameObject layoutTileRoot;

  [SerializeField] private KFFText[] mTalentPlayer;
  [SerializeField] private KFFText[] mTalentPlayerBoost;
  [SerializeField] private GameObject[] mTalentPlayerBoostRoots;
  [SerializeField] private KFFText[] mTalentOpp;
  [SerializeField] private KFFText[] mTalentOppBoost;
  [SerializeField] private GameObject[] mTalentOppBoostRoots;

  [SerializeField] private GameObject LoadedBackgroundRoot;
  [SerializeField] private GameObject LoadedBackgroundRootTop;
  [SerializeField] private GameObject LoadedBackgroundRootBottom;
  // these get hidden when a loaded backgorund is used
  [SerializeField] private GameObject BGGlow;
  [SerializeField] private GameObject BGMain;
  [SerializeField] private GameObject infoParent;

  [SerializeField] private GameObject[] FlagGroup;
  [Header("Fight Card Art")] public string BlitzBackgroundPrefabName;

  private FightCard currentFightCard;
  private FactionFeudData currentFactionFeud;
  private FactionFeudManager.State lastKnownState;
  
  [Header("R58")]
  [SerializeField] private Transform autoplayNode;
  [SerializeField] private GameObject autoplayPrefab;
  [SerializeField] private HorizontalLayoutGroup horizontalLayout;
  
  [Header("Faction Feud Art")]
  public string FeudBackgroundPrefabName;

  public GameObject[] consumableParents;
  public GameObject consumablePrefab;
  public GameObject consumableEmptyPrompt;
  private ConsumableTileScript[] mConsumableTiles = new ConsumableTileScript[3];

  private List<RewardTileScript>[] mLootRewardLists = null;
  private List<WrestlerItem> mPlayerWrestlerList = null;
  private List<int> healthValues = new List<int>();
  static public int LOOT_LIST_COUNT = 3;

  public bool isPVE = true;

  public bool isFactionFeud { get; private set; }
  public bool isRoadBoss { get; private set; }
  private int playedTurnCount = 0;
  // public GameObject OTRSection;
  [SerializeField] private Sprite warningSprite;
  [SerializeField] private GameObject uniqueRuleRoot;
  [SerializeField] private GameObject[] nullifiedPerkButtons;

  private VersusTourneyItemData pvpCurrentTournamentEntry;
  private bool shownFFOppSelPopup;

  private List<WrestlerItem> mWrestlers = null;

  private int OpponentTalentScore = 0;
  private bool mMatchStart = false;
  private bool mInTransition = false;

  private int mPlayerWrestlerCount;
  private int mOppWrestlerCount;
  private WrestlerItem[] mPreviousPlayerWrestlers;

  private BoostsController mBoostController;

  private List<WrestlerItem> mWrestlerExclusiveSelects;

  private Chapter_R46 newChapterDataR46;
  // public GameObject RewardSectionR46;
  public GameObject MatchRewardSpawnPoint;
  public GameObject OTRRewardSpawnPoint;
  public KFFText MatchRewardUpToLbl;
  public KFFText OTRRewardNumberLbl;
  public GameObject OTRRewardSection;
  public GameObject MatchRewardSection;

  public bool InTransition { set { mInTransition = value; } get { return mInTransition; } }

  public int PlayerWrestlerCount { get { return mPlayerWrestlerCount; } }

  //R60
  public GameObject teamLinkBoostCount;

  private bool mQuestReqsMet = false;

  private PVEPrepScreenWrestlerSlot[] mSlotScripts;

  private GameObject mGuaranteedObjR46;
  private GameObject matchRewardObjR46;
  private Book_R46 newBookR46;
  private string bookRewardCurrency = "";
  private Dictionary<ServerActions, MessageHandler> serverActionsMessageTypes = new Dictionary<ServerActions, MessageHandler>();
  private int battleType = (int)BattleConnectionManager_R50.BattleType.Pve;
  private bool isShowingSpining;
  private bool havingLinkBonus = false;
  private bool isShowedWarning;
  private string nullifyDataID;
  private Coroutine coroutineWaitToShowTeamLinkVfx;
  private bool _isPerkNullified = false;
  [SerializeField] private GameObject objMaskPropNullify;

  public PVEPrepScreenWrestlerSlot[] slotScripts {
    get {
      if(mSlotScripts == null || mSlotScripts.Length == 0) {
        if(layoutTile == null)
          PopulateLayoutTile();

        //var layout = (!isFactionFeud && Quest != null) ? GetMatchTypeLayout(Quest.MatchType) : GetMatchTypeLayout(SpecialMatchType.Single);
        mSlotScripts = layoutTileRoot.GetComponentsInChildren<PVEPrepScreenWrestlerSlot>(true);
      }
      return mSlotScripts;
    }
  }

  private UICSSBonusPrematchController_R44 mCSSBonusPrematchController;
  private UniqueRuleButtonController uniqueRuleController;

  //Road boss
  private RoadBossData currentRoadBossData;
  private RoadBossEventNodeData _roadBossEventNodeData;

  protected override void Awake() {
    base.Awake();

    RPSAdvantagePrefab = KFFPrefabManager.Instance.FixPrefab(RPSAdvantagePrefab) as GameObject;
    RPSAdvantagePrefab_lowres = KFFPrefabManager.Instance.FixPrefab(RPSAdvantagePrefab_lowres) as GameObject;
  }

  // HungTV.r33 Miz Quest Badges
  [SerializeField] MizBadgeData[] mMizQuestBadges;
  [SerializeField] CanvasGroup[] mOldBadges;

  private void RefreshMizQuestBadgesUI() {
    if(!PlayerInfoScript.Instance.HasMizQuest()) {
      return;
    }
    foreach(var mMizQuestBadge in mMizQuestBadges) {
      mMizQuestBadge.Populate();
    }
  }

  private void OnEnable() {
    ICMultiplayerEvent.onGetNewbieMizQuestHistorySuccess += RefreshMizQuestBadgesUI;
    SetupServerListener();
  }

  private void OnDisable() {
    ICMultiplayerEvent.onGetNewbieMizQuestHistorySuccess -= RefreshMizQuestBadgesUI;
    RemoveAllListener();
  }

  private void SetupServerListener() {
    Dictionary<ServerActions, MessageHandler> messageTypes = new Dictionary<ServerActions, MessageHandler>();
    messageTypes.Add(ServerActions.Connected, OnConnected);
    messageTypes.Add(ServerActions.Error, OnError);
    messageTypes.Add(ServerActions.Close, OnClosed);
    messageTypes.Add(ServerActions.Disconnected, OnDisconnected);
    messageTypes.Add(ServerActions.ReceiveBattleData, OnReceiveBattleData);
    AddListener(messageTypes);
  }

  void OnError(IMessage rMessage) {
    if (!BattleConnectionManager_R50.Instance.IsBossBattleMode()) {
      Dictionary<string, object> responseDict = null;
      try {
        responseDict = (Dictionary<string, object>)rMessage.Data;
      } catch (Exception e) { }
      string mess = "PVEPrepError";
      if (responseDict != null && responseDict.TryGetValue(SimplePopupController.COMMON_ERROR_MESS_KEY, out var val)) {
        mess = val.ToString();
      }
      TFUtils.MyLogError(new object[] { "OnError", mess }, go: null, color: "magenta");
      mInTransition = false;
      WPAnalyticsManager.Instance.TrackBattleConnectSocketErrorEvent(mess, BattleConnectionManager_R50.Instance.GetBattleServerAddress());
      HideSpinning();
      //-73007: Error code when start battle with ss with 0 energy left 
      if (mess == "-73007") {
        SimplePopupController.Instance.ShowMessage(KFFLocalization.Get("!!ROAD_BOSS_START_BATTLE_ERROR_TITLE"), KFFLocalization.Get("!!ROAD_BOSS_START_BATTLE_ERROR_73007"), null, SimplePopupController.PopupPriority.ServerError);
      } else {
        SimplePopupController.Instance.ShowCommonErrorPopup(SimplePopupController.ClientCommonErrorCode.PVEPREPSCREENCONTROLLER_ONERROR, responseDict);
      }
    }
  }

  void OnClosed(IMessage rMessage) {
    Dictionary<string, object> responseDict = null;
    try {
      responseDict = (Dictionary<string, object>)rMessage.Data;
    } catch (Exception e) { }
    string mess = "PVEPrepClosed";
    if (responseDict != null && responseDict.TryGetValue(SimplePopupController.COMMON_ERROR_MESS_KEY, out var val)) {
      mess = val.ToString();
    }
    TFUtils.MyLogError(new object[] { "OnClosed", mess }, go: null, color: "magenta");
    mInTransition = false;
    HideSpinning();
    SimplePopupController.Instance.ShowCommonErrorPopup(SimplePopupController.ClientCommonErrorCode.PVEPREPSCREENCONTROLLER_ONCLOSED, responseDict);
  }

  private void OnConnected (com.ootii.Messages.IMessage rMessage) {
    bool success = (bool)rMessage.Data;
    TFUtils.MyLogError (new object[] {
      "BattleManager.OnConnected ",
      success
    }, go : null, color: "magenta");
    HideSpinning();
    Invoke("LoadBattle", 0.1f);
  }

  private void OnReceiveBattleData(com.ootii.Messages.IMessage rMessage) {
    //bool success = (bool)rMessage.Data;
    Dictionary<string, object> dictData = (Dictionary<string, object>)rMessage.Data;

    if (dictData != null) {
      if (dictData.ContainsKey("data")) {
        List<object> listObject = (List<object>)dictData["data"];
        List<object> listObject2 = (List<object>)listObject[0];
        int icmpMessageType = int.Parse(listObject2[0].ToString());
        List<object> listObject3 = (List<object>)listObject2[2];

        switch (icmpMessageType) {
          case (int)ICMPMessageType.START_MATCH:
            HandleBattleDataResponse(listObject3);
            break;
          case (int)ICMPMessageType.START_MATCH_R46:
            HandleStipulationBattleDataResponse(listObject3);
            break;

          default:
            break;
        }
      }
    }
  }

  private void HandleStipulationBattleDataResponse(List<object> data) {
    List<object> wrestler_list = data;

    foreach (Dictionary<string, object> wrestler_data in wrestler_list)
      PlayerInfoScript.Instance.SaveData.ResourcesData.ProcessUpdateWrestler(wrestler_data);

    string str = TFUtils.DebugListToString(wrestler_list);
    TFUtils.LogWarning("SCS: Start Match SS Payload\n" + str);
  }

  private void HandleBattleDataResponse(List<object> data) {
    string cssRewardKey_R44 = "css_rewards";
    string cssRewardKey_R48 = "css_rewards_r48";
    string rbePointsKey = "rbe_points";
    string mentorBonusKey = "mentor_bonus";
    List<object> wrestler_lst = new List<object>();
    foreach (Dictionary<string, object> dataDict in data) {
      if (dataDict.ContainsKey(cssRewardKey_R44)) {
        if (dataDict.ContainsKey(cssRewardKey_R44)) {
          PostMatchController_r16.cssRewardDataStartMatch_R44 = (List<object>)dataDict[cssRewardKey_R44];
        }

        if (dataDict.ContainsKey(rbePointsKey)) {
          PostMatchController_r16.rbeProgressDataStartMatch_R48 = dataDict[rbePointsKey];
        }

        if (dataDict.ContainsKey(cssRewardKey_R48)) {
          PostMatchController_r16.cssRewardDataStartMatch_R48 = (Dictionary<string, object>)dataDict[cssRewardKey_R48];
        }
      } else {
        PlayerInfoScript.Instance.SaveData.ResourcesData.ProcessUpdateWrestler(dataDict);
        wrestler_lst.Add(dataDict);
      }

      if (dataDict.ContainsKey(mentorBonusKey)) {
        PostMatchController_r16.CacheCSSMentorReward(dataDict[mentorBonusKey]);
      }
    }

    string str = TFUtils.DebugListToString(wrestler_lst);
    TFUtils.LogWarning("SCS: Start Match SS Payload\n" + str);
  }


  void ShowSpinning() {
    if (!isShowingSpining) {
      ICWaitIndicator.Instance.Show();
      isShowingSpining = true;
    }
  }

  void HideSpinning() {
    if (isShowingSpining) {
      ICWaitIndicator.Instance.Hide();
      isShowingSpining = false;
    }
  }

  void LoadBattle() {
    Debug.LogError($"Load battle scene: {battleType.ToString()}");
    SceneFlowManager.Instance.LoadBattleScene ();
  }

  private void OnDisconnected (com.ootii.Messages.IMessage rMessage) {
    TFUtils.MyLogError (new object[] {
      "BattleManager.OnDisconnected ",
      rMessage.Data.ToString ()
    }, go : null, color: "magenta");
    HideSpinning();
  }

  public void InitBattleConnection() {
    TFUtils.MyLogError(new object[]{
      "InitBattleConnection", null, Time.realtimeSinceStartup
    }, go: null, color: "magenta");
    ShowSpinning();
    SendEventMessage(ServerActions.Connect, battleType);
    SceneFlowManager.Instance.SetReturnLocation(SceneFlowManager.ReturnLocation.Map);
  }

  public virtual void AddListener(Dictionary<ServerActions, MessageHandler> _messageTypes) {
    serverActionsMessageTypes = _messageTypes;
    foreach (var msg in serverActionsMessageTypes) {
      MessageDispatcher.AddListener(msg.Key.ToString("d"), msg.Value, true);
    }
  }

  public virtual void RemoveAllListener() {
    foreach (var msg in serverActionsMessageTypes) {
      MessageDispatcher.RemoveListener(msg.Key.ToString("d"), msg.Value, true);
    }
  }

  public virtual void SendEventMessage(ServerActions messageType, object data, float delay = 0) {
    MessageDispatcher.SendMessage(this, messageType.ToString("d"), data, delay);
  }

  public override void Setup() {

    if(mLootRewardLists == null) {

      mLootRewardLists = new List<RewardTileScript>[LOOT_LIST_COUNT];
      for(int i = 0; i < LOOT_LIST_COUNT; i++) {

        if(mLootRewardLists[i] == null)
          mLootRewardLists[i] = new List<RewardTileScript>();
      }

    }

  }

  public override IEnumerator PushRoutine(ScreenInfo info) {
    // HungTV.r33 Check if we need to show the miz quest dia log
    //NewbieMizQuestDialogManager_R33.CheckAndShowActiveMizQuestDialog(NewbieMizDialogPlace.PreBattle);

    RememberPreviousWrestlers();

    isPVE = true;
    if(info.KeyExists(infoIsPVE) == true)
      isPVE = info.TryGet<bool>(infoIsPVE);

    isFactionFeud = false;
    if (info.KeyExists(infoIsFactionFeud) == true)
      isFactionFeud = info.TryGet<bool>(infoIsFactionFeud);
    if (info.KeyExists(infoIsRoadBoss)) {
      isRoadBoss = info.TryGet<bool>(infoIsRoadBoss);
    }
    if (info.KeyExists(infoPlayedTurnCount)) {
      playedTurnCount = info.TryGet<int>(infoPlayedTurnCount);
    }

    if(info.KeyExists(infoPVPCurrentTournament)) {
      pvpCurrentTournamentEntry = info.TryGet<VersusTourneyItemData>(infoPVPCurrentTournament);
    }

    if(this.isFactionFeud || this.isPVE) {
      PlayerInfoScript.Instance.StateData.SetPvpBattleMode(false);
    }

    PlayerInfoScript.Instance.StateData.SetFactionBossBattleMode(false);

    mMatchStart = false;

    if(isPVE) {
      var wrestlers = new List<WrestlerItem>();

      var quest = info.Get<QuestData>("Quest");
      PlayerInfoScript.Instance.SaveData.PlayerData.SetCurrentNullifyID(quest.Chapter.NullifyDataID); 
      PlayerInfoScript.Instance.SaveData.PlayerData.SetCurrentStatChangesID(quest.Chapter.StatChangesID); 

      if(quest.Chapter.IsFightCard) {
        var card = FightCardManager.Instance.GetFightCardOfChapterID(quest.Chapter.ID);
        mWrestlerExclusiveSelects = new List<WrestlerItem>(card.Wrestlers);
      }

      nullifyDataID = quest.Chapter.NullifyDataID;
      PopulatePVEPrep(quest, out wrestlers);
      StartSwitchLootPrizeCoroutines();
    } else if(isFactionFeud) {
      this.currentFactionFeud = FactionFeudManager.Instance.curFactionFeud;
      nullifyDataID = currentFactionFeud.NullifyDataID;
      PlayerInfoScript.Instance.SaveData.PlayerData.SetCurrentNullifyID(currentFactionFeud.NullifyDataID); 
      PlayerInfoScript.Instance.SaveData.PlayerData.SetCurrentStatChangesID(currentFactionFeud.StatChangesID); 
      // this.Label_Reward.text = "";

      List<WrestlerItem> wrestlers = PlayerInfoScript.Instance.SaveData.ResourcesData.Wrestlers;
      if(FactionFeudManager.HasInstance && FactionFeudManager.Instance.curAllianceMatchup != null && FactionFeudManager.Instance.curAllianceMatchup.members.Length > 0) {
        foreach(var member in FactionFeudManager.Instance.curAllianceMatchup.members) {
          if(member.ID == PlayerInfoScript.Instance.SaveData.PlayerData.ID) {
            List<WrestlerItem> newList = new List<WrestlerItem>();
            foreach(var wrestler in member.wrestlers) {
              foreach(var playerWrestler in wrestlers) {
                if(wrestler != null && playerWrestler != null && wrestler.Form.ID == playerWrestler.Form.ID)
                  newList.Add(playerWrestler);
              }
            }
            mWrestlerExclusiveSelects = newList;
          }
        }
      }

      lastKnownState = FactionFeudManager.Instance.curState;

      WrestlerItem item = info.Get<WrestlerItem>(infoWrestlerToAttack);

      PopulateFFPrep(item);

      GameObject bg = LoadedBackgroundRoot.InstantiateAsChild(UIPrefabs.GetPrefab(FeudBackgroundPrefabName));
      if(bg != null) {
        PVEBackgroundScript script = bg.GetComponent<PVEBackgroundScript>();
        if(script != null) {
          script.PopulateFeudBackground(LoadedBackgroundRootTop, LoadedBackgroundRootBottom);
        }
        BGGlow.SetActive(false);
        BGMain.SetActive(false);
      }

      PopulateUniqueRuleIcon(2);
    } else if (isRoadBoss) {
      WrestlerItem roadBossOpponentWrestler = info.Get<WrestlerItem>(infoWrestlerRoadBossToAttack);
      RoadBossData roadBossData = info.Get<RoadBossData>(infoRoadBossNode);
      _roadBossEventNodeData = info.Get<RoadBossEventNodeData>(infoRoadBossEventNodeData);
      currentRoadBossData = roadBossData;
      PopulateRoadBossPrep(roadBossOpponentWrestler);
    } else { // PVP
      //R36 TODO Get specific active tournament
      if (pvpCurrentTournamentEntry == null) {
        pvpCurrentTournamentEntry = PVPHubController.Instance.CurrentTournament;
        PlayerInfoScript.Instance.SaveData.PlayerData.SetCurrentNullifyID(pvpCurrentTournamentEntry.NullifyDataID);
        PlayerInfoScript.Instance.SaveData.PlayerData.SetCurrentStatChangesID(pvpCurrentTournamentEntry.StatChangesID);
      }

      nullifyDataID = pvpCurrentTournamentEntry.NullifyDataID;
      var selectedOpponent = PVPHubController.Instance.SelectedOpponent;
      PopulatePVPPrep(pvpCurrentTournamentEntry.Quest, selectedOpponent);

      PVPHubController.Instance.SelectedOpponent = null;
    }

    //    this.ShowPrepScreenTween.PlayWithCallback(DelayHideMapCam);

    // if(TapToChangeTween != null)
    //   TapToChangeTween.Play();

    //Boost
    /*public GameObject BoostsPrefab;
  public LayoutGroup BoostsParent;*/
    if(BoostsPrefab && BoostsParent) {
      //TODO: populate

      var newGO = BoostsParent.gameObject.InstantiateAsChild(BoostsPrefab);
      mBoostController = newGO.GetComponent<BoostsController>();
      if(mBoostController) {
        mBoostController.PopulateTest(false);
      }
    }

    WPAnalyticsManager.Instance.TriggerInterstitialForLeanPlum(WPAnalyticsManager.LPTRIGGER_PREPSCREEN);

    PlayerInfoScript.Instance.SaveData.ResourcesData.onWrestlerUpdated += OnWrestlerUpdated;

    ICMultiplayer.Instance.onEvoWrestler += OnWrestlerUpdated;

    // HungTV.r33 Newbie Miz Quest Badge
    RefreshMizQuestBadgesUI();
    if (isPVE)
    {
      UpdateCSSBonus(true, Quest);
    } else if (isFactionFeud)
    {
      UpdateCSSBonus(true, currentFactionFeud);
    }

    UpdateUIMentorSlot();
    SetupAutoplayUI();
    UpdateCSSBonus(false);
    UpdatePerkButtons();
    SetActiveMaskPropNullify();
    return base.PushRoutine(info);
  }

  private void DelayHideMapCam() {
    if (MapCylinderController.HasInstance) {
      MapCylinderController.Instance.MapCam.enabled = false;
    }
  }

  public override void PostPush() {
    //    TutorialController.Instance.StartTutorial("Backstage2_Partner");
    //TutorialController.Instance.StartTutorialIfPrevStateComplete("Backstage1_stadium");

    DelayHideMapCam();

    base.PostPush();
  }

  public override IEnumerator PopRoutine() {
    // this may have to be called elsewhere if other screens 
    // need to have old wrestler selections updated before this screen is popped
    RestorePreviousWrestlers();

    StopSwitchLootPrizeCoroutines();

    if(mMatchStart == false) {
      // Reset map selected status.
      MapCylinderController.Instance.QuestSelected = false;
    }

    PlayerInfoScript.Instance.StateData.SetFactionFeudBattleMode(false);

    PlayerInfoScript.Instance.SaveData.ResourcesData.onWrestlerUpdated -= OnWrestlerUpdated;

    ICMultiplayer.Instance.onEvoWrestler -= OnWrestlerUpdated;

    return base.PopRoutine();
  }

  public override void OnFocus() {
    //StartSwitchLootPrizeCoroutines();


    UpdateOpponentBySelectedGender();

    mInTransition = false;

    // Talent score update.
    List<WrestlerItem> ws = GetWrestlerListForPrep();
    OpponentTalentScore = SetTalentScore(ws);
    if (isPVE == true || isRoadBoss) {
      PopulatePrepPanel(ws);
      DisplayEquippedConsumables();
    }

    base.OnFocus();

    this.PreventTweens = false;

    if(currentFactionFeud != null)
      RedrawFactionFeudData();
    UpdatePerkButtons();
    UpdateCSSBonus(false);
    UpdateUIMentorSlot();
  }

  public static void GetWrestlerCount(QuestData quest, out int Player, out int Opponent) {
    Player = Opponent = 1;
    if (PlayerInfoScript.Instance.StateData.IsPVP == true) {
      Player = Opponent = 2;
    } else if (PlayerInfoScript.Instance.StateData.IsFactionBoss) {
      //HungTN - WHIP-34940
      Player = Opponent = 1;
    } else if (PlayerInfoScript.Instance.StateData.IsRoadBoss) {
      Player = 2;
      Opponent = 1;
    } else if (quest != null) {
      switch (quest.MatchType) {
        case SpecialMatchType.TagTeam:
          Player = Opponent = 2;
          break;
        case SpecialMatchType.TripleThreat:
          Player = Opponent = 2;
          break;
        case SpecialMatchType.FatalFourWay:
          Player = Opponent = 3;
          break;
        case SpecialMatchType.SixMenTagMatch:
          Player = Opponent = 3;
          break;
        case SpecialMatchType.RoadBoss:
          Player = 2;
          Opponent = 1;
          break;
        default:
          break;
      }
    }
  }

  public static string GetRequirementText(string stableOrWrestlerID) {
    if(string.IsNullOrEmpty(stableOrWrestlerID) == true)
      return "";

    var stable = EraAndStableDataManager.Instance.GetData(stableOrWrestlerID);
    var wrestler = WrestlerDataManager.Instance.GetData(stableOrWrestlerID);
    var ssgroup = SSGroupDataManager.Instance.GetData(stableOrWrestlerID);
    int trainer_count = -1;

    trainer_count = TrainerDataManager.GetRequiredTrainerCountForMatch(stableOrWrestlerID);

    if(stable != null) {
      return KFFLocalization.Get(stable.Name);
    } else if(wrestler != null) {
      return KFFLocalization.Get(wrestler.Name);
    } else if(trainer_count > 0) {
      string str = KFFLocalization.Get("!!TRAINER_REQUIRED_IN_QUESTMAP");
      str = str.Replace("<count>", trainer_count.ToString());
      return str;
    }
    return "";
  }

  void GenerateWrestlerCount(QuestData quest) {
    PVEPrepScreenController.GetWrestlerCount(quest, out mPlayerWrestlerCount, out mOppWrestlerCount);
  }

  //TODO-pvp : new spec
  public void PopulatePVPPrep(QuestData quest, WPOpponentData selectedOpponent) {
    if(selectedOpponent == null)
      selectedOpponent = PVPHubController.Instance.SelectedOpponent;
    if(selectedOpponent == null)
      selectedOpponent = PVPHubController.Instance.RevengeOpponent;

    //R36 Get specific active tournament
    var activeTournament = pvpCurrentTournamentEntry;
    PlayerInfoScript.Instance.StateData.SetActiveQuest(activeTournament.Quest, activeTournament.Quest.Chapter);
    PlayerInfoScript.Instance.StateData.SetPvpBattleMode(true, true);
    //set the offensiveSS to the StateData
    for(int i = 0; i < PVPHubController.Instance.CurrentTournamentPlayerData.offensiveSS.Count; i++) {
      PlayerInfoScript.Instance.StateData.PlayerWrestlers[i] = PVPHubController.Instance.CurrentTournamentPlayerData.offensiveSS[i];
    }

    FrontEndWrestlerController.Instance.UnloadWrestlerModel(1);

    PlayerInfoScript.Instance.StateData.PVPOpponent = selectedOpponent;
    PlayerInfoScript.Instance.StateData.OpponentWrestlers.Initialize();
    if(quest != null) {
      GenerateWrestlerCount(quest);

      for(int i = 0; i < QuestData.MAX_WRESTLER_PER_TEAM; i++) {
        if(i < mOppWrestlerCount && selectedOpponent.wrestlerData.Length > i) {
          if(!isFactionFeud)
            PlayerInfoScript.Instance.StateData.OpponentWrestlers[i] = new WrestlerItem(selectedOpponent.wrestlerData[i], MiscParams.Instance.PVPAILevel);
        } else
          PlayerInfoScript.Instance.StateData.OpponentWrestlers[i] = null;

      }
      Quest = quest;

      OnMatchCommit(true, null);
    }

    MatchName.text = KFFLocalization.Get(quest.LevelName);

    UpdateRPSFX();
  }

  public void UpdateOpponentBySelectedGender() {
    if(this.isPVE) {
      if(Quest.MatchType == SpecialMatchType.Single) {
        DisplayWrestlerSlots(PlayerInfoScript.Instance.StateData.OpponentWrestler, 1, true);
      }
    }

  }

  public void PopulatePVEPrep(QuestData quest, out List<WrestlerItem> wrestlers) {
    PlayerInfoScript.Instance.StateData.OpponentWrestlers = new WrestlerItem[QuestData.MAX_WRESTLER_PER_TEAM];
    PlayerInfoScript.Instance.StateData.OpponentWrestlers.Initialize();

    PlayerInfoScript.Instance.StateData.SetFactionFeudBattleMode(false);
    int diff = PlayerInfoScript.Instance.SaveData.PlayerData.GetGameDifficulty();
    if(quest != null && quest != Quest) {
      if(!quest.Chapter.IsFightCard) {
        List<double> listHPPercentage = new List<double>() {1, 1};
        if (quest.Chapter.IsTicketedEventR46) {          
          ChapterProgress_R46 chapterProgress = PVEUtils_R46.GetChapterProgressById(PlayerInfoScript.Instance.SaveData.PVEManager_R46.currentChapter.ChapterID, diff);
          if (chapterProgress != null) {
            QuestProgress_R46 newQuestProgress = chapterProgress.GetQuestProgressById(quest.ID);
            if (newQuestProgress != null) {
              listHPPercentage = newQuestProgress.hpPercentage;
            }
          }
        }
        //TODO For now keeping the single opponent. later delete
        double hpPercentage = 1; 
        var opponentWrestler = BuildOpponentWrestler(quest.SetLoadout, listHPPercentage[0]);
        PlayerInfoScript.Instance.StateData.OpponentWrestler = opponentWrestler;
        for(int i = 0; i < quest.SetLoadouts.Count; i++) {
          if(quest.SetLoadouts[i] != null) {
            if (i < listHPPercentage.Count) {
              hpPercentage = listHPPercentage[i];
            }
            var op = BuildOpponentWrestler(quest.SetLoadouts[i], hpPercentage);
            PlayerInfoScript.Instance.StateData.OpponentWrestlers[i] = op;
          }
        }

        PlayerInfoScript.Instance.StateData.SetFightCardBattleMode(false);
      } else {
        currentFightCard = FightCardManager.Instance.GetFightCardOfChapterID(quest.Chapter.ID);
        MapNodeTemplateData mapTemplateData = null;
        mapTemplateData = ChapterManager.Instance.GetChapterMapTemplateData(quest.Chapter.ID);

        if(currentFightCard.Opponents.ContainsKey(quest.ID)) {
          wrestlers = currentFightCard.Opponents[quest.ID];

          GameObject bg = LoadedBackgroundRoot.InstantiateAsChild(UIPrefabs.GetPrefab(BlitzBackgroundPrefabName));
          if(bg != null) {
            PVEBackgroundScript script = bg.GetComponent<PVEBackgroundScript>();
            if(script != null) {
              script.PopulateBitzBackground(LoadedBackgroundRootTop, LoadedBackgroundRootBottom, currentFightCard, mapTemplateData);
            }
            BGGlow.SetActive(false);
            BGMain.SetActive(false);
          }

        } else {
          //          Debug.LogError("SCS: INVALID QUEST FOR FIGHT CARD");
        }

        PlayerInfoScript.Instance.StateData = currentFightCard.BuildGameStateData(quest);
      }

      Quest = quest;
    }

    GenerateWrestlerCount(quest);

    wrestlers = GetWrestlerListForPrep();

    PopulatePrepPanel(wrestlers);

    if(MatchName != null)
      MatchName.text = KFFLocalization.Get(Quest.LevelName);

    if (quest != null && quest.Chapter.IsTicketedEventR46) {
      newChapterDataR46 = PVEUtils_R46.GetChapterById(quest.Chapter.ID, (ChapterType)diff);
    }

    // Create Loot Item Display.
    if (quest.Chapter.IsTicketedEventR46 && (newChapterDataR46 != null && newChapterDataR46.IsHealthReward)) {
      healthValues = newChapterDataR46.HealthRewards;
      for(int i = 0; i < QuestData.MAX_LOOT_DROP; i++) {
        if (mLootRoots[i] != null) {
          mLootRoots[i].SetActive(false);
        }
        
        if (healthLoots[i] != null) {
          healthLoots[i].SetActive(true);
        }
        
        if (healthValueTxt[i] != null) {
          healthValueTxt[i].text = healthValues[i] + "%";
        }
      }
    } else {
      for(int i = 0; i < QuestData.MAX_LOOT_DROP; i++) {
        if (healthLoots[i] != null) {
          healthLoots[i].SetActive(false);
        }
        if(i >= Quest.WeightsTables.Count)
          break;

        mLootRewardLists[i].Clear();
        mLootRoots[i].transform.DestroyAllChildren();

        GachaWeightTable table = Quest.WeightsTables[i];
        if(table == null)
          continue;

        RewardEntry[] rewards = table.TopRewards;

        foreach(RewardEntry r in rewards) {

          GameObject go = mLootRoots[i].InstantiateAsChild(mRewardTilePrefab);
          RewardTileScript tile = go.GetComponent<RewardTileScript>();
          if(tile != null) {
            tile.TileMode = RewardTileMode.PrepDisplay;
            tile.Populate(r);
            mLootRewardLists[i].Add(tile);
          }
        }
      }
    }

    //One-time reward display
    FightCard fCard = FightCardManager.Instance.GetFightCardOfChapterID(Quest.Chapter.ID);
    if(fCard != null) {
      FightCardRewardsData rewardItem = fCard.GetRewardTier();
      int rewardIndex = 0;
      for(int i = 0; i < fCard.Chapter.Quests.Count; i++)
        if(fCard.Chapter.Quests[i] == Quest)
          rewardIndex = i;

      if(rewardItem.Items.Length > rewardIndex) {
        RewardEntry rewardAsEntry = new RewardEntry(rewardItem.Items[rewardIndex].stringID);
        CurrencyTileScript.TileMode currency_tile_mode = CurrencyTileScript.TileMode.WithoutAmount;
        mGuaranteedObjR46 = ItemManager.Instance.PopulateItemPanelPreMatch(OTRRewardSpawnPoint, rewardAsEntry, PosterTileMode.PostMatch, currency_tile_mode);
        OTRRewardNumberLbl.text = "x" + rewardAsEntry.Amount;
        
        //Set position
        if (rewardAsEntry != null) {
          SetRewardAnchorPosition(rewardAsEntry.ItemRewarded.ItemType);
        }
        
        // ItemManager.Instance.PopulateItemPanel(mOneTimeLootRoot, rewardAsEntry, PosterTileMode.PostMatch, currency_tile_mode, showInvPopup: true);
      }

      // check for FC checkmark      
      int star;
      PlayerInfoScript.Instance.SaveData.PVEProgressionData.GetQuestStarCount(diff, Quest, out star);
      bool cleared = (star > 0);
      oneTimeEarnedCheck.SetActive(cleared);
    } else if(Quest.GuaranteedDrop != null) {
      RewardEntry guaranteedItem;
      CurrencyTileScript.TileMode currency_tile_mode = CurrencyTileScript.TileMode.WithAmount;
      if (isPVE && Quest != null && Quest.Chapter != null && Quest.Chapter.IsTicketedEventR46) {
        if (Quest != null) {
          Quest_R46 newQuestData = PVEUtils_R46.GetQuest();
          //Dictionary <string, object> dicTest = new Dictionary<string, object>();
          //dicTest.Add("1",newQuestData);
          //Debug.LogError(dicTest.toJson());
          if (newQuestData != null && newQuestData.OTR.Count > 0) {
            // OTRSection.SetActive(true);
            for (int i = 0; i < newQuestData.OTR.Count; i++) {
              //Debug.LogError("newQuestData.OTR[i]: " + newQuestData.OTR[i]); 
              guaranteedItem = new RewardEntry(newQuestData.OTR[i]);
              currency_tile_mode = CurrencyTileScript.TileMode.WithoutAmount;
              mGuaranteedObjR46 = ItemManager.Instance.PopulateItemPanelPreMatch(OTRRewardSpawnPoint, guaranteedItem, PosterTileMode.PostMatch, currency_tile_mode, showInvPopup:true);
              OTRRewardNumberLbl.text = "x" + guaranteedItem.Amount;
              
              //Set position
              //Set position
              if (guaranteedItem != null) {
                SetRewardAnchorPosition(guaranteedItem.ItemRewarded.ItemType);
              }
              // ItemManager.Instance.PopulateItemPanel(mOneTimeLootRoot, guaranteedItem, PosterTileMode.PostMatch, currency_tile_mode, showInvPopup: true);
            }
          } else {
            // OTRSection.SetActive(false);
          }          
        }
      } else {
        guaranteedItem = new RewardEntry(Quest.GuaranteedDrop);  
        currency_tile_mode = CurrencyTileScript.TileMode.WithoutAmount;
        mGuaranteedObjR46 = ItemManager.Instance.PopulateItemPanelPreMatch(OTRRewardSpawnPoint, guaranteedItem, PosterTileMode.PostMatch, currency_tile_mode, showInvPopup:true);
        OTRRewardNumberLbl.text = "x" + guaranteedItem.Amount;
        
        //Set position
        if (guaranteedItem != null && guaranteedItem.ItemRewarded != null) {
          SetRewardAnchorPosition(guaranteedItem.ItemRewarded.ItemType);
        }
       
        // ItemManager.Instance.PopulateItemPanel(mOneTimeLootRoot, guaranteedItem, PosterTileMode.PostMatch, currency_tile_mode, showInvPopup: true);

        int star;
        PlayerInfoScript.Instance.SaveData.PVEProgressionData.GetQuestStarCount(diff, Quest, out star);
        bool cleared = (star > 0);
        oneTimeEarnedCheck.SetActive(cleared);
      }      
    }

    //Check to display reward section
    if (isPVE && quest != null && quest.Chapter != null && quest.Chapter.IsTicketedEventR46) {
      // Label_Reward.gameObject.SetActive(false);
      // RewardSectionR46.SetActive(true);
      PopulateRewardR46();
    } else {
      // Label_Reward.gameObject.SetActive(true);
      // RewardSectionR46.SetActive(false);
      HideMatchRewardR46();
    }

    UpdateRPSFX();
    DisplayEquippedConsumables();
    //WPAnalyticsManager.Instance.TrackPrematchScreenInfo(GetPlayerSlots().ToArray(), GetOpponentSlots().ToArray());
  }

  private void SetRewardAnchorPosition(InventoryItemType type) {
    //Set position
    RectTransform rectTransform = OTRRewardSpawnPoint.GetComponent<RectTransform>();
    if (rectTransform == null) {
      Debug.LogError("SetRewardAnchorPosition Null");
      return;
    }

    switch (type) {
      case InventoryItemType.Poster:
      case InventoryItemType.Shard:
        rectTransform.anchoredPosition = Vector2.zero;
        break;
      default:
        rectTransform.anchoredPosition = new Vector2(0f, -20f);
        break;
    }
  }

  private void PopulateRewardR46() {
    if (mGuaranteedObjR46 != null) {
      Destroy(mGuaranteedObjR46);
    }

    if (matchRewardObjR46 != null) {
      Destroy(matchRewardObjR46);
    }
    
    int diff = PlayerInfoScript.Instance.SaveData.PlayerData.GetGameDifficulty();
    if (Quest != null) {
      newChapterDataR46 = PVEUtils_R46.GetChapterById(Quest.Chapter.ID, (ChapterType)diff);
      if (newChapterDataR46 != null) {
        //Populate 1 time reward   
        Quest_R46 newQuestData = newChapterDataR46.GetQuestById(Quest.ID);
        if (newQuestData != null) {
          RewardEntry guaranteedItem;
          CurrencyTileScript.TileMode currency_tile_mode;
          if (newQuestData.OTR != null && newQuestData.OTR.Count > 0) {
            for (int i = 0; i < newQuestData.OTR.Count; i++) {
              guaranteedItem = new RewardEntry(newQuestData.OTR[i]);
              currency_tile_mode = CurrencyTileScript.TileMode.WithoutAmount;
              mGuaranteedObjR46 = ItemManager.Instance.PopulateItemPanel(OTRRewardSpawnPoint, guaranteedItem, PosterTileMode.PostMatch, currency_tile_mode);
              OTRRewardNumberLbl.text = "x" + guaranteedItem.Amount;
            }                 
          } else {
            HideOneTimeRewardR46();
          }
        }

        //Populate match reward   
        if (newChapterDataR46.ActiveScoring && newChapterDataR46.GetActiveBattleScoreDataList().Count > 0) {
          Quest_R46 newQData = newChapterDataR46.GetQuestById(Quest.ID);

          if (newQData != null) {   
            newBookR46 = PVEUtils_R46.GetBook();          
            if (newBookR46 != null) {
              bookRewardCurrency = newBookR46.RewardCurrency;            
              if (!string.IsNullOrEmpty(bookRewardCurrency) && newQData.MaxRewardWin > 0) {
                RewardEntry matchRewardItem = new RewardEntry(bookRewardCurrency, newQData.MaxRewardWin);
                CurrencyTileScript.TileMode  currency_tile_mode = CurrencyTileScript.TileMode.WithoutAmount;
                matchRewardObjR46 = ItemManager.Instance.PopulateItemPanel(MatchRewardSpawnPoint, matchRewardItem, PosterTileMode.PostMatch, currency_tile_mode);
                MatchRewardUpToLbl.text = string.Format(KFFLocalization.Get("!!R46_UP_TO"), newQData.MaxRewardWin);
              } else {
                HideMatchRewardR46();
              }
            }
          }
        } else {
          HideMatchRewardR46();
        }        
      }
    }

    if (mGuaranteedObjR46 == null) {
      HideOneTimeRewardR46();
    }

    if (matchRewardObjR46 == null) {
      HideMatchRewardR46();
    }

    // if (mGuaranteedObjR46 != null && matchRewardObjR46 != null) {
    //   RewardSectionR46.transform.localScale = new Vector3(0.8f, 0.8f, 0.8f);
    // } else {
    //   RewardSectionR46.transform.localScale = new Vector3(1f, 1f, 1f);
    // }
  }

  private void HideOneTimeRewardR46() {
    OTRRewardSection.SetActive(false);
  }

  private void HideMatchRewardR46() {
    MatchRewardSection.SetActive(false);
  }

  public void ShowFactionFeudOppSelection() {
    this.PreventTweens = true;

    // get claimed opponent
    string memberID = FactionFeudManager.Instance.curOpponentMatchup.GetMemberIDClaimedByMemberID(PlayerInfoScript.Instance.SaveData.PlayerData.ID);

    if(string.IsNullOrEmpty(memberID)) {
      var screen = UIManager.Instance.GetScreen<FactionFeudOpponentSelectionController>();
      if(screen != null) {
        screen.onClickNext();
      }
    } else {
      var inf = new UIScreen.ScreenInfo();
      inf.Add(FactionFeudOpponentSelectionController.infoFactionFeudMatchupData, FactionFeudManager.Instance.curOpponentMatchup);
      inf.Add(FactionFeudOpponentSelectionController.infoMemberID, memberID);
      inf.Add(FactionFeudOpponentSelectionController.infoMode, FactionFeudOpponentSelectionController.DisplayMode.WrestlerSelection);

      UIManager.Instance.QueuePush(FactionFeudOpponentSelectionController.prefabName, inf);
    }
  }

  public void PopulateFFPrep(WrestlerItem item) {
    PlayerInfoScript.Instance.StateData.OpponentWrestlers = new WrestlerItem[QuestData.MAX_WRESTLER_PER_TEAM];
    PlayerInfoScript.Instance.StateData.OpponentWrestlers.Initialize();

    PlayerInfoScript.Instance.StateData.PlayerWrestlers = new WrestlerItem[QuestData.MAX_WRESTLER_PER_TEAM];

    PlayerInfoScript.Instance.StateData.SetFactionFeudBattleMode(true, true);

    //06262017 EO : There was no CurrentActiveQuest set. it's temporary hard coding the Q_NXT_001.
    //When tool support the specific quest ID, that should be assigend here.
    PlayerInfoScript.Instance.StateData.SetActiveQuest(QuestDataManager.Instance.GetData("Q_NXT_001"), null);

    for(int ii = 0; ii < PlayerInfoScript.Instance.StateData.OpponentWrestlers.Length; ii++)
      PlayerInfoScript.Instance.StateData.OpponentWrestlers[ii] = null;

    if(item != null) {
      item = BreakFFWrestlerRef(item);
      PlayerInfoScript.Instance.StateData.OpponentWrestlers[0] = item;
      PlayerInfoScript.Instance.StateData.OpponentWrestler = item;
    } else {
      if(PlayerInfoScript.Instance.StateData.OpponentWrestler != null && PlayerInfoScript.Instance.StateData.OpponentWrestler.InFactionFeud)
        PlayerInfoScript.Instance.StateData.OpponentWrestlers[0] = BreakFFWrestlerRef(PlayerInfoScript.Instance.StateData.OpponentWrestler);
    }

    bool showOppSelPopup = false;
    if(PlayerInfoScript.Instance.StateData.OpponentWrestler == null)
      showOppSelPopup = true;

    if(PlayerInfoScript.Instance.StateData.OpponentWrestlers.Length < 1)
      showOppSelPopup = true;
    else {
      if(PlayerInfoScript.Instance.StateData.OpponentWrestlers[0] == null)
        showOppSelPopup = true;
      else {
        if(PlayerInfoScript.Instance.StateData.OpponentWrestlers[0].CurrentHP < 1 || PlayerInfoScript.Instance.StateData.OpponentWrestlers[0].isDefeatedFF()) {
          showOppSelPopup = true;
          PlayerInfoScript.Instance.StateData.OpponentWrestlers[0] = null;
        }
      }
    }

    GenerateWrestlerCount(null);

    List<WrestlerItem> wrestlers = GetWrestlerListForPrep();
    PopulatePrepPanel(wrestlers);

    // get claimed opponent
    string memberID = FactionFeudManager.Instance.curOpponentMatchup.GetMemberIDClaimedByMemberID(PlayerInfoScript.Instance.SaveData.PlayerData.ID);
    FactionFeudMember oppMember = FactionFeudManager.Instance.curOpponentMatchup.GetMemberByID(memberID);

    if(oppMember != null) {
      if(MatchName != null)
        MatchName.text = KFFLocalization.Get(PlayerInfoScript.Instance.SaveData.PlayerData.PlayerName + " " + "!!BUTTON_PVP" + " " + oppMember.name);

      // superstar count
      int numUninjured = oppMember.GetUninjuredWrestlerCount();
      int numTotal = oppMember.GetTotalWrestlerCount();

      if(val_FeudSuperStars != null)
        val_FeudSuperStars.text = KFFLocalization.Get("!!SUPERSTARS") + " " + numUninjured + "/" + numTotal;
    }

    if(LootRoot != null)
      LootRoot.SetActive(false);
    if(FeudWrestlerSelectionRoot != null)
      FeudWrestlerSelectionRoot.SetActive(true);

    UpdateRPSFX();
    DisplayEquippedConsumables();

    UpdateStartButtonStatus();

    if(showOppSelPopup && !shownFFOppSelPopup) { // need to select wrestler
      shownFFOppSelPopup = true;
      ShowFactionFeudOppSelection();
    } else if(showOppSelPopup && StartButton != null) {
      StartButton.GetComponent<Button>().interactable = false;
    }

    RefreshFFWrestler();

    if(!CanGoIntoFight() && StartButton != null)
      StartButton.GetComponent<Button>().interactable = false;
  }

  public void PopulateRoadBossPrep(WrestlerItem item) {
    QuestData quest = QuestDataManager.Instance.GetDataWithDifficulty("RoadBoss_2v1", 0);
    PlayerInfoScript.Instance.StateData.SetActiveQuest(quest, quest.Chapter);
    
    PlayerInfoScript.Instance.StateData.OpponentWrestlers = new WrestlerItem[QuestData.MAX_WRESTLER_PER_TEAM];
    PlayerInfoScript.Instance.StateData.OpponentWrestlers.Initialize();

    PlayerInfoScript.Instance.StateData.OpponentWrestlers[0] = item;
    PlayerInfoScript.Instance.StateData.OpponentWrestler = item;

    GenerateWrestlerCount(null);

    List<WrestlerItem> wrestlers = GetWrestlerListForPrep();
    PopulatePrepPanel(wrestlers);
  }

  public void RefreshFFWrestler() {
    WrestlerItem currentWrestler = PlayerInfoScript.Instance.StateData.OpponentWrestlers[0];

    if(currentWrestler != null) {

      // check against currently claimed opponent's list
      string memberID = FactionFeudManager.Instance.curOpponentMatchup.GetMemberIDClaimedByMemberID(PlayerInfoScript.Instance.SaveData.PlayerData.ID);
      FactionFeudMember oppMember = FactionFeudManager.Instance.curOpponentMatchup.GetMemberByID(memberID);
      if(oppMember != null) {
        foreach(var wrestler in oppMember.wrestlers) {
          if(wrestler != null) {
            if(wrestler.Form.ID == currentWrestler.Form.ID) {
              if(wrestler.CurrentHP != currentWrestler.CurrentHP) { // this WOULD work, but something is forcing the HP of the selected wrestler to be full now... even when defeated

                // create fresh copy of wrestler
                WrestlerItem newWrestler = BreakFFWrestlerRef(wrestler);

                // apply to slots
                PlayerInfoScript.Instance.StateData.OpponentWrestlers[0] = newWrestler;
                PlayerInfoScript.Instance.StateData.OpponentWrestler = newWrestler;

                // refresh HUD
                PopulateFFPrep(newWrestler);
                return;
              }
            }
          }
        }
      }
    }
  }

  public WrestlerItem BreakFFWrestlerRef(WrestlerItem refWrestler) {

    // get claimed opponent
    if(FactionFeudManager.Instance.curOpponentMatchup != null) {
      string memberID = FactionFeudManager.Instance.curOpponentMatchup.GetMemberIDClaimedByMemberID(PlayerInfoScript.Instance.SaveData.PlayerData.ID);
      FactionFeudMember oppMember = FactionFeudManager.Instance.curOpponentMatchup.GetMemberByID(memberID);

      if(oppMember != null) {
        foreach(var w in oppMember.wrestlers) {
          if(w == null)
            continue;

          if(w.Form.ID == refWrestler.Form.ID)
            return w;
        }
      }
    }

    WrestlerItem newWrestler = new WrestlerItem(WrestlerDataManager.Instance.GetData(refWrestler.Form.ID));
    newWrestler.SetCurrentHP(refWrestler.CurrentHP);
    newWrestler.InFactionFeud = true;
    newWrestler.IsSkinLocked = true;
    return newWrestler;
  }

  List<Coroutine> mLootPrizeCoroutines = new List<Coroutine>();

  IEnumerator SwitchLootPrize(List<RewardTileScript> _list, float _wait = 0f) {
    if (_list == null || _list.Count == 0) {
      yield break;
    }
    int count = 0;
    float FadeDuration = 0.2f;
    float StayDuration = 4f;
    List<CanvasGroup> _canvasGroups = new List<CanvasGroup>();
    CanvasGroup canvas = null;
    CanvasGroup canvas_next = null;
    Transform animGo = null;
    Transform animGo_next = null;

    //while(canvas == null && _list.Count >= 2) {
    //  Debug.LogError("while: " + _list.Count);
    //  canvas = _list[1].gameObject.GetComponentInChildren<CanvasGroup>();
    //  yield return null;
    //}
    for (int i = 0; i < _list.Count; i++) {
      if(_list[i] != null)
        canvas = _list[i].gameObject.GetComponentInChildren<CanvasGroup>();
      if (canvas != null) {
        canvas.alpha = (i == 0) ? 1f : 0f;
      }
      if (_canvasGroups != null) {
        _canvasGroups.Add(canvas);
      }
    }

    //float baseScale = _list [0].gameObject.transform.localScale.x;

    //    float duration  = StayDuration - _initial_wait;
    //    yield return new WaitForSeconds(_initial_wait);

    while(true) {

      yield return new WaitForSeconds(_wait);
      bool canContinue = true;
      if(count >= _canvasGroups.Count)
        count = 0;
      canvas = _canvasGroups[count];
      if (canvas != null) { 
        animGo = canvas.gameObject.transform;
      } else {
        canContinue = false;
      }

      count++;
      if(count >= _canvasGroups.Count)
        count = 0;

      canvas_next = _canvasGroups[count];
      if (canvas_next != null) {
        animGo_next = canvas_next.gameObject.transform;
      } else {
        canContinue = false;
      }

      //float scaleDownAmount = 1f;
      //float scaleUpAmount = .01f;
      float totaltime = 0f;
      float delayedTime = 0f;

      while(canContinue && canvas_next.alpha < 1) {
        totaltime += Time.deltaTime;
        delayedTime = Mathf.Clamp(((totaltime / FadeDuration) - .5f), 0f, 1f);
        canvas.alpha -= Time.deltaTime / (FadeDuration * .5f);
        canvas_next.alpha = delayedTime;
        if (mLootScaleCurve != null) {
          float scaleUpAmount = Mathf.Max(0f, mLootScaleCurve.Evaluate(delayedTime));
          float scaleDownAmount = Mathf.Max(0f, 1 - totaltime / (FadeDuration * .5f));
          animGo.localScale = new Vector3(scaleDownAmount, scaleDownAmount, 1f);
          animGo_next.localScale = new Vector3(scaleUpAmount, scaleUpAmount, 1f);
        }
        yield return null;
      }

      /*
      while (canvas.alpha < 1){
        canvas.alpha += Time.deltaTime/FadeDuration;
        yield return null;
      }
      */

      _wait = StayDuration;
    }

  }

  private float timeCounter = 0f;
  private const float COUNTER_INTERVAL = 1f;
  void Update() {
    timeCounter += Time.deltaTime;
    if (timeCounter > COUNTER_INTERVAL) {
      timeCounter = 0f;
      if (mPrepRoot.activeInHierarchy == true) {
        for (int i = 0; i < slotScripts.Length; i++) {
          if (slotScripts[i] != null)
            slotScripts[i].Refresh();
        }
      }
    }
  }

  private void PopulateLayoutTile() {
    string layoutPrefabName = "";
    if(!isFactionFeud) {
      if (isRoadBoss) {
        //fake match type to show road boss, implement later
        layoutPrefabName = GetMatchTypeLayoutPrefab(SpecialMatchType.RoadBoss);
      } else {
        var quest = PlayerInfoScript.Instance.StateData.CurrentActiveQuest;
        layoutPrefabName = GetMatchTypeLayoutPrefab(quest.MatchType);
      }
    } else
      layoutPrefabName = GetMatchTypeLayoutPrefab(SpecialMatchType.Single);

    GameObject layoutGO = layoutTileRoot.InstantiateAsChild(UIPrefabs.GetPrefab(layoutPrefabName));
    layoutTile = layoutGO.GetComponent<PVEPrepScreenWrestlerLayoutTile>();
    if (infoParent != null && layoutTile != null && layoutTile.popupInfoParent != null) {
      layoutTile.popupInfoParent.transform.SetParent(infoParent.transform, false);
    }
  }

  private void PopulatePrepPanel(List<WrestlerItem> wrestlers) {

    //MatchPrepType = PrepType.Match_1v1;

    if(layoutTile == null) {
      MatchPrepType = PrepType.Match_1v1;
      PopulateLayoutTile();
    }

    SetDifficulty();
    UpdateStartButtonStatus();
    PopulateMatchInfo();
    
    if (StartButton != null) {
      StartButton.SetActive(!isRoadBoss);
    }
    if (StartRoadBossButton != null) {
      StartRoadBossButton.gameObject.SetActive(isRoadBoss);
    }
    if (isRoadBoss) {
      var listHistorySequence = UIManager.Instance.TryGetScreen<RoadBossQuestInfoController>()?.RoadBossHistorySequenceListId;
      layoutTile.PopulateBossMoveInfo(currentRoadBossData,listHistorySequence);
      layoutTile.PopulateBossHealthInfo(currentRoadBossData);
      layoutTile.PopulateBossBoostInfo();
      PopulateStartButtonInfo();
    }

    bool isFightCard = (Quest != null) ? Quest.Chapter.IsFightCard : false;
    bool isHealActive = true;

    if(isFightCard == true)
      isHealActive = false;

    if(isFactionFeud)
      isHealActive = true; // so health bar is displayed for opponetns

    bool isAllPlayerWrestlersSet = true;
    int emptyPlayerWrestlerSlotIndex = -1;
    for(int i = 0; i < wrestlers.Count; i++) {
      bool isThisHealActive = isHealActive;

      bool isOpponent = false;
      if(wrestlers.Count >= 3) //tag team.
        isOpponent = i >= 2;
      else
        isOpponent = i == 1;

      if(isFactionFeud && !isOpponent)
        isThisHealActive = false; // do not show health button for non opp SS

      if (wrestlers[i] != null && isFactionFeud && isOpponent) {
        wrestlers[i].GetBeltFactionFeudData();
      }

      DisplayWrestlerSlots(wrestlers[i], i, isOpponent, isThisHealActive);
      //  var foundInPlayers = PlayerInfoScript.Instance.StateData.PlayerWrestlers.Find(m=> m == wrestlers[i]);
      //  FrontEndWrestlerController.Instance.ApplyCamBackgroundColor(i, foundInPlayers == null);
      if (!isOpponent && wrestlers[i] == null) {
        isAllPlayerWrestlersSet = false;
        emptyPlayerWrestlerSlotIndex = i;
      }
    }

    //WHIP-34395 if one of the player wrestler slots is not set, hide the talent button
    if (!isAllPlayerWrestlersSet) {
      var slotscript = slotScripts.Find(m => m.Slot == emptyPlayerWrestlerSlotIndex + 1);
      if (slotscript != null) {
        slotscript.SetTalentActive(false);
      }
    }
    UpdateLinks();
    EnableMapCanvas(false);
    // Set Talent Score.
    OpponentTalentScore = SetTalentScore(wrestlers);

    WrestlerItem[] p1 = GetWrestlerList(false);
    WrestlerItem[] p2 = GetWrestlerList(true);
    LinkBonusController.Instance.UpdateLinkBonuses(p1, p2, (LinkBonusController.Instance.p1_active_link != null) ? LinkBonusController.Instance.p1_active_link.link_ID : null);

    // Set up boosts for new boost system.
    // R24 Quan - apply flag boost for AI
    if (Quest != null && Quest.Flags != null && Quest.Flags.Count > 0) {
      BoostController.Instance.UpdatePreviewBoosts(p1, p2, isFightCard || isFactionFeud, new FlagManager(Quest.Flags));
    } else {
      BoostController.Instance.UpdatePreviewBoosts(p1, p2, isFightCard || isFactionFeud);
    }

    //Reset Entourage UI
    foreach (PVEPrepScreenWrestlerSlot s in slotScripts) {
      s.ResetUI();
    }

    // Update Link & Trainer Icons.
    //
    // Update Player side link & trainer icons.
    PVEPrepScreenController.UpdateLinkAndTrainerIcons(slotScripts, false, p1);

    // Update Opponent side link & trainer icons.
    PVEPrepScreenController.UpdateLinkAndTrainerIcons(slotScripts, true, p2);

    int playerBoostCount = 0;
    int oppBoostCount = 0;

    NullifyItemData nullifyItemData = null;
    if (!string.IsNullOrEmpty(nullifyDataID)) {
      nullifyItemData = NullifyDataManager.Instance.GetData(nullifyDataID);
    }
    // update boost counter for player
    for (int w = 0; w < p1.Length; w++) {
      if (p1[w] != null)
        playerBoostCount += BoostController.Instance.GetBoostCountOfWreslter(p1[w], false, nullifyData: nullifyItemData);
    }

    //int p1boosts = BoostController.Instance.p1_boosts.Count;

    SetBoostCount(p1, false, playerBoostCount);
    var listWrestler = p1.ToList().Where(wrestler => wrestler != null).ToList();
    listWrestler.ForEach(wrestler => {
      wrestler.UpdatePerkEffect(false);
    });

    // update boost counter for opponent
    for (int w = 0; w < p2.Length; w++) {
      if (p2[w] != null) {
        oppBoostCount += BoostController.Instance.GetBoostCountOfWreslter(p2[w], true, nullifyData: nullifyItemData);

        oppBoostCount += p2[w].GetListBossAttributeData().Count;
      }
    }
      
    SetBoostCount(p2, true, oppBoostCount);
    var listWrestlerP2 = p2.ToList().Where(wrestler => wrestler != null).ToList();
    listWrestlerP2.ForEach(wrestler => {
      wrestler.UpdatePerkEffect(true);
    });
    if (isRoadBoss) {
      bool hasWrestlerSlot1 = p1 != null && p1.Length > 0 && p1[0] != null;
      bool hasWrestlerSlot2 = p1 != null && p1.Length > 1 && p1[1] != null;
      layoutTile.SetActiveTalent(hasWrestlerSlot1 || hasWrestlerSlot2);
      layoutTile.SetActiveTalentLeft(hasWrestlerSlot1);
      layoutTile.SetActiveTalentRight(hasWrestlerSlot2);
    }
    
    SwitchUINewModeR46();
    WPAnalyticsManager.Instance.TrackPrematchScreenInfo(GetPlayerSlots().ToArray(), GetOpponentSlots().ToArray());
  }
  
  /// <summary>
  /// Populate start match button info and state
  /// </summary>
  private void PopulateStartButtonInfo() {
    if (StartRoadBossButton != null) {
      int roadBossMaxTurnCount = MiscParams.Instance.RoadBossDailyBattleLimiter;
      int turnLeft = roadBossMaxTurnCount - playedTurnCount;
      if (turnLeft < 0) {
        turnLeft = 0;
      }
      if (turnLimitText.Text != null) {
        turnLimitText.Text = $"{turnLeft}/{roadBossMaxTurnCount}";
      }
      if (turnLeft < 1) {
        StartRoadBossButton.interactable = false;
      } else {
        StartRoadBossButton.interactable = true;
      }
    }
  }

  //R46
  [Header("New PvE mode")] [SerializeField]
  private Transform oldModeBottomParent;

  [SerializeField] private Transform newModeBottomParent;
  [SerializeField] private Transform consumableButton;
  [SerializeField] private KFFText enforcerSlotCount;
  [SerializeField] private Button enforcerButton;
  [SerializeField] private GameObject ruleButton;
  [SerializeField] private GameObject bottomR46NewMode;

  void SwitchUINewModeR46() {
    if (isPVE && Quest.Chapter.IsTicketedEventR46) {
      PopulateEnforcer();

      if (enforcerButton.gameObject.activeSelf || PlayerInfoScript.Instance.SaveData.PVEManager_R46.CanActiveRulePopup(Quest.Chapter.IsTicketedEventR46)) {
        bottomR46NewMode.SetActive(true);
        for (int i = 0; i < slotScripts.Length; i++) {
          if (!slotScripts[i].IsOpponent) {
            slotScripts[i].SwitchUI(true);
          }
        }
        consumableButton.transform.parent = newModeBottomParent;
        consumableButton.transform.localPosition = Vector3.zero;
        oldModeBottomParent.gameObject.SetActive(false);
        ruleButton.SetActive(true);
        if (horizontalLayout != null) {
          horizontalLayout.padding = new RectOffset(105, 100, -47, 0);
        }
      } else {
        SwitchToOldLayout();
        ruleButton.SetActive(false);
      }
    } else {
      SwitchToOldLayout();
      ruleButton.SetActive(false);
    }
  }

  void SwitchToOldLayout() {
    bottomR46NewMode.SetActive(false);
    for (int i = 0; i < slotScripts.Length; i++) {
      if (!slotScripts[i].IsOpponent) {
        slotScripts[i].SwitchUI(false);
      }
    }
    consumableButton.transform.parent = oldModeBottomParent;
    consumableButton.transform.localPosition = Vector3.zero;
    oldModeBottomParent.gameObject.SetActive(true);
  }
  
  public float DELAY_SHOWING_ENFORCER = 0.7f;
  public void PopulateEnforcer() {
    if (Quest?.Chapter != null) {
      int diff = PlayerInfoScript.Instance.SaveData.PlayerData.GetGameDifficulty();
      ChapterProgress_R46 chapterProgress = PVEUtils_R46.GetChapterProgressById(Quest.Chapter.ID, diff);
      Chapter_R46 chapterR46 = PVEUtils_R46.GetChapterById(Quest.Chapter.ID, (ChapterType)diff);
      
      // set difficulty to get enforcer slot datas
      if (chapterR46 != null && chapterR46.EnforcerSlots != null) {
        for (int i = 0; i < chapterR46.EnforcerSlots.Count; i++) {
          chapterR46.EnforcerSlots[i].Diff = diff;
        }
      }
      enforcerSlots = chapterProgress.GetEnforcerSlots(chapterR46.EnforcerSlots);
      if (chapterProgress != null && enforcerSlots.Count > 0) {
        Invoke(nameof(ShowEnforcerButton), DELAY_SHOWING_ENFORCER);
        enforcerSlotCount.Text = string.Format(KFFLocalization.Get("!!R46_ENFORCER_COUNT"), enforcerSlots.Count(e => e.equippedWrestler != null),
          enforcerSlots.Count);
      } else {
        enforcerButton.gameObject.SetActive(false);
        enforcerSlotCount.Text = string.Format(KFFLocalization.Get("!!R46_ENFORCER_COUNT"), 0, 0);
      }
    }
  }

  private void ShowEnforcerButton() {
    enforcerButton.gameObject.SetActive(true);
  }

  private List<EnforcerSlot> enforcerSlots = new List<EnforcerSlot>();
  public void OnClickEnforcer() {
    var info = new UIScreen.ScreenInfo();
    info.Add("Mode", RosterInventoryController.MenuMode.EnforcerSelect.ToString());
    info.Add(RosterInventoryController.EnforcerSlotsKey, enforcerSlots);
    info.Add(RosterInventoryController.EnforcerChapterKey, Quest?.Chapter.ID ?? "");
    info.Add(RosterInventoryController.EnforcerDidJoinChapterKey, true);
    UIManager.Instance.QueuePush("frontEnd_roster_default", info);
  }

//  public void OnClickStartBattleR53() {
//    if (mInTransition) {
//      return;
//    }
//    
//    GameStateData stateData = PlayerInfoScript.Instance.StateData;
//    
//    // Check wrestler health.
//    int wrestler_count = (!isFactionFeud) ? WPGame.GetPlayerWrestlerCount() : WPGame.GetPlayerWrestlerCount(SpecialMatchType.Single);
//
//    for(int i = 0; i < wrestler_count; i++) {
//      if(stateData.PlayerWrestlers[i] == null)
//        continue;
//
//      if(stateData.PlayerWrestlers[i].IsInjured()) {
//        string title = KFFLocalization.Get("!!PRE_MATCH_WRESTLER_INJURED");
//        string body = KFFLocalization.Get("!!PRE_MATCH_WRESTLER_INJURED_DESC");
//        SimplePopupController.Instance.ShowMessage(title, body);
//        KFFSoundPlayer.Instance.PlayErrorSound();
//        return;
//      }
//    }
//    
//    // Set Player Wrestlers.
//    ForgetPreviousWrestlers(); //lock in selections now
//    for(int i = 0; i < wrestler_count; i++) {
//      WrestlerItem w = PlayerInfoScript.Instance.StateData.PlayerWrestlers[i];
//      if(w != null) {
//        //R25: check SGSS
//        bool inFFBrawl = isFactionFeud && w.InFactionFeud;
//        if(w.Form.isSGSS && !w.Form.CanBeAccessedByPlayer && !w.InFightCard && !inFFBrawl) {
//          //HungTN - WHIP-6892
//          SimplePopupController.Instance.ShowMessage(KFFLocalization.Get("!!ERROR_MATCH_START"), KFFLocalization.Get("!!SUBSCRIPTION_SGSS_CANNOTUSE"), MatchStartErrorHandle, KFFLocalization.Get("!!BUTTON_RETURN"));
//          return;
//        }
//
//        PlayerInfoScript.Instance.SaveData.ResourcesData.SetCurrentWrestler(w, i);
//      }
//    }
//    
//    // VFX
//    ParticleCanvasController.Instance.SpawnVFX(BattleStartVFX, StartButton.transform);
//    mMatchStart = true;
//    StopSwitchLootPrizeCoroutines();
//    
//    mInTransition = true;
//    TFUtils.MyTime(true);
//    ABManager.Instance.PreloadAssetBundles();
//
//    // Go to boss battle scene
//    battleType = (int)BattleConnectionManager_R50.BattleType.BossMode;
//    LoadBattleScene();    
//  }
  
  public void OnClickBattleScoring() {
    ScreenInfo info = new ScreenInfo();
    Chapter_R46 newChapterDataR46 = PVEUtils_R46.GetChapterById(Quest.Chapter.ID, (ChapterType)PlayerInfoScript.Instance.SaveData.PlayerData.GetGameDifficulty());
    info.Add("newChapterData", newChapterDataR46);
    // info.Add("Quest", Quest);
    UIManager.Instance.QueuePush("frontEnd_rules_popup_r46", info);
  }

  //R46
  private bool ShouldShowConfirmEnforcer() {
    if (enforcerSlots?.Count == 0)
      return false;
    if (PlayerInfoScript.Instance.SaveData.PlayerData.DisableWarnings[(int)WPPlayerData.DisableWarningType.EnforcerEmpty]) {
      return false;
    }
    for (int i = 0; i < enforcerSlots.Count; i++) {
      if (enforcerSlots[i].equippedWrestler != null) {
        return false;
      }
    }

    if (TutorialController.Instance.IsBlockActive(TutorialController.ReactivationBlockChallengeID)) {
      return false;
    }
    
    return true;
  }

  public static void UpdateLinkAndTrainerIcons(PVEPrepScreenWrestlerSlot[] _slots, bool _isOpponent, WrestlerItem[] _pls) {
    int index = 0;
    bool boost_link = false;

    if(_isOpponent == true)
      index = 1;

    if(_pls != null) {
      for(int i = 0; i < _pls.Length; i++) {
        if(_pls[i] == null)
          continue;

        PVEPrepScreenWrestlerSlot slot = null;
        foreach(PVEPrepScreenWrestlerSlot s in _slots) {
          if(s == null)
            continue;
          if(_isOpponent != s.IsOpponent)
            continue;

          int trainer_count = 0;
          int total_count = 0;
          if((s.Wrestler != null) && (s.Wrestler.Form.ID == _pls[i].Form.ID)) {
            IEnumerable<BuffData> list = BoostController.Instance.GetPreviewBuffs(index, _pls[i].Form.GroupID);
            if(list != null) {
              foreach(BuffData b in list) {
                total_count++;
                if(b.Source == BuffData.BuffSource.Trainer)
                  trainer_count++;
              }
            }

            if(boost_link == false) {
              if(total_count > trainer_count)
                boost_link = true;
            }
            // Update WrestlerSlots
            s.UpdateBoostsFX(trainer_count, boost_link);
          }
        }
      }
    }
  }

  public static int GetBuffCountFromTrainers(WrestlerItem _w, bool _isOpponent) {
    int index = 0;
    if(_isOpponent == true)
      index = 1;

    int trainer_count = 0;
    IEnumerable<BuffData> list = BoostController.Instance.GetPreviewBuffs(index, _w.Form.GroupID);
    if(list != null) {
      foreach(BuffData b in list)
        if(b.Source == BuffData.BuffSource.Trainer)
          trainer_count++;
    }

    return trainer_count;
  }

  public List<WrestlerItem> GetWrestlerListForPrep() {
    //    HideWrestlerList();//force hiding the wrestler list when going into the prep screen

    bool isFightCard = (Quest != null) ? Quest.Chapter.IsFightCard : false;

    if(this.isPVE && PlayerInfoScript.Instance.StateData.PlayerWrestlers[0] != null) {
      if(PlayerInfoScript.Instance.StateData.PlayerWrestlers[0].InFactionFeud && FactionFeudManager.Instance.ShouldLockWrestlers()) {
        PlayerInfoScript.Instance.StateData.PlayerWrestlers[0] = null;
      }
    }

    //try to load the first wrestler 
    if(!PlayerInfoScript.Instance.StateData.IsPVP) {
      if(PlayerInfoScript.Instance.StateData.PlayerWrestlers[0] == null && (Quest == null || (Quest != null && Quest.QuestRequirements == null))) {
        WrestlerItem wcurrent = PlayerInfoScript.Instance.SaveData.ResourcesData.GetCurrentWrestler();
        int index = PlayerInfoScript.Instance.StateData.PlayerWrestlers.IndexOf(wcurrent);
        bool ffInvalid = (!(wcurrent != null && wcurrent.InFactionFeud && FactionFeudManager.Instance.ShouldLockWrestlers())) && this.isFactionFeud;
        bool pveInvalid = (wcurrent != null && wcurrent.InFactionFeud && FactionFeudManager.Instance.ShouldLockWrestlers()) && this.isPVE;
        if(index < 0 && (!ffInvalid && !pveInvalid)) {
          PlayerInfoScript.Instance.StateData.PlayerWrestlers[0] = PlayerInfoScript.Instance.SaveData.ResourcesData.GetCurrentWrestler();
        }
      }
    }

    if(this.isPVE) {
      //R30 
      if(PlayerInfoScript.Instance.StateData.CurrentActiveQuest.MatchType == SpecialMatchType.Single) {
        if(PlayerInfoScript.Instance.StateData.PlayerWrestlers[0] != null) {
          if(PlayerInfoScript.Instance.StateData.PlayerWrestlers[0].Form.SSGroup.Gender != PlayerInfoScript.Instance.StateData.CurrentActiveQuest.SetLoadout.WrestlerGender) {
            PlayerInfoScript.Instance.StateData.PlayerWrestlers[0] = null;
          }
        }
      } else if(PlayerInfoScript.Instance.StateData.CurrentActiveQuest.MatchType == SpecialMatchType.TagTeam) {
        for(int i = 0; i < PlayerInfoScript.Instance.StateData.CurrentActiveQuest.SetLoadouts.Count && i < 2; i++) {
          //Fix WHIP-14035
          if (i >= PlayerInfoScript.Instance.StateData.PlayerWrestlers.Length) {
            continue;
          }
          WrestlerItem wrestlerItem = PlayerInfoScript.Instance.StateData.PlayerWrestlers[i];
          if(wrestlerItem != null && wrestlerItem.Form.SSGroup.Gender != PlayerInfoScript.Instance.StateData.CurrentActiveQuest.SetLoadouts[i].WrestlerGender) {
            PlayerInfoScript.Instance.StateData.PlayerWrestlers[i] = null;
          }
        }
      }
    }

    if (isRoadBoss) {
      
    }

    WrestlerItem[] roster, wlist;
    if(mWrestlerExclusiveSelects != null) {
      roster = new WrestlerItem[mWrestlerExclusiveSelects.Count];
      wlist = mWrestlerExclusiveSelects.ToArray();
    } else {
      //check if the current wrestler is applicable to the era and stable
      roster = new WrestlerItem[PlayerInfoScript.Instance.StateData.PlayerWrestlers.Length];
      wlist = new WrestlerItem[PlayerInfoScript.Instance.StateData.PlayerWrestlers.Length];
      PlayerInfoScript.Instance.StateData.PlayerWrestlers.CopyTo(wlist, 0);
    }

    // blank out player's SS if injured
    if(PlayerInfoScript.Instance.StateData.PlayerWrestlers[0] != null && isFactionFeud) {
      if(PlayerInfoScript.Instance.StateData.PlayerWrestlers[0].CurrentHP < 1 || PlayerInfoScript.Instance.StateData.PlayerWrestlers[0].IsInjured()) {
        PlayerInfoScript.Instance.StateData.PlayerWrestlers[0] = null;
      }
    }

    // HACK
    // Some how populate function called mnultiple times from menu system.
    // And same wrestler registerd to PlayerWrestlers list. So, remove duplicated wrestler based on group ID.
    int count = PlayerInfoScript.Instance.StateData.PlayerWrestlers.Length;
    for(int i = 0; i < count; i++) {
      WrestlerItem wrestler = PlayerInfoScript.Instance.StateData.PlayerWrestlers[i];

      if(wrestler != null) {
        if((mWrestlerExclusiveSelects != null && !mWrestlerExclusiveSelects.Contains(PlayerInfoScript.Instance.StateData.PlayerWrestlers[i])) ||
           (PlayerInfoScript.Instance.StateData.PlayerWrestlers[i].InFightCard && !isFightCard) ||
           (isFightCard && PlayerInfoScript.Instance.StateData.PlayerWrestlers[i].IsInjured()) ||
           PlayerInfoScript.Instance.StateData.PlayerWrestlers[i].IsLockInFactionBossLineup()) 
        {
          PlayerInfoScript.Instance.StateData.PlayerWrestlers[i] = null;
          continue;
        }

        for(int j = i + 1; j < count; j++) {
          if(PlayerInfoScript.Instance.StateData.PlayerWrestlers[j] != null) {
            if(PlayerInfoScript.Instance.StateData.PlayerWrestlers[i].Form.SSGroup.ID == PlayerInfoScript.Instance.StateData.PlayerWrestlers[j].Form.SSGroup.ID)
              PlayerInfoScript.Instance.StateData.PlayerWrestlers[j] = null;
          }
        }
      }
    }

    WrestlerItem wrestlerSlot1 = null;
    WrestlerItem wrestlerSlot2 = null;
    WrestlerItem wrestlerSlot3 = null;

    //Fix WHIP-14035
    if (PlayerInfoScript.Instance.StateData.PlayerWrestlers[0] != null) {
      wrestlerSlot1 = PlayerInfoScript.Instance.StateData.PlayerWrestlers[0];
    }
    if (PlayerInfoScript.Instance.StateData.PlayerWrestlers[1] != null) {
      wrestlerSlot2 = PlayerInfoScript.Instance.StateData.PlayerWrestlers[1];
    }
    if (PlayerInfoScript.Instance.StateData.PlayerWrestlers[2] != null) {
      wrestlerSlot3 = PlayerInfoScript.Instance.StateData.PlayerWrestlers[2];
    }

    mQuestReqsMet = true;

    if(!PlayerInfoScript.Instance.StateData.IsPVP && !PlayerInfoScript.Instance.StateData.IsFactionFeud) {
      if(Quest != null && Quest.QuestRequirements != null) {
        wrestlerSlot1 = wrestlerSlot2 = wrestlerSlot3 = null;

        for(int n = 0; n < roster.Length; n++) {
          //Fix WHIP-14035
          if (n >= wlist.Length) {
            continue;
          }
          WrestlerItem w = wlist[n];
          if(w != null && !(w.InFactionFeud && FactionFeudManager.Instance.ShouldLockWrestlers()) && !w.InFightCard && !w.IsLockInFactionBossLineup()) {
            if(Quest != null && Quest.QuestRequirements.Length > n && Quest.QuestRequirements[n] != null) {
              if(w.CanHeFightThisMatch(Quest.QuestRequirements[n]) == true) {
                roster[n] = w;
              }
            } else {
              roster[n] = w;
            }
          }
        }

        //Fix WHIP-14035
        if (roster[0] != null) {
          wrestlerSlot1 = roster[0];
        }
        if (roster[1] != null) {
          wrestlerSlot2 = roster[1];
        }
        if (roster[2] != null) {
          wrestlerSlot3 = roster[2];
        }
      }
    }

    // To make sure, the wrestler in the slot is the latest wrestler item. (especially after evo/enhance.)
    if(wrestlerSlot1 != null)
      wrestlerSlot1 = PlayerInfoScript.Instance.SaveData.ResourcesData.GetWrestlerBySSGroupID(wrestlerSlot1.Form.GroupID);
    if(wrestlerSlot2 != null)
      wrestlerSlot2 = PlayerInfoScript.Instance.SaveData.ResourcesData.GetWrestlerBySSGroupID(wrestlerSlot2.Form.GroupID);
    if(wrestlerSlot3 != null)
      wrestlerSlot3 = PlayerInfoScript.Instance.SaveData.ResourcesData.GetWrestlerBySSGroupID(wrestlerSlot3.Form.GroupID);

    List<WrestlerItem> wrestlers = new List<WrestlerItem>();

    if(Quest != null) {
      // scastro: we must update PlayerWrestlers now because
      // there are too many dependencies on it being updated with what you see, such as props prep screen.
      // We'll restore their previous selection if they back out of the screen.
      PlayerInfoScript.Instance.StateData.PlayerWrestlers[0] = wrestlerSlot1;
      PlayerInfoScript.Instance.StateData.PlayerWrestlers[1] = wrestlerSlot2;
      PlayerInfoScript.Instance.StateData.PlayerWrestlers[2] = wrestlerSlot3;

      if(Quest.MatchType == SpecialMatchType.TagTeam) {
        wrestlers.Add(wrestlerSlot1);
        wrestlers.Add(wrestlerSlot2);
        wrestlers.Add(PlayerInfoScript.Instance.StateData.OpponentWrestlers[0]);
        wrestlers.Add(PlayerInfoScript.Instance.StateData.OpponentWrestlers[1]);
      } else if(Quest.MatchType == SpecialMatchType.TripleThreat) {
        wrestlers.Add(wrestlerSlot1);
        wrestlers.Add(PlayerInfoScript.Instance.StateData.OpponentWrestlers[0]);
        wrestlers.Add(PlayerInfoScript.Instance.StateData.OpponentWrestlers[1]);
      } else if(Quest.MatchType == SpecialMatchType.FatalFourWay) {
        wrestlers.Add(wrestlerSlot1);
        wrestlers.Add(PlayerInfoScript.Instance.StateData.OpponentWrestlers[0]);
        wrestlers.Add(PlayerInfoScript.Instance.StateData.OpponentWrestlers[1]);
        wrestlers.Add(PlayerInfoScript.Instance.StateData.OpponentWrestlers[2]);
      } else if(Quest.MatchType == SpecialMatchType.SixMenTagMatch) {
        wrestlers.Add(wrestlerSlot1);
        wrestlers.Add(wrestlerSlot2);
        wrestlers.Add(wrestlerSlot3);
        wrestlers.Add(PlayerInfoScript.Instance.StateData.OpponentWrestlers[0]);
        wrestlers.Add(PlayerInfoScript.Instance.StateData.OpponentWrestlers[1]);
        wrestlers.Add(PlayerInfoScript.Instance.StateData.OpponentWrestlers[2]);
      } else { //if (Quest.MatchType == SpecialMatchType.Single)
        wrestlers.Add(wrestlerSlot1);
        wrestlers.Add(PlayerInfoScript.Instance.StateData.OpponentWrestlers[0]);
      }
    } else if(isFactionFeud) {
      wrestlers.Add(wrestlerSlot1);
      wrestlers.Add(PlayerInfoScript.Instance.StateData.OpponentWrestlers[0]);
    } else if (isRoadBoss) {
      if (_roadBossEventNodeData != null) {
        RoadBossSlotRequirement slot1Reqs = _roadBossEventNodeData.Slot1Reqs;
        if (wrestlerSlot1 != null && (slot1Reqs == null || slot1Reqs.DoesWrestlerMeetRequirements(wrestlerSlot1))) {
          wrestlers.Add(wrestlerSlot1);
        } else {
          wrestlers.Add(null);
        }
        RoadBossSlotRequirement slot2Reqs = _roadBossEventNodeData.Slot2Reqs;
        if (wrestlerSlot2 != null && (slot2Reqs == null || slot2Reqs.DoesWrestlerMeetRequirements(wrestlerSlot2))) {
          wrestlers.Add(wrestlerSlot2);
        } else {
          wrestlers.Add(null);
        }
      }
      wrestlers.Add(PlayerInfoScript.Instance.StateData.OpponentWrestlers[0]);
    }

    return wrestlers;
  }

  public bool SwapSSOrder() {
    GameStateData sd = PlayerInfoScript.Instance.StateData;
    if(sd.PlayerWrestlers != null) {
      if(sd.PlayerWrestlers.Length >= 2) {
        WrestlerItem w = sd.PlayerWrestlers[0];
        sd.PlayerWrestlers[0] = sd.PlayerWrestlers[1];
        sd.PlayerWrestlers[1] = w;

        return true;
      }
    }

    return false;
  }

  public void PopulateMatchInfo() {
    var stateData = PlayerInfoScript.Instance.StateData;
    var opponentData = stateData.PVPOpponent;
    bool isPVP = stateData.IsPVP;

    if (MatchType != null && stateData.CurrentActiveQuest != null) {
      MatchType.text = (!isFactionFeud) ? stateData.CurrentActiveQuest.MatchType.ToString() : SpecialMatchType.Single.ToString();
    }
    
    foreach(var info in PVPPlayerInfo) {
      if(info.InfoGroup != null)
        info.InfoGroup.SetActive(isPVP || isFactionFeud);
    }

    if (PVEMatchInfoGroup != null && PVPRankInfoGroup != null) {
      PVEMatchInfoGroup.SetActive(isFactionFeud);
      PVPRankInfoGroup.SetActive(isFactionFeud);
    }

    // hide groups during FF
    if(isFactionFeud) {
      PVEMatchInfoGroup.SetActive(false);
      PVPRankInfoGroup.SetActive(false);
    }
    ShowFTUEForUnlockMentor();

    //PVPBackgroundGroup.SetActive(isPVP);
    if (RankToEarn != null && RankToLose != null) {
      if (!isFactionFeud && opponentData != null) {
        RankToEarn.text = (isPVP) ? KFFLocalization.Get("!!WIN") + ": " + opponentData.eloWinDelta : "";
        RankToLose.text = (isPVP) ? KFFLocalization.Get("!!LOSE") + ": -" + opponentData.eloLoseDelta : "";
      } else {
        RankToEarn.text = KFFLocalization.Get("!!WIN") + ": TEMP";
        RankToLose.text = KFFLocalization.Get("!!LOSE") + ": -TEMP";
      }
    }
/* HungTN-R36 no use in r36
    if(isPVP) {
      var learderboardDatabase = PVPLeaderboardDataManager.Instance.GetDatabase();
      for(int i = 0; i < PVPPlayerInfo.Length; i++) {
        //TODO-PVP : opponent player name should be included in the WPOpponentData
        string playerName = (i == 0) ? PlayerInfoScript.Instance.SaveData.PlayerData.MultiplayerPlayerName : opponentData.playerName;
        if(PVPPlayerInfo[i].PlayerName != null)
          PVPPlayerInfo[i].PlayerName.text = KFFLocalization.Get("!!PLAYER") + ": " + playerName;
        // xuyen.nguyenthi R36 TODO
        string talent = "0";
        //     string talent = (i == 0) ? 
//          PlayerInfoScript.Instance.SaveData.MultiplayerData.TotalTalentOfOffensiveSS.ToString() : opponentData.talent.ToString();
        if(PVPPlayerInfo[i].PlayerTalent != null)
          PVPPlayerInfo[i].PlayerTalent.text = KFFLocalization.Get("!!TALENT") + ": " + talent;

        // TODO: opponentData.currentLeagueName is actually the league ID now. Confusing.
        var currentLeague = opponentData.currentLeagueName;
        var currentLeaderboard = learderboardDatabase.Find(m => m.ID == currentLeague);
        //Sprite s = KFFResourceManager.Instance.LoadResource(currentLeaderboard.IconTexture, typeof(Sprite)) as Sprite;
        if(PVPPlayerInfo[0].League != null)
          //PVPPlayerInfo[0].League.sprite = s;
          KFFResourceManager.Instance.LoadOnDemandLooseTextureToImage(currentLeaderboard.IconTexture, PVPPlayerInfo[0].League);
      }
    }
    */
  }

  private void ShowFTUEForUnlockMentor() {
    if (TutorialController.Instance.IsBlockActive(TutorialController.ReactivationBlockChallengeID)) {
      return;
    }
    
    UnlockMentorManager UnlockMentor = new UnlockMentorManager();
    if (!UnlockMentorManager.IsFirstTimeUnlockMentor) {
      return;
    }

    UnlockMentor.UpdateConditionSlotMentor();
    PlayerLeagueConfigDataManager config = PlayerLeagueConfigDataManager.Instance;
    WrestlerItem[] list = GetWrestlerList(false);

    for (int i = 0; i < list.Length; i++) {
      if (TrainerDataManager.Instance.IsDeployedMentorData()) {
        if (list[i] != null && list[i].StartedTier >= WrestlerTier.Bronze && list[i].StartedRarity >= UnlockMentor.unlockStar && PlayerInfoScript.Instance.SaveData.ResourcesData.PlayerLeagueManager.CurrentLeagueLevel >= config.UnlockMentorLeague) {
          UIManager.Instance.QueuePush(MentorTutorialUnlock_R49.PrefabId);
          UnlockMentorManager.IsFirstTimeUnlockMentor = false;
          break;
        }
      }
    }
  }

  public void HideWrestlerList() {
    for(int i = 0; i < slotScripts.Length; i++) {
      if(slotScripts[i].FadeBlackGroup != null)
        slotScripts[i].FadeBlackGroup.SetActive(false);
    }
    UpdateStartButtonStatus();
  }

  public void DisplayWrestlerSlots(WrestlerItem wrestler, int slot, bool isOpponent = false, bool isHealActive = true) {
    var slotscript = slotScripts.Find(m => m.Slot == slot + 1);
    slotscript.Populate(wrestler, isOpponent, slot); //send null to show required info
    OpponentTalentScore = SetTalentScore(GetWrestlerListForPrep());
  }

  private void DisplayRequiredInfo(int slot) {
    var slotscript = slotScripts.Find(m => m.Slot == slot + 1);
    if(Quest.QuestRequirements != null && Quest.QuestRequirements.Length > (slot - 1))
      slotscript.ShowRequiredInfo(Quest.QuestRequirements[slot - 1]);
  }

  //public void RefreshOverlayForAll()
  //{
  //  var tiles = TileGrid.GetComponentsInChildren<WrestlerTileScript>();
  //  foreach (var tile in tiles)
  //  {
  //    tile.RefreshOverlay();
  //  }
  //}
  
  //R46
  public void OnClickBattleR46() {
    if (FBLobbyManager_R53.Instance.IsPlayerJoinedRoom()) {
      FBLobbyUI_R53.ShowWarningBattleLock();
      return;
    }

    bool isStipulation = Quest != null && Quest.Chapter != null && Quest.Chapter.IsTicketedEventR46; 
    if (isStipulation) {
      if (PlayerInfoScript.Instance.SaveData.PVEManager_R46.currentChapter != null) {
        if (PlayerInfoScript.Instance.SaveData.PVEManager_R46.currentChapter.GetCurrentChapterState() != ChapterState.ACTIVE) {
          SimplePopupController.Instance.ShowMessage(KFFLocalization.Get("!!R46_CHAPTER_ENDED_TITLE"), KFFLocalization.Get("!!R46_CHAPTER_ENDED_DESC"), CloseBackToCircuit);
          return;
        }
      }
    }
    if (ShouldShowConfirmEnforcer()) {
      SimplePopupController.Instance.ShowWarning(KFFLocalization.Get("!!R46_ENFORCER_EMPTY_WARN_TITLE"), KFFLocalization.Get("!!R46_ENFORCER_EMPTY_WARN_CONTENT"), 
        OnClickEnforcer, CheckStipulationMode, ToggleDisableWarningEnforcer, KFFLocalization.Get("!!R46_SET_ENFORCER"), KFFLocalization.Get("!!R42_BATTLE_NOW"));
    } else {
      CheckStipulationMode();
    }
  }

  private void CheckStipulationMode() {
    bool isStipulation = Quest != null && Quest.Chapter != null && Quest.Chapter.IsTicketedEventR46;
    if (isStipulation && MiscParams.Instance.DisableFreezePinMeter) {
      // R55 Check Freeze Pin Meter effect
      bool haveFreezePinMeter = false;
      int wrestler_count = WPGame_R50.GetPlayerWrestlerCount();
      for(int i = 0; i < wrestler_count; i++) {
        WrestlerItem w = PlayerInfoScript.Instance.StateData.PlayerWrestlers[i];
        if(w != null) {
          for (int j = 0; j < w.AbilitySet.Length; j++) {
            if (w.AbilitySet[j].Form.FreezePinMeter) {
              haveFreezePinMeter = true;
              break;
            }
          }
        }
      }
        
      //R55: Warning about Free Pin Meter effect in Stipulation Mode
      if (haveFreezePinMeter && !PlayerInfoScript.Instance.SaveData.PlayerData.DisableWarnings[(int)WPPlayerData.DisableWarningType.MoveDisabled]) {
        SimplePopupController.Instance.ShowWarning(KFFLocalization.Get("!!R55_FREEZE_PIN_METER_WARNING_TITLE"), 
          KFFLocalization.Get("!!R55_FREEZE_PIN_METER_WARNING_DESC"), 
          () => {
            PlayerInfoScript.Instance.SaveData.PlayerData.DisableWarnings[(int)WPPlayerData.DisableWarningType.MoveDisabled] = SimplePopupController.Instance.ToggleButton.isOn;
            OnClickStartBattle();
          }, 
          () => { }, toggled => {}, 
          KFFLocalization.Get("!!BUTTON_OK"), 
          KFFLocalization.Get("!!BUTTON_CANCEL"));  
      }
      else {
        OnClickStartBattle();
      }
    }
    else {
      OnClickStartBattle();
    }
  }
  
  private void CloseBackToCircuit() {
    UIManager.Instance.QueuePopToScreen(typeof(TourSelectController));
  }

  private static void ToggleDisableWarningEnforcer(bool isToggledOn) {
    PlayerInfoScript.Instance.SaveData.PlayerData.DisableWarnings[(int)WPPlayerData.DisableWarningType.EnforcerEmpty] = isToggledOn;
  }

  void OnClickStartBattle() {
    bool isFactionFeud = PlayerInfoScript.Instance.StateData.IsFactionFeud;
    if (isFactionFeud) {
      if(!UIManager.Instance.enableFF) {
        var title = KFFLocalization.Get("!!FACTION_FEUD_MAINTENANCE");
        var body = KFFLocalization.Get("!!THIS_FUTURE_IS_UNDER_MAINTENANCE");
        SimplePopupController.Instance.ShowMessage(title, body, BottomMenuController.Instance.BackToHomeMenu);
        return;
      }
    }

    if(mInTransition == true)
      return;

    if(!mQuestReqsMet) {
      Debug.Log("requirements not met!");
      return;
    }

    // Check if Expired
    if(currentFightCard != null && currentFightCard.GetTimeRemaining() <= 0) {
      SimplePopupController.Instance.ShowMessage(
        KFFLocalization.Get("!!LEAGUE_END"),
        KFFLocalization.Get("!!FC_EVENT_ENDED_DESC"));
      return;
    }

    //Moved this earlier
    if(!CanGoIntoFight()) {
      SimplePopupController.Instance.ShowMessage("", KFFLocalization.Get("!!ERR_NEED_BOOK_WRESTLERS"));
      KFFSoundPlayer.Instance.PlayErrorSound();
      return;
    }

    //TODO check stamina later.
    GameStateData stateData = PlayerInfoScript.Instance.StateData;
    QuestData activeQuest = stateData.CurrentActiveQuest;

    // Check wrestler health.
    int wrestler_count = (!isFactionFeud) ? WPGame_R50.GetPlayerWrestlerCount() : WPGame_R50.GetPlayerWrestlerCount(SpecialMatchType.Single);

    //TEMP Check against wrestler health in FF to prevent battles against 0 health opponents
    if(isFactionFeud) {
      if(stateData.OpponentWrestlers[0] != null) {
        if(stateData.OpponentWrestlers[0].isDefeatedFF()) {
          string title = "LOC: Wrestler Already Defeated!";
          string body = "LOC: Select a wrestler with more health then 0!";
          SimplePopupController.Instance.ShowMessage(title, body, null);
          return;
        }
      }
    }

    for(int i = 0; i < wrestler_count; i++) {
      if(stateData.PlayerWrestlers[i] == null)
        continue;

      //hide injured warning in road boss mode
      if (stateData.PlayerWrestlers[i].IsInjured() && !isRoadBoss) {
        string title = KFFLocalization.Get("!!PRE_MATCH_WRESTLER_INJURED");
        string body = KFFLocalization.Get("!!PRE_MATCH_WRESTLER_INJURED_DESC");
        SimplePopupController.Instance.ShowMessage(title, body);
        KFFSoundPlayer.Instance.PlayErrorSound();
        return;
      }
    }

    // Set Player Wrestlers.
    ForgetPreviousWrestlers(); //lock in selections now
    for(int i = 0; i < wrestler_count; i++) {
      WrestlerItem w = PlayerInfoScript.Instance.StateData.PlayerWrestlers[i];
      if(w != null) {
        //R25: check SGSS
        bool inFFBrawl = isFactionFeud && w.InFactionFeud;
        if(w.Form.isSGSS && !w.Form.CanBeAccessedByPlayer && !w.InFightCard && !inFFBrawl) {
          //HungTN - WHIP-6892
          SimplePopupController.Instance.ShowMessage(KFFLocalization.Get("!!ERROR_MATCH_START"), KFFLocalization.Get("!!SUBSCRIPTION_SGSS_CANNOTUSE"), MatchStartErrorHandle, KFFLocalization.Get("!!BUTTON_RETURN"));
          return;
        }

        PlayerInfoScript.Instance.SaveData.ResourcesData.SetCurrentWrestler(w, i);
      }
    }

    bool bonusActive = false;
    QuestBonusType bonusType = QuestBonusType.None;
    string bonusText = "";
    if(!PlayerInfoScript.Instance.StateData.IsPVP) {
      if (!isFactionFeud && !isRoadBoss) {
        activeQuest.Chapter.GetBonusStatus(out bonusActive, out bonusType, out bonusText);
      } else {
        //Debug.LogError("TODO: Faction Feud Bonuses!");
      }
    }
    stateData.ActiveQuestBonus = bonusType;
    
    isShowedWarning = false;
    if (WPGame_R50.GetPlayerWrestlerCount() == 1) {
      isShowedWarning = CheckForWarningMessage(stateData.PlayerWrestlers[0], stateData.OpponentWrestlers[0],
          WarningContinueCallback, slotScripts, nullifyDataID);
    } else {
      isShowedWarning = CheckForWarningMessage(stateData.PlayerWrestlers[0], stateData.PlayerWrestlers[1],
          stateData.OpponentWrestlers[0], stateData.OpponentWrestlers[1], WarningContinueCallback, slotScripts, nullifyDataID);
    }
    
    if (isShowedWarning) {
      return;
    }
      
    if (CheckToShowCaptcha(WarningContinueCallback)) {
      return;
    }
    

    //TODO nice VFX
    if (isRoadBoss) {
      if (StartRoadBossButton != null) {
        ParticleCanvasController.Instance.SpawnVFX(BattleStartVFX, StartRoadBossButton.transform);
      }
    } else if (StartButton != null) {
      ParticleCanvasController.Instance.SpawnVFX(BattleStartVFX, StartButton.transform);
    }


    if(stateData.CurrentActiveQuest != null)
      Debug.Log("Starting quest " + stateData.CurrentActiveQuest.ID);

    

    mMatchStart = true;

    StopSwitchLootPrizeCoroutines();

    // Track data for Scopely.
    // Looks pvp match is not start from here. So send information as PVE match.
    WPAnalyticsManager.Instance.SetParametersForBattleDataTrack(OpponentTalentScore);
    //R24
    if(!PlayerInfoScript.Instance.StateData.IsFactionFeud) {
      if(Quest != null && Quest.Flags != null && Quest.Flags.Count > 0) {
        WPAnalyticsManager.Instance.SetFlagParametersForBattleDataTrack(new FlagManager(Quest.Flags), false);
      }
    } else {
      if(FactionFeudManager.Instance.curFactionFeud != null && FactionFeudManager.Instance.curOpponentMatchup != null) {
        string claimedMemberID = FactionFeudManager.Instance.curOpponentMatchup.GetMemberIDClaimedByMemberID(PlayerInfoScript.Instance.SaveData.PlayerData.ID);
        FactionFeudMember oppPlayer = FactionFeudManager.Instance.curOpponentMatchup.GetMemberByID(claimedMemberID);
        if(oppPlayer != null && oppPlayer.flagManager != null) {
          WPAnalyticsManager.Instance.SetFlagParametersForBattleDataTrack(oppPlayer.flagManager, false);
        }
      }
    }

    mInTransition = true;
    TFUtils.MyTime(true);

    ABManager.Instance.PreloadAssetBundles();

    if(!isFactionFeud) {
      if (Quest != null && Quest.Chapter != null && Quest.Chapter.IsTicketedEventR46) {
        battleType = (int)BattleConnectionManager_R50.BattleType.Stipulation;
      } else if (isRoadBoss){
        battleType = (int)BattleConnectionManager_R50.BattleType.RoadBoss;
      } else {
        battleType = (int)BattleConnectionManager_R50.BattleType.Pve;
      }
      
      LoadBattleScene();
    } else {
      TFUtils.MyLogError(new object[]{
        "sLoadBattleSceneFF=========", 
      }, go: null, color: "magenta");
      battleType = (int)BattleConnectionManager_R50.BattleType.Factionfued;
      sLoadBattleSceneFF();
    }
  }

  private void WarningContinueCallback() {
    if (isShowedWarning) {
      isShowedWarning = false;
      if (CheckToShowCaptcha(WarningContinueCallback)) {
        return;
      }
    }
    if (isRoadBoss) {
      if (StartRoadBossButton != null) {
        ParticleCanvasController.Instance.SpawnVFX(BattleStartVFX, StartRoadBossButton.transform);
      }
    } else if (StartButton != null) {
      ParticleCanvasController.Instance.SpawnVFX(BattleStartVFX, StartButton.transform);
    }
    GameStateData stateData = PlayerInfoScript.Instance.StateData;

    if (!isFactionFeud && stateData.CurrentActiveQuest != null) {
      Debug.Log("Starting quest " + stateData.CurrentActiveQuest.ID);
    }

    mMatchStart = true;

    StopSwitchLootPrizeCoroutines();

    // Track data for Scopely.
    // Looks pvp match is not start from here. So send information as PVE match.
    WPAnalyticsManager.Instance.SetParametersForBattleDataTrack(OpponentTalentScore);
    if(Quest != null && Quest.Flags != null && Quest.Flags.Count > 0) {
      WPAnalyticsManager.Instance.SetFlagParametersForBattleDataTrack(new FlagManager(Quest.Flags), false);
    }

    mInTransition = true;

    TFUtils.MyTime(true);
    ABManager.Instance.PreloadAssetBundles();
    if(!isFactionFeud) {
      if (Quest != null && Quest.Chapter != null && Quest.Chapter.IsTicketedEventR46) {
        battleType = (int)BattleConnectionManager_R50.BattleType.Stipulation;
      } else if (isRoadBoss) {
        battleType = (int)BattleConnectionManager_R50.BattleType.RoadBoss;
      } else {
        battleType = (int)BattleConnectionManager_R50.BattleType.Pve;
      }
      LoadBattleScene();
    }
    else {
      battleType = (int)BattleConnectionManager_R50.BattleType.Factionfued;
      sLoadBattleSceneFF();
    }
    
  }

  private void WarningcancelCallback() {
  }

  private void StartSwitchLootPrizeCoroutines() {
    for(int i = 0; i < mLootRewardLists.Length; i++) {
      if(mLootRewardLists[i].Count > 1)
        mLootPrizeCoroutines.Add(StartCoroutine(SwitchLootPrize(mLootRewardLists[i], 1.5f * (float)i)));
    }
  }

  private void StopSwitchLootPrizeCoroutines() {

    foreach(Coroutine co in mLootPrizeCoroutines)
      StopCoroutine(co);

    mLootPrizeCoroutines.Clear();
  }

  public void ConfirmWrestlerHealthFF(List<object> wrestler_list) {
    if(this.isFactionFeud) {
      TFUtils.LogWarning("SCS: Start Match Confirm HP");

      for(int ii = 0; ii < wrestler_list.Count; ii++) {
        Dictionary<string, object> dict = (Dictionary<string, object>)wrestler_list[ii];
        string id = TFUtils.LoadString(dict, "ID");
        long hp = PlayerInfoScript.Instance.StateData.PlayerWrestlers[ii].CurrentHP;
        if(dict.ContainsKey("CurrentHP")) {
          hp = TFUtils.LoadInt(dict, "CurrentHP");
        }

        if(id == PlayerInfoScript.Instance.StateData.PlayerWrestlers[ii].Form.ID) {
          PlayerInfoScript.Instance.StateData.PlayerWrestlers[ii].CurrentHP = hp;
          TFUtils.LogWarning("SCS: Confirm HP: " + hp);
        }
      }
    }
  }

  //TODO when store hooked up
  /*
  private void PurchaseSlotsAndTryAgain()
  {
    PlayerSaveData saveData = PlayerInfoScript.Instance.SaveData;
    saveData.ConsumeHardCurrency(MiscParams.Instance.InventorySpacePurchaseCost, "inventory space");
    saveData.AddEmptyInventorySlots(MiscParams.Instance.InventorySpacePerPurchase);
    
    BuyInventoryPopupController.Instance.Show(OnClickPlay);
  }
  
  private void RefillStaminaAndTryAgain()
  {
    PlayerInfoScript.Instance.SaveData.ConsumeHardCurrency(MiscParams.Instance.StaminaRefillCost, "stamina refill");
    StaminaManager.Instance.RefillStamina();
    
    BuyStaminaPopupController.Instance.Show(OnClickPlay);
  }
  */

  public static void MatchStartErrorHandle() {
    // Stuff messed up, so go back
    BottomMenuController.Instance.BackToHomeMenu();
  }

  public static string MatchStartError(int errorCode, string book_id, int difficulty, string league_id, string quest_id) {
    switch(errorCode) {
      case (int)WhiplashErrorCode.PVEPROGRESSQUESTLOCKED:
        return "Quest " + book_id + " " + difficulty + " " + league_id + " " + quest_id + " is locked";
      default:
        string ret = "Error " + errorCode;
#if !KFF_RELEASE
        ret += " " + ICMultiplayer.TranslateErrorCode(errorCode);
#endif
        return ret;
    }
  }

  public void sLoadBattleScene(QuestData quest) {
    if (quest != null && quest.Chapter != null && quest.Chapter.IsTicketedEventR46) {
      WPAnalyticsManager.Instance.InitDataTrackForTicketedBattle();
    }
    var currentWrestler = PlayerInfoScript.Instance.SaveData.ResourcesData.GetCurrentWrestler();

    if(WPGame_R50.GetPlayerWrestlerCount() == 1) {
      PlayerInfoScript.Instance.StateData.PlayerWrestlers[0] = currentWrestler;
    } else {
      currentWrestler = PlayerInfoScript.Instance.StateData.PlayerWrestlers[0];
      PlayerInfoScript.Instance.SaveData.ResourcesData.SetCurrentWrestler(currentWrestler);
    }

    if(quest.MatchType == SpecialMatchType.Single) {
      for(int i = 1; i < PlayerInfoScript.Instance.StateData.PlayerWrestlers.Length; i++)
        PlayerInfoScript.Instance.StateData.PlayerWrestlers[i] = PlayerInfoScript.Instance.StateData.OpponentWrestlers[i] = null;

    }
    if(quest.MatchType == SpecialMatchType.TagTeam) {
      //TODO for now auto-assign players if null
      if(PlayerInfoScript.Instance.StateData.PlayerWrestlers[1] == null) {      
        List<WrestlerItem> wrestlers = PlayerInfoScript.Instance.SaveData.ResourcesData.Wrestlers;
        foreach(var wrestler in wrestlers) {
          //R25: check SGSS
          if(wrestler.Form.isSGSS && !wrestler.Form.CanBeAccessedByPlayer) {
            continue;
          }

          if(wrestler.Form.ID != currentWrestler.Form.ID) {
            PlayerInfoScript.Instance.StateData.PlayerWrestlers[1] = wrestler;
            break;
          }
        }
      }
      PlayerInfoScript.Instance.StateData.PlayerWrestlers[2] = PlayerInfoScript.Instance.StateData.OpponentWrestlers[2] = null;
    } else if(quest.MatchType == SpecialMatchType.TripleThreat || quest.MatchType == SpecialMatchType.FatalFourWay) {
      PlayerInfoScript.Instance.StateData.PlayerWrestlers[1] = null;
      PlayerInfoScript.Instance.StateData.PlayerWrestlers[2] = null;
      if(quest.MatchType == SpecialMatchType.TripleThreat) {
        PlayerInfoScript.Instance.StateData.OpponentWrestlers[2] = null;
      }
    } else if(quest.MatchType == SpecialMatchType.SixMenTagMatch) {
      //TODO for now auto-assign players
      for(int i = 1; i < 3; i++) {
        if(PlayerInfoScript.Instance.StateData.PlayerWrestlers[i] == null) {
          List<WrestlerItem> wrestlers = PlayerInfoScript.Instance.SaveData.ResourcesData.Wrestlers;
          foreach(var wrestler in wrestlers) {
            //R25: check SGSS
            if(wrestler.Form.isSGSS && !wrestler.Form.CanBeAccessedByPlayer) {
              continue;
            }

            var foundWrestler = PlayerInfoScript.Instance.StateData.PlayerWrestlers.Find(m => m.Form?.ID == wrestler.Form.ID);
            if(foundWrestler == null) {
              PlayerInfoScript.Instance.StateData.PlayerWrestlers[i] = wrestler;
              break;
            }
          }
        }
      }
    }

    //KFFSoundPlayer.Instance.PlaySound(StartButtonSound.name);
    //FrontEndWrestlerController.Instance.PlayBattleStartAnimForAll();

    int player_count = WPGame_R50.GetPlayerWrestlerCount();
    long[] hps = new long[player_count];
    List<string> wrestler_ids = new List<string>();
    for(int i = 0; i < player_count; i++) {
      WrestlerItem w = PlayerInfoScript.Instance.SaveData.ResourcesData.GetWrestler(PlayerInfoScript.Instance.StateData.PlayerWrestlers[i].Form.ID);
      if(w != null) {
        hps[i] = w.GetCurrentHP();
        wrestler_ids.Add(w.Form.ID);
      }
    }

    // HACK: this is short term solution. But stop calling StartMatch before complete tutorial.
    if(TutorialController.Instance.IsFTUEComplete() == true) {
      string match_type = "pve";
      // Setup our data for the call

      string league_id = "Default";
      string book_id = "Default";
      int difficulty = 0;

      string quest_id = "Unknown";
      List<string> consumable_ids = new List<string>();
      if(PlayerInfoScript.Instance.StateData.IsPVP) {
        match_type = "pvp";
      } else {
        quest_id = quest.ID;
        if(quest.Chapter != null) {
          league_id = quest.Chapter.ID;
          if(quest.Chapter.BookID != "") {
            book_id = quest.Chapter.BookID;
          }
        }
        difficulty = PlayerInfoScript.Instance.SaveData.PlayerData.GetGameDifficulty();

        // populate the consumables brought into this match, if any.
        ConsumableSelectionManager.CleanSelectedConsumables();
        consumable_ids.AddRange(PlayerInfoScript.Instance.SaveData.PlayerData.SelectedConsumableIDs);

#if !KFF_RELEASE
        DebugMenuController.Instance.ShouldUseConsumablesWithoutServer = false;
#endif
      }

      // TFServerOp op;
      ICMPMessageType messType = ICMPMessageType.START_MATCH;

      if (quest != null && quest.Chapter != null && quest.Chapter.IsTicketedEventR46) {
        messType = ICMPMessageType.START_MATCH_R46;
        ChapterProgress_R46 newChapterProgress = PVEUtils_R46.GetChapterProgressById(quest.Chapter.ID, difficulty);
        List<Dictionary<string, object>> RewardBoostProperties = null;
        if (newChapterProgress != null) {
          RewardBoostProperties = newChapterProgress.RewardBoostProperties;
        }
        BattleConnectionManager_R50.Instance.SetStipulationBattleData(wrestler_ids.ToArray(), difficulty, league_id, quest_id, consumable_ids.ToArray(), RewardBoostProperties);
        battleType = (int)BattleConnectionManager_R50.BattleType.Stipulation;
        PlayerInfoScript.Instance.StateData.isPopulatedEndMatchDataR46 = false;
        PlayerInfoScript.Instance.StateData.lastMatchStarCount = 0;

        //Fix bug WHIP-24199
        SavePlayerProgression();

        InitBattleConnection();
        TFUtils.MyLogError(new object[]{
          "MyLogError1", null, Time.realtimeSinceStartup
        }, go: null, color: "magenta");
        // op = Multiplayer.Multiplayer.StartMatchR46(wrestler_ids.ToArray(), difficulty, league_id, quest_id, consumable_ids.ToArray(), null);
      } else {
        TFUtils.MyLogError(new object[]{
          "MyLogError2", null, Time.realtimeSinceStartup
        }, go: null, color: "magenta");
        battleType = (int)BattleConnectionManager_R50.BattleType.Pve;
        PlayerInfoScript.Instance.StateData.isPopulatedEndMatchData = false;
        PlayerInfoScript.Instance.StateData.lastMatchStarCount = 0;
        InitBattleConnection();
        //op = Multiplayer.Multiplayer.StartMatch(null, wrestler_ids.ToArray(), match_type, book_id, difficulty, league_id, quest_id, consumable_ids.ToArray(), null);
      }

      PlayerInfoScript.Instance.SaveWP();
    } else {
      // In tutorial, ignore StartMatch. So, copied start sequence from above.
      //      ScreenFadeController.Instance.ShowBlackFade(LoadBattleSceneCallBackInFTUE());
      //      PlayerInfoScript.Instance.SaveWPForTutorialBlockComplete(PVEPrepScreenController.StartBattleCallbackInFTUE);

      // Set quest star count 0, as played flag. Not new quest any more.
      {
        // This is PVE.
        PlayerSaveData sd = PlayerInfoScript.Instance.SaveData;
        int diff = sd.PlayerData.SelectedDifficulty;
        WPPVEProgressionData prog = sd.PVEProgressionData;

        // Set 0 star as already played status.
        prog.AddQuestToCompList(diff, PlayerInfoScript.Instance.StateData.CurrentActiveQuest, 0);

        prog.RemoveQuestFromNewList(diff, PlayerInfoScript.Instance.StateData.CurrentActiveQuest, WPPVEProgressionData.NEW_LABEL_KEY);
        prog.RemoveLeagueFromNewList(diff, PlayerInfoScript.Instance.StateData.CurrentActiveQuest.Chapter, WPPVEProgressionData.NEW_LABEL_KEY);
      }

      //TODO for now back to map
      SceneFlowManager.Instance.SetReturnLocation(SceneFlowManager.ReturnLocation.Map);
      PlayerInfoScript.Instance.SaveData.PVEProgressionData.LastActiveQuest = PlayerInfoScript.Instance.StateData.CurrentActiveQuest.ID;
      PlayerInfoScript.Instance.SaveData.PVEProgressionData.SetPVENextQuest(PlayerInfoScript.Instance.StateData.CurrentActiveQuest);      
    }

    // Recover 
    for(int i = 0; i < player_count; i++) {
      if(PlayerInfoScript.Instance.StateData.PlayerWrestlers[i] != null) {
        PlayerInfoScript.Instance.StateData.PlayerWrestlers[i].SetCurrentHP(hps[i]);
        //        PlayerInfoScript.Instance.StateData.PlayerWrestlers[i].CurrentHP  =  PlayerInfoScript.Instance.StateData.PlayerWrestlers[i].GetCurrentHP();
      }
    }

  }

  public void sLoadBattleSceneFF() {
    var currentWrestler = PlayerInfoScript.Instance.SaveData.ResourcesData.GetCurrentWrestler();

    PlayerInfoScript.Instance.StateData.PlayerWrestlers[0] = currentWrestler;

    for(int i = 1; i < PlayerInfoScript.Instance.StateData.PlayerWrestlers.Length; i++)
      PlayerInfoScript.Instance.StateData.PlayerWrestlers[i] = null;

    int player_count = WPGame_R50.GetPlayerWrestlerCount(SpecialMatchType.Single);
    long[] hps = new long[player_count];

    List<string> wrestler_ids = new List<string>();
    for(int i = 0; i < player_count; i++) {
      WrestlerItem wi = PlayerInfoScript.Instance.StateData.PlayerWrestlers[i];
      if(wi == null || wi.Form == null) {
        continue;
      }
      WrestlerItem w = PlayerInfoScript.Instance.SaveData.ResourcesData.GetWrestler(wi.Form.ID);
      if(w != null && w.Form != null) {
        hps[i] = w.GetCurrentHP();
        wrestler_ids.Add(w.Form.ID);
      }
    }

    // HACK: this is short term solution. But stop calling StartMatch before complete tutorial.
    if (TutorialController.HasInstance && TutorialController.Instance.IsFTUEComplete() == true) {
      List<string> consumable_ids = new List<string>();
      string match_type = "ff";
      string league_id = "Default";
      string book_id = "Default";
      int difficulty = 0;

      string quest_id = "Unknown";

      // populate the consumables brought into this match, if any.
      ConsumableSelectionManager.CleanSelectedConsumables();
      consumable_ids.AddRange(PlayerInfoScript.Instance.SaveData.PlayerData.SelectedConsumableIDs);

#if !KFF_RELEASE
      DebugMenuController.Instance.ShouldUseConsumablesWithoutServer = false;
#endif

      WPOpponentData selectedOpponent = new WPOpponentData();
      WrestlerItem wrestler = PlayerInfoScript.Instance.StateData.OpponentWrestler;

      if(wrestler != null && wrestler.Form != null) {
        // TEMP(?)
        selectedOpponent.currentLeagueName = "Faction Feud";
        selectedOpponent.talent = wrestler.Talent;
        selectedOpponent.ID = wrestler.Form.ID;
        selectedOpponent.numWrestlers = 1;
        selectedOpponent.playerName = "Faction Feud";
        selectedOpponent.eloLoseDelta = 100;
        selectedOpponent.eloWinDelta = 100;
        selectedOpponent.wrestlerData = new WPOpponentWrestlerData[1];

        WPOpponentWrestlerData tempData = new WPOpponentWrestlerData();
        tempData.ID = wrestler.Form.ID;
        tempData.level = wrestler.Level;

        if(wrestler.AbilitySet != null) {
          tempData.cards = new WPOpponentItemInfo[wrestler.AbilitySet.Length];
          for(int k = 0; k < tempData.cards.Length; k++) {
            if(wrestler.AbilitySet[k] != null && wrestler.AbilitySet[k].Form != null) {
              WPOpponentItemInfo cardInfo = new WPOpponentItemInfo {
                ID = wrestler.AbilitySet[k].Form.ID,
                level = wrestler.AbilitySet[k].AbilityLevel
              };
              tempData.cards[k] = cardInfo;
            }
          }
        }
        //badges
        if(wrestler.BadgeSet != null) {
          tempData.badges = new WPOpponentItemInfo[wrestler.BadgeSet.Length];
          for(int k = 0; k < tempData.badges.Length; k++) {
            if(wrestler.BadgeSet[k] != null && wrestler.BadgeSet[k].Form != null) {
              WPOpponentItemInfo badgeInfo = new WPOpponentItemInfo {
                ID = wrestler.BadgeSet[k].Form.ID,
                level = wrestler.BadgeSet[k].BadgeLevel
              };
              tempData.badges[k] = badgeInfo;
            }
          }
        }

        selectedOpponent.wrestlerData[0] = tempData;
      }
      battleType = (int)BattleConnectionManager_R50.BattleType.Pve;
      PlayerInfoScript.Instance.StateData.isPopulatedEndMatchData = false;
      PlayerInfoScript.Instance.StateData.lastMatchStarCount = 0;
      InitBattleConnection();
      // TFServerOp op = Multiplayer.Multiplayer.StartMatch(null, wrestler_ids.ToArray(), match_type, book_id, difficulty, league_id, quest_id, consumable_ids.ToArray(), null);
      // ICMultiplayer.Instance.BlockTillMessage(ICMPMessageType.START_MATCH, (messageType) => {
      //   ICMessageResponse response = ICMultiplayer.Instance.GetMessageResponse(messageType);

      //   if(response == null || response.errorCode < 0) {
      //     SimplePopupController.Instance.ShowServerErrorMessage(
      //       KFFLocalization.Get("!!ERROR_MATCH_START"),
      //       MatchStartError(response != null ? response.errorCode : 0, "", -1, "", ""),
      //       response.errorCode.ToString(),
      //       MatchStartErrorHandle);

      //     return true;
      //   }

      //   SpecialLoadBattle();

      //   return true;
      // }, op);
      // op.Execute();

      PlayerInfoScript.Instance.SaveWP();
    }

    // Recover 
    for(int i = 0; i < player_count; i++) {
      if(PlayerInfoScript.Instance.StateData.PlayerWrestlers[i] != null) {
        PlayerInfoScript.Instance.StateData.PlayerWrestlers[i].SetCurrentHP(hps[i]);
      }
    }
  }

  //Fix WHIP-18321: fake old screen fade to avoid weird bug
  private static async void SpecialLoadBattle() {
    // ScreenFadeController.Instance.ShowBlackFade(delegate () {
    //   InputLock.ForceUnlock();
    //   SceneFlowManager.Instance.LoadBattleScene();
    // });
    await UniTask.Delay(500);
    InputLock.ForceUnlock();
    SceneFlowManager.Instance.LoadBattleScene();
  }

  public void LoadBattleSceneSequenceInFTUE(string _ftue_block_id) {
    StartCoroutine(StartBattleSequenceInFTUE(_ftue_block_id));
  }

  IEnumerator StartBattleSequenceInFTUE(string _ftue_block_id) {
    while(TutorialController.Instance.IsBlockComplete(_ftue_block_id) == false)
      yield return null;

    while(ICMultiplayer.Instance.IsBlocked() == true)
      yield return null;

    InputLock.ForceUnlock();
    SceneFlowManager.Instance.LoadBattleScene();
  }

  // TODO: start the MP match
  public void LoadBattleScene() {
    if (battleType == (int)BattleConnectionManager_R50.BattleType.Pve) {
      PlayerInfoScript.Instance.StateData.isPopulatedEndMatchData = false;
      PlayerInfoScript.Instance.StateData.lastMatchStarCount = 0;
      //Fix bug WHIP-24199
      SavePlayerProgression();
      InitBattleConnection();
    } else if (battleType == (int)BattleConnectionManager_R50.BattleType.Stipulation) {
      sLoadBattleScene(Quest);
    } else if (battleType == (int) BattleConnectionManager_R50.BattleType.RoadBoss) {
      InitBattleConnection();
    } else if (battleType == (int) BattleConnectionManager_R50.BattleType.BossMode) {
      InitBattleConnection();
    }
  }

  void SavePlayerProgression() {
    if (TutorialController.Instance.IsFTUEComplete()) {
      PlayerSaveData psd = PlayerInfoScript.Instance.SaveData;
      GameStateData gsd = PlayerInfoScript.Instance.StateData;
      int diff = psd.PlayerData.SelectedDifficulty;
      WPPVEProgressionData prog = psd.PVEProgressionData;
      // Set 0 star as already played status.
      if (prog != null) {
        prog.AddQuestToCompList(diff, gsd.CurrentActiveQuest, 0);
        prog.RemoveQuestFromNewList(diff, gsd.CurrentActiveQuest, WPPVEProgressionData.NEW_LABEL_KEY);
        if (gsd.CurrentActiveQuest != null) {
          prog.RemoveLeagueFromNewList(diff, gsd.CurrentActiveQuest.Chapter, WPPVEProgressionData.NEW_LABEL_KEY);
        }
      }

      //PVE progression data should not be updated when PVP because it only tracks PVE.
      if (!gsd.IsPVP) {
        if (gsd.CurrentActiveQuest != null) {
          psd.PVEProgressionData.LastActiveQuest = gsd.CurrentActiveQuest.ID;
          PlayerInfoScript.Instance.SaveData.PVEProgressionData.SetPVENextQuest(PlayerInfoScript.Instance.StateData.CurrentActiveQuest);     
          if (TutorialController.Instance.IsFTUEComplete() && gsd.CurrentActiveQuest.Chapter.QuestLine == QuestLineEnum.Main && gsd.CurrentActiveQuest.Chapter.isChallengeChapter == false) {
            psd.PVEProgressionData.LastActiveQuestMain = gsd.CurrentActiveQuest.ID;
          }
        }
      }
    }
    PlayerInfoScript.Instance.SaveWP();
  }

  [Header("TugOfWarController")]
  //COPIED FROM TugOfWarController
  public Scrollbar TugOfWarScrollBar;
  public Image TugOfWarFillBar;
  public float DifficultyEasyThreshold = 0.7f;
  public float DifficultyHardThreshold = 0.3f;
  public Text DifficultyLabel;

  private float mDestPosition = 0.5f;

  private void RefreshTugOfWarDisplay() {
    if(mPlayerWrestlerList == null)
      mPlayerWrestlerList = new List<WrestlerItem>();

    var p1 = PlayerInfoScript.Instance.SaveData.ResourcesData.GetCurrentWrestler();
    var p2 = PlayerInfoScript.Instance.StateData.OpponentWrestlers[0];

    float hp1 = (p1 == null) ? 0 : p1.GetCurrentHP();
    float hp2 = (p2 == null) ? 0 : p2.GetStat(WrestlerStat.HP);

    mPlayerWrestlerList.Clear();
    mPlayerWrestlerList.Add(p1);

    //    if ( mHealth1v1[0] != null )
    //      mHealth1v1[0].text = string.Format("{0} / {1}", hp1, p1.HP);

    mDestPosition = hp1 / (float)(hp1 + hp2);

    if(TugOfWarScrollBar != null) {
      TugOfWarScrollBar.value = mDestPosition;
    }
    if(TugOfWarFillBar != null) {
      TugOfWarFillBar.fillAmount = mDestPosition;
    }

    SetDifficulty();
  }

  private void SetDifficulty() {
    if(DifficultyLabel == null)
      return;

    if(mDestPosition >= DifficultyEasyThreshold)
      DifficultyLabel.text = KFFLocalization.Get("!!MODE_NORMAL");
    else if(mDestPosition <= DifficultyHardThreshold)
      DifficultyLabel.text = KFFLocalization.Get("!!MODE_HELL");
    else
      DifficultyLabel.text = KFFLocalization.Get("!!MODE_HARD");

    Color color = Color.Lerp(Color.red, Color.green, mDestPosition);
    DifficultyLabel.color = color;
  }
  //COPIED from WPGame
  public static WrestlerItem BuildOpponentWrestler(QuestLoadoutData loadout, double hpPercentage = 1) {
    //string wrestlerID = qLoadoutData.WrestlerID;
    WrestlerItem resultWrestler = new WrestlerItem(loadout, hpPercentage);

    return resultWrestler;
  }

  public int SelectedSlot;
  public static string CHAPTERR46_KEY = "EnforcerChapterKey";
  public void OnBookButtonPressed(int slot) {
    if(mInTransition || PlayerInfoScript.Instance.StateData.IsPVP)
      return;

    mInTransition = true;

    SelectedSlot = slot; //store the last clicked BookButton to tell which slot to book

    // Get Wrestler Group ID who need to exclude.
    WrestlerItem exclude_ss = GetWrestlerGroupToExclude(GetWrestlerListForPrep(), slot);

    int index = slot - 1;

    RosterInventoryController.MenuMode mode;
    if (!isFactionFeud) {
      if (isRoadBoss) {
        mode = RosterInventoryController.MenuMode.WrestlerSelect;
      } else {
        mode = (Quest.Chapter.IsFightCard ? RosterInventoryController.MenuMode.FightCard : RosterInventoryController.MenuMode.WrestlerSelect);
      }
    }
    else
      mode = RosterInventoryController.MenuMode.FactionFeud;

    ScreenInfo info = new ScreenInfo();    
    info.Add("Mode", mode.ToString());
    info.Add("ExcludeSS", exclude_ss);
    info.Add("PvEPrepSlotSS", slot);
    
    //only use for select SS from pre-match screen of PvE ticketed R46 mode 
    if (m_Quest != null && m_Quest.Chapter != null && m_Quest.Chapter.IsTicketedEventR46 && newChapterDataR46 != null && mode == RosterInventoryController.MenuMode.WrestlerSelect) {
      info.Add(CHAPTERR46_KEY, newChapterDataR46);
    }

    if(Quest != null && Quest.QuestRequirements != null && Quest.QuestRequirements.Length > index) {
      info.Add("RequiredSS", Quest.QuestRequirements[slot - 1]);

      // Check requirement. If this is for Trainer, Check required trainer count.
      string str = Quest.QuestRequirements[slot - 1];
      int trainer_required = TrainerDataManager.GetRequiredTrainerCountForMatch(str);

      if(trainer_required > 0) {
        bool has_ss = false;
        // Received correct value.
        // Check, if the user owned at least one required ss.
        int minRarityTierLevel = int.MaxValue;
        List<WrestlerItem> wrestlers = PlayerInfoScript.Instance.SaveData.ResourcesData.Wrestlers;
        foreach(WrestlerItem w in wrestlers) {
          if(w != null && w.Form != null) {
            if(w.Form.GetAvailableEntourageSlot() >= trainer_required) {
              has_ss = true;
              break;
            } else {
              if(minRarityTierLevel > w.Form.GetRarityTierLevel()) {
                minRarityTierLevel = w.Form.GetRarityTierLevel();
              }
            }
          }
        }

        if(has_ss == false) {
          int rarity = WrestlerDataManager.GetRarityFormRarityTierLevel(minRarityTierLevel);
          WrestlerTier tier = WrestlerDataManager.GetTierFormRarityTierLevel(minRarityTierLevel);
          // There is no ss that can meet trainer requirement.
          string ss_info = KFFLocalization.Get("!!ROSTER_ENHANCE_RARITY_INFO");
          ss_info = ss_info.Replace("<star_count>", rarity.ToString());
          ss_info = ss_info.Replace("<tier>", WrestlerTIerExtensions.ToLocalizedString(tier));

          string body = KFFLocalization.Get("!!TRAINER_REQUIRED_WARNING_BODY");
          //          body = "You need <ss_req> to equip a Trainer.\n\nYou might find one in Loot or you can Level Up and Evo a 1-Star Superstar!";

          body = body.Replace("<ss_req>", ss_info);
          SimplePopupController.Instance.ShowMessage(
            KFFLocalization.Get("!!TRAINER_REQUIRED_WARNING"), body,
            TrainerWarning_Callback, KFFLocalization.Get("!!BUTTON_OK"));

          // Show warning message.
          mInTransition = false;
          return;
        }
      }
    }

    List<WrestlerItem> roadBossMatchRequirementWrestlerList = new List<WrestlerItem>();
    RoadBossSlotRequirement roadBossSlotRequirement = null;
    if (isRoadBoss && _roadBossEventNodeData != null) {
      roadBossMatchRequirementWrestlerList = _roadBossEventNodeData.GetListWrestlerForRequirementSlot(slot);
      roadBossSlotRequirement = _roadBossEventNodeData.GetSelectedSlotRequirementByIndex(slot);
    }

    info.Add("PlayerWrestlerCount", mPlayerWrestlerCount);
    info.Add("OppWrestlerCount", mOppWrestlerCount);

    info.Add(RosterInventoryController.infoWrestlers, mWrestlerExclusiveSelects);
    info.Add(RosterInventoryController.infoWrestlersListRoadBoss, roadBossMatchRequirementWrestlerList);
    info.Add(RosterInventoryController.infoRoadBossSlotRequirement, roadBossSlotRequirement);
    info.Add(RosterInventoryController.infoRoadBossEventNodeData, _roadBossEventNodeData);
    info.Add(RosterInventoryController.slotRoadBoss, slot);

    if(PlayerInfoScript.Instance.StateData.PlayerWrestlers.Length > index) {
      if(PlayerInfoScript.Instance.StateData.PlayerWrestlers[index] != null)
        info.Add("Current", PlayerInfoScript.Instance.StateData.PlayerWrestlers[index].Form.ID);
    }

    if(this.isPVE) {
      if(PlayerInfoScript.Instance.StateData.CurrentActiveQuest.MatchType == SpecialMatchType.Single) {
        info.Add(RosterInventoryController.infoGenderRequire, PlayerInfoScript.Instance.StateData.CurrentActiveQuest.SetLoadout.WrestlerGender);
      } else if(PlayerInfoScript.Instance.StateData.CurrentActiveQuest.MatchType == SpecialMatchType.TagTeam) {
        info.Add(RosterInventoryController.infoGenderRequire, PlayerInfoScript.Instance.StateData.CurrentActiveQuest.SetLoadouts[index].WrestlerGender);
      }
    }

    if(this.isFactionFeud) {
      info.Add(RosterInventoryController.infoGenderRequire, FactionFeudManager.Instance.curFactionFeud.GetFactionFeudGenderType());
    }

    UIManager.Instance.QueuePush(("frontEnd_roster_default"), info);
  }

  //R24
  public void OnFlagButtonPressed(int slot) {
    ScreenInfo info = new ScreenInfo();
    info.Add("Mode", MenuType.FlagSlotMain);
    UIManager.Instance.QueuePush("frontEnd_flag_slot_main", info);
  }

  private void TrainerWarning_Callback() {
  }

  public WrestlerItem GetCurrentExcludedSS() {
    return GetWrestlerGroupToExclude(GetWrestlerListForPrep(), SelectedSlot);
  }

  public string[] GetCurrentRequirement() {
    int index = SelectedSlot - 1;

    if(Quest != null && Quest.QuestRequirements != null && Quest.QuestRequirements.Length > index)
    if(Quest.QuestRequirements[index] != null)
      return new string[] { Quest.QuestRequirements[index] };

    return null;
  }

  public string GetCurrentSSID() {
    int index = SelectedSlot - 1;

    if(PlayerInfoScript.Instance.StateData.PlayerWrestlers.Length > index) {
      if(PlayerInfoScript.Instance.StateData.PlayerWrestlers[index] != null)
        return PlayerInfoScript.Instance.StateData.PlayerWrestlers[index].Form.ID;
    }

    return null;
  }

  public void OnRPSInfoPressed() {
    this.PreventTweens = true;
    UIManager.Instance.QueuePush(("all_rps_popup"));
  }

  public void WrestlerSelectBannerClosed() {
    //    mWrestlerSelectBanner = null;
  }

  public void OnClickManage1v1() {
    NullifyItemData nullifyItemData = null;
    if (!string.IsNullOrEmpty(nullifyDataID)) {
      nullifyItemData = NullifyDataManager.Instance.GetData(nullifyDataID);
    }
    slotScripts[1].OnClickInfoButton(0, nullifyItemData);
  }

  public void OnClickManage2v2Slot1() {
    NullifyItemData nullifyItemData = null;
    if (!string.IsNullOrEmpty(nullifyDataID)) {
      nullifyItemData = NullifyDataManager.Instance.GetData(nullifyDataID);
    }
    slotScripts[2].OnClickInfoButton(0, nullifyItemData);
  }

  public void OnClickManage2v2Slot2() {
    NullifyItemData nullifyItemData = null;
    if (!string.IsNullOrEmpty(nullifyDataID)) {
      nullifyItemData = NullifyDataManager.Instance.GetData(nullifyDataID);
    }
    slotScripts[3].OnClickInfoButton(1, nullifyItemData);
  }

  public void OnClickConsumables() {
    UIManager.Instance.QueuePush(("frontEnd_consumables_slot"));
  }

  public void OnClickFactionFeudLineup() {
    ShowFactionFeudOppSelection();
  }

  public void OnClickFactionFeudPrev() {
    SelectNextPrevFeudWrestler(false);
  }

  public void OnClickFactionFeudNext() {
    SelectNextPrevFeudWrestler(true);
  }

  private void SelectNextPrevFeudWrestler(bool isNext) {
    if(FactionFeudManager.Instance.curOpponentMatchup != null) {
      string memberID = FactionFeudManager.Instance.curOpponentMatchup.GetMemberIDClaimedByMemberID(PlayerInfoScript.Instance.SaveData.PlayerData.ID);
      FactionFeudMember member = FactionFeudManager.Instance.curOpponentMatchup.GetMemberByID(memberID);
      if(member != null) {
        WrestlerItem nextAvailableWrestler = null;
        nextAvailableWrestler = member.GetNextPrevAvailableWrestler(PlayerInfoScript.Instance.StateData.OpponentWrestler, isNext);

        if(nextAvailableWrestler == null) {
          // TODO a toast that no other wrestlers are available
          return;
        }

        // TODO animation
        PopulateFFPrep(nextAvailableWrestler);
      }
    }
  }

  public void OnClickBack() {
    if (TopMenuTitleBarController.HasInstance) {
      //Hide Gem Info
      TopMenuTitleBarController.Instance.isShowGemInfoRoot = false;
      // xuyen.nguyenthi added to fix the bug WHIP-3565 Map - The Difficulty Bar on top of the Quests Map is displayed incorrectly after going back from a node with specific game rule.
      TopMenuTitleBarController.Instance.ActiveTitleBarSpecialRulesTrigger(false);
    }

    if(mInTransition == true || (UIManager.HasInstance && (UIManager.Instance.isScreenBusy || UIManager.Instance.QueueCount > 0)))
      return;

    if(!PlayerInfoScript.Instance.StateData.IsPVP) {
      mInTransition = true;

      //GetComponent<MenuBaseScript>().BackToAboveLayerMenu();

      if(!isFactionFeud) {
        PVEQuestInfoTileScript screen = UIManager.Instance.TryGetScreen<PVEQuestInfoTileScript>();
        if(screen != null)
          screen.ReSelectCurrentSelectedQuest();
        else {
          if (!isRoadBoss && MapCylinderController.HasInstance)
            MapCylinderController.Instance.ReSelectCurrentSelectedQuest(Quest);
        }

        PVESidebarController.ShowSidebar(true, false);
      }

      base.CloseScreen();

      if(currentFactionFeud == null && MapCylinderController.HasInstance && !isRoadBoss)
        MapCylinderController.Instance.MapCam.enabled = true;
    }

    FrontEndWrestlerController.Instance.SwitchWrestlerCam(WrestlerCamMode.None);
  }

  public override void Back() {
    OnClickBack();
  }

  private void EnableMapCanvas(bool enable) {
    if(!isFactionFeud)
      MapCylinderController.Instance.SetActiveFor2DCanvas(enable);
  }

  private void NotifyAfterMatchCancel(bool success, Dictionary<string, object> dict) {
    mInTransition = false;

    if(success)
      StartCoroutine(ShowSimplePopupWithDelay());
  }

  IEnumerator ShowSimplePopupWithDelay() {
    while(SimplePopupController.Instance.HideTween.AnyTweenPlaying())
      yield return null;

    // This rank point takes into account a loss/cancel with pin bonus
    int rank_point_delta = Mathf.Min(ICMultiplayer.LastTournamentPlayerData.elo - ICMultiplayer.lastPVPBattleReward.elo, Mathf.Abs(ICMultiplayer.lastPVPBattleReward.eloDelta));

    //TODO: need to localize this
    string title = KFFLocalization.Get("!!MATCH_LOST");
    string body = KFFLocalization.Get("!!YOU_LOST") + " " + rank_point_delta + " " + KFFLocalization.Get("!!RANK_POINTS");
    if(rank_point_delta == 0) {
      body = KFFLocalization.Get("!!RANK_ZERO_ALREADY");
    }
    SimplePopupController.Instance.ShowMessage(title, body, BackToHome);

  }

  private void BackToHome() {
    //    BottomMenuController.Instance.HidePVPChildMenus();
    //    BottomMenuController.Instance.OnClickButton("Home");

    //StartCoroutine(BackToHomeMenuCoroutine());
    //UIManager.Instance.QueuePop();
    PVPHubController.Instance.SelectedOpponent = null;
    FightModeSelectController screen = UIManager.Instance.TryGetScreen<FightModeSelectController>();
    if(screen != null)
      screen.FocusTweens();
    UIManager.Instance.QueuePopToScreen(typeof(FightModeSelectController));
    //base.CloseScreen();
  }

  IEnumerator BackToHomeMenuCoroutine() {
    MenuType menu = BottomMenuController.Instance.currentMenu;
    while(menu != MenuType.Main) {
      BottomMenuController.Instance.BackToHomeMenu();
      menu = BottomMenuController.Instance.currentMenu;
      yield return new WaitForSeconds(0.05f);
    }

  }

  public void UpdateStartButtonStatus() {
    var StartButtonButton = StartButton.GetComponent<Button>();
    StartButtonButton.interactable = CanGoIntoFight();
    //StartButton.SetActive(CanGoIntoFight());
  }

  private bool CanGoIntoFight() {
    if(!mQuestReqsMet) {
      return false;
    }

    int[] playerCountArray = new int[2];
    playerCountArray[0] = 1; //player count
    playerCountArray[1] = 1; //opponent count
    
    if (isRoadBoss) {
      playerCountArray[0] = 2;
      playerCountArray[1] = 1;
    } else {
      if (!isFactionFeud) {
        if (Quest.MatchType == SpecialMatchType.TagTeam) {
          playerCountArray[0] = playerCountArray[1] = 2;
        } else if (Quest.MatchType == SpecialMatchType.TripleThreat) {
          playerCountArray[1] = 2;
        } else if (Quest.MatchType == SpecialMatchType.FatalFourWay) {
          playerCountArray[1] = 3;
        } else if (Quest.MatchType == SpecialMatchType.SixMenTagMatch) {
          playerCountArray[0] = playerCountArray[1] = 3;
        }
      }
    }
 
    
    for(int i = 0; i < playerCountArray.Length; i++) {
      for(int j = 0; j < playerCountArray[i]; j++) {
        if(i == 0 && PlayerInfoScript.Instance.StateData.PlayerWrestlers[j] == null) {
          return false;
        } else if(i == 1 && PlayerInfoScript.Instance.StateData.OpponentWrestlers[j] == null) {
          return false;
        }
      }
    }

    return true;
  }

  private string GetMatchTypeLayoutPrefab(SpecialMatchType matchType) {
    string layout = "frontEnd_event_layout1v1";
    switch(matchType) {
      case SpecialMatchType.Single:
        MatchPrepType = PrepType.Match_1v1;
        layout = "frontEnd_event_layout1v1";
        break;
      case SpecialMatchType.TagTeam:
        MatchPrepType = PrepType.Match_2v2;
        layout = "frontEnd_event_layout2v2";
        break;
      case SpecialMatchType.TripleThreat:
        MatchPrepType = PrepType.Match_1v1;
        layout = "frontEnd_event_layout1v1"; // DOES NOT YET EXIST OR DEPRECATED
        break;
      case SpecialMatchType.FatalFourWay:
        MatchPrepType = PrepType.Match_1v1;
        layout = "frontEnd_event_layout1v1"; // DOES NOT YET EXIST OR DEPRECATED
        break;
      case SpecialMatchType.SixMenTagMatch:
        MatchPrepType = PrepType.Match_1v1;
        layout = "frontEnd_event_layout1v1"; // DOES NOT YET EXIST OR DEPRECATED
        break;
      case SpecialMatchType.RoadBoss:
        MatchPrepType = PrepType.Match_2v1;
        layout = "frontEnd_road_boss_layout2v1";
        break;
      default:
        break;
    }
    return layout;
  }

  private void OnMatchCommit(bool success, Dictionary<string, object> dict) {
    if(success) {
      var wrestlers = GetWrestlerListForPrep();
      PopulatePrepPanel(wrestlers);
    }
  }

#region Autoplay

  void SetupAutoplayUI() {
    if (NewbieModularFTUEController.HasInstance && NewbieModularFTUEController.Instance.isModularFTUECompleted == false) {
      return;
    }

    if (TutorialController.Instance.IsBlockComplete(TutorialController.AUTOPLAY_BLOCK_KEY) == false) {
      return;
    }

    if (TutorialController.GetActiveState()?.ID == "Tab_Reactivation_Battle") {
      return;
    }

    var info = BattleModeUtil_R58.GetPlayerAutoPlayInfo();
    if (info.isUnlocked) {
      autoplayNode.gameObject.SetActive(true);
      GameObject go = autoplayNode.InstantiateAsChild(autoplayPrefab);
      var autoplayUI = go.GetComponent<AutoPlayAndSpeedUpUI_R58>();
      go.transform.localScale = new Vector3(1.2f, 1.2f, 1);
      if (autoplayUI != null) {
        autoplayUI.SetUpForAutoplay(info, AutoPlayAndSpeedUpUI_R58.DisplayType.Prematch);
      }
    }
  }
  
#endregion

  public WrestlerItem GetWrestlerGroupToExclude(List<WrestlerItem> wrestlers, int _slot) {
    if(wrestlers.Count > 2) {
      // Tag Match.
      // Player side.
      switch(_slot) {
        case 1:
          if(wrestlers.Count >= 2)
          if(wrestlers[1] != null)
            return wrestlers[1];
          return null;
        case 2:
          if(wrestlers.Count >= 1)
          if(wrestlers[0] != null)
            return wrestlers[0];
          return null;
      }
    }

    return null;
  }

  public WrestlerItem[] GetWrestlerList(bool _isOpponent) {
    List<WrestlerItem> list = new List<WrestlerItem>();

    List<WrestlerItem> ws = GetWrestlerListForPrep();
    if(ws.Count > 2) {
      // Tag Match.
      // Player side.
      if(_isOpponent == false) {
        list.Add(ws[0]);
        list.Add(ws[1]);
      } else {
        list.Add(ws[2]);
        if (!isRoadBoss) {
          list.Add(ws[3]);
        }
      }
    } else {
      // Single Match.
      if(_isOpponent == false)
        list.Add(ws[0]);
      else
        list.Add(ws[1]);
    }

    return list.ToArray();
  }

  public int SetTalentScore(List<WrestlerItem> wrestlers) {
    int talent = 0;
    int runeTalent = 0;
    if(wrestlers.Count > 2) {
      // Tag Match.
      // Player side.
      if (wrestlers[0] != null) {
        talent += wrestlers[0].Talent;
        runeTalent += wrestlers[0].TalentScoreRune;
      }
      if (wrestlers[1] != null) {
        talent += wrestlers[1].Talent;
        runeTalent += wrestlers[1].TalentScoreRune;
      }

      layoutTile.mTalentPlayer.text = talent.ToString();
      if (isRoadBoss) {
        int talentTotal = 0;
        if (layoutTile.mTalentPlayerLeft != null && wrestlers[0] != null) {
          layoutTile.mTalentPlayerLeft.text = wrestlers[0].Talent.ToString();
          talentTotal += wrestlers[0].Talent;
        }
        if (layoutTile.mTalentPlayerRight != null && wrestlers[1] != null) {
          layoutTile.mTalentPlayerRight.text = wrestlers[1].Talent.ToString();
          talentTotal += wrestlers[1].Talent;
        }
        if (layoutTile.mTalentTotal != null) {
          layoutTile.mTalentTotal.text = talentTotal.ToString();
        }
        return 0;
      }
//      layoutTile.runeTalentBoostPlayer.text = runeTalent.ToString();

      // Opponents side.
      talent = 0;
      runeTalent = 0;
      if (wrestlers[2] != null) {
        talent += wrestlers[2].Talent;
        runeTalent += wrestlers[2].TalentScoreRune;
      }
      if (wrestlers[3] != null) {
        talent += wrestlers[3].Talent;
        runeTalent += wrestlers[3].TalentScoreRune;
      }

      layoutTile.mTalentOpp.text = talent.ToString();
//      layoutTile.runeTalentBoostOpp.text = runeTalent.ToString();
    } else {
      // Single Match.

      // Tag Match.
      // Player side.
      if (wrestlers[0] != null) {
        talent += wrestlers[0].Talent;
        runeTalent += wrestlers[0].TalentScoreRune;
      }

      layoutTile.mTalentPlayer.text = talent.ToString();
//      layoutTile.runeTalentBoostPlayer.text = runeTalent.ToString();

      // Opponents side.
      talent = 0;
      if (wrestlers[1] != null) {
        talent += wrestlers[1].Talent;
        runeTalent += wrestlers[1].TalentScoreRune;
      }

      layoutTile.mTalentOpp.text = talent.ToString();
//      layoutTile.runeTalentBoostOpp.text = runeTalent.ToString();
    }

    return talent;
  }

  public void SetBoostCount(WrestlerItem[] wrestlers, bool isOpp = false, int boostCount = 0) {
    if(boostCount < 1 && isOpp) {
      layoutTile.mTalentOppBoostRoots.SetActive(false);
      return;
    } else if(boostCount < 1 && !isOpp) {
      layoutTile.mTalentPlayerBoostRoots.SetActive(false);
      return;
    }

    int boostIndex = 0;

    if(wrestlers.Length > 1)
      boostIndex = 1;

    if(isOpp) {
      layoutTile.mTalentOppBoostRoots.SetActive(true);
      layoutTile.mTalentOppBoost.text = boostCount.ToString();
    } else {
      layoutTile.mTalentPlayerBoostRoots.SetActive(true);
      layoutTile.mTalentPlayerBoost.text = boostCount.ToString();
    }
    SetCapIcon(wrestlers, isOpp);
  }

  private void SetCapIcon(WrestlerItem[] wrestlers, bool isOpp) {
    var wrestlerList = wrestlers.ToList().Where(e => e != null).ToList();
    if(wrestlerList.Count == 0) {
      return;
    }
    //update wrestler info before cap
    wrestlerList.ForEach(wrestler => {
      wrestler.UpdateWrestlerInfoBeforeCap(isOpp);
    });

    if(isOpp) {
      layoutTile.SetOppCap(wrestlerList.Exists(wrestler => wrestler.effectCapDesDic.Count > 0));
    } else {
      layoutTile.SetPlayerCap(wrestlerList.Exists(wrestler => wrestler.effectCapDesDic.Count > 0));
    }
  }

  // update the rock-paper-scissors FX for all wrestlers (this should be called whenever any wrestlers change)
  public void UpdateRPSFX() {
    for(int i = 0; i < slotScripts.Length; i++) {
      if(slotScripts[i] != null) {
        StartCoroutine(slotScripts[i].UpdateRPSFXSequence());
      }
    }
  }

  //R42
  [SerializeField] 
  private GameObject lockedConsumableUI;
  public void OnClickConsumablesLock() {
    if (!string.IsNullOrEmpty(GearSessionManager.Instance.SCSUserAttributes.BundleOfferID)) {
      var offerData = OffersDataManager.Instance.GetData(GearSessionManager.Instance.SCSUserAttributes.BundleOfferID);
      if (offerData != null ) {
        offerData.ShowBundleOffer(PlayerInfoScript.Instance.SaveData.ResourcesData.PlayerLeagueManager.GetFeatureUnlockLevel(FeatureGating.Props),ScreenCategory.battle_prematch,"battle_props_locked_feature", isShowOffer => {
          if (!isShowOffer) {
            ShowToolTipProp();
          }
        });
      } else {
        ShowToolTipProp();
      }
    } else {
      ShowToolTipProp();
    }
  }

  /// <summary>
  /// Show Prop Tooltip
  /// </summary>
  void ShowToolTipProp() {
    string info = string.Format(KFFLocalization.Get("!!R42_UNLOCK_AT_LEAGUE"), PlayerInfoScript.Instance.SaveData.ResourcesData.PlayerLeagueManager.GetFeatureUnlockLevel(FeatureGating.Props));
    UIToolTipController.Config config = new UIToolTipController.Config(UIToolTipController.TailDirection.Down, -1, -1, int.MaxValue);
    UIToolTipController.Instance.Show(lockedConsumableUI, config, info);
  }
  
  public void DisplayEquippedConsumables(bool updateSelections = true) {
    if(!TutorialController.Instance.IsFTUEComplete()) {
      return;
    }
    if(!UIManager.Instance.Consumable_r11_Active)
      return;
    PlayerLeagueManager plm = PlayerInfoScript.Instance.SaveData.ResourcesData.PlayerLeagueManager;
    bool isPropsUnlocked = plm.IsFeatureUnlocked(FeatureGating.Props);
    if (!isPropsUnlocked) {
      lockedConsumableUI.SetActive(true);
      return;
    } else {
      lockedConsumableUI.SetActive(false);
    }
    if(updateSelections) {
      //if any consumables were previously exhausted during battle, they need to be cleared when next viewing selections.
      ConsumableSelectionManager.CleanSelectedConsumables();
      ConsumableSelectionManager.AutoEquipConsumables();
    }

    //make sure it has 3 all the time.
    ConsumableSelectionManager.FillSelectionsToMax();

    bool hasConsumables = false;
    var consumables = PlayerInfoScript.Instance.SaveData.PlayerData.SelectedConsumableIDs;
    for(int i = 0; i < consumables.Count; i++) {
      consumablePrefab = UIPrefabs.GetPrefab("frontEnd_consumable_tile");
      var con = ConsumableDataManager.Instance.GetData(consumables[i]);
      if(con != null) {
        if(mConsumableTiles[i] == null) {
          var obj = consumableParents[i].InstantiateAsChild(consumablePrefab);
          Button btn = obj.GetComponent<Button>();
          if(btn)
            btn.interactable = false;
          var tileScript = obj.GetComponent<ConsumableTileScript>();
          mConsumableTiles[i] = tileScript;
        }
        mConsumableTiles[i].gameObject.SetActive(true);
        mConsumableTiles[i].Populate(con, ConsumableTileMode.PrepDisplay);
        hasConsumables = true;
      } else if(mConsumableTiles[i] != null) {
        mConsumableTiles[i].gameObject.SetActive(false);
      }
    }

    consumableEmptyPrompt.SetActive(!hasConsumables);
  }

  public static bool CheckForWarningMessage(WrestlerItem playerSS_1, WrestlerItem playerSS_2, WrestlerItem opponentSS_1, WrestlerItem opponentSS_2, SimplePopupController.PopupButtonCallback callback,
      PVEPrepScreenWrestlerSlot[] wrestlerSlots = null, string nullifyDataID = "") {
    
    if (playerSS_1 == null) {
      return false; // TODO warning to select a superstar
    }

    if(PlayerInfoScript.Instance.StateData.IsFactionFeud)
      return false; // skip all wanring messages

    if (TutorialController.Instance.IsBlockActive(TutorialController.ReactivationBlockChallengeID)) {
      return false; // Skip all warning for tutorial
    }
    
    // STATS CHANGE MOVES
    if (!PlayerInfoScript.Instance.SaveData.PlayerData.DisableWarnings[(int)WPPlayerData.DisableWarningType.StatsChangeMove] && !playerSS_1.InFightCard) {
      bool shouldShowStatsChangeMoveWarning = false;

      if (wrestlerSlots != null) {
        for (int i = 0; i < wrestlerSlots.Length; i++) {
          if (wrestlerSlots[i] != null) {
            shouldShowStatsChangeMoveWarning = shouldShowStatsChangeMoveWarning || wrestlerSlots[i].isShowingChangeMoveWarning;
          }
        }
      }
      
      if (shouldShowStatsChangeMoveWarning) {
        // SHOW POPUP
        SimplePopupController.Instance.ShowWarning(KFFLocalization.Get("!!R67_CHANGE_MOVE_WARNING"),
            KFFLocalization.Get("!!R67_CHANGE_MOVE_WARNING_DESC") + "\n\n" + KFFLocalization.Get("!!CONTINUE_BATTLE_CONFIRM"),
            callback, StatsChangeMoveWarningCancelCallback,
            StatsChangeMoveWarningCallback);

        return true;
      }
    }
    
    // HEALTH
    if(!PlayerInfoScript.Instance.SaveData.PlayerData.DisableWarnings[(int)WPPlayerData.DisableWarningType.Health] && !playerSS_1.InFightCard) {
      float playerHealth = 0;
      float playerHealthMax = 0;

      playerHealth += playerSS_1.CurrentHP;
      playerHealthMax += playerSS_1.HP;

      if(playerSS_2 != null) {
        playerHealth += playerSS_2.CurrentHP;
        playerHealthMax += playerSS_2.HP;
      }

      float deltaHP = playerHealthMax - playerHealth;
      float threshHP = MiscParams.Instance.BattlePrepHealthWarning * playerHealthMax;

      if(deltaHP > threshHP) {
        // SHOW POPUP
        //hide heal warning in road boss mode
        if (!PlayerInfoScript.Instance.StateData.IsRoadBoss) {
          SimplePopupController.Instance.ShowWarning(KFFLocalization.Get("!!BATTLEWARNING_HEALTH"), KFFLocalization.Get("!!BATTLEWARNING_HEALTH_DESC") + "\n\n" + KFFLocalization.Get("!!CONTINUE_BATTLE_CONFIRM"), callback, HealthWarningCancelCallback, HealthWarningCallback);
          return true;
        }
      }
    }

    bool isFTUECompleted = NewbieModularFTUEController.HasInstance && NewbieModularFTUEController.Instance.isModularFTUECompleted;
    // TALENT
    if(!PlayerInfoScript.Instance.SaveData.PlayerData.DisableWarnings[(int)WPPlayerData.DisableWarningType.Talent] && !playerSS_1.InFightCard && isFTUECompleted) {
      float playerTalent = 0;
      float opponentTalent = 0;

      playerTalent += playerSS_1.Talent;

      if(playerSS_2 != null)
        playerTalent += playerSS_2.Talent;

      opponentTalent += opponentSS_1.Talent;

      if(opponentSS_2 != null)
        opponentTalent += opponentSS_2.Talent;

      float deltaT = opponentTalent - playerTalent;
      float threshT = MiscParams.Instance.BattlePrepTalentWarning * opponentTalent;

      if(deltaT > threshT) {
        // SHOW POPUP
        SimplePopupController.Instance.ShowWarning(KFFLocalization.Get("!!BATTLEWARNING_TALENT"), KFFLocalization.Get("!!BATTLEWARNING_TALENT_DESC") + "\n\n" + KFFLocalization.Get("!!CONTINUE_BATTLE_CONFIRM"), callback, TalentWarningCancelCallback, TalentWarningCallback);

        return true;
      }
    }

    // RPS ADVANTAGE
    if(!PlayerInfoScript.Instance.SaveData.PlayerData.DisableWarnings[(int)WPPlayerData.DisableWarningType.Advantage] && !playerSS_1.InFightCard && !isFTUECompleted) {
      string playerClass1 = playerSS_1.GetClassData().ID;
      string playerClass2 = playerSS_2 != null ? playerSS_2.GetClassData().ID : null;
      string opponentClass1 = opponentSS_1.GetClassData().ID;
      string opponentClass2 = opponentSS_2 != null ? opponentSS_2.GetClassData().ID : null;

      if(RPS.IsAtDisadvantage(playerClass1, playerClass2, opponentClass1, opponentClass2)) {
        // SHOW POPUP
        SimplePopupController.Instance.ShowWarning(KFFLocalization.Get("!!BATTLEWARNING_CLASS"), KFFLocalization.Get("!!BATTLEWARNING_CLASS_DESC") + "\n\n" + KFFLocalization.Get("!!CONTINUE_BATTLE_CONFIRM"), callback, AdvantageWarningCancelCallback, AdvantageWarningCallback);

        return true;
      }
    }

    // Trainer DisAdvantage
    if(UIManager.HasInstance) {
      bool isNullifyTrainerBoost = false;
      
      //Check the nullify condition
      if (!string.IsNullOrEmpty(nullifyDataID)) {
        NullifyItemData nullifyItemData = NullifyDataManager.Instance.GetData(nullifyDataID);
        isNullifyTrainerBoost = NullifyDataManager.IsNullifyBoost(nullifyItemData, false, NullifyBuffType.Entourage);
      }
      
      if (!PlayerInfoScript.Instance.SaveData.PlayerData.DisableWarnings[
            (int)WPPlayerData.DisableWarningType.TrainerDisAdvantage]) {
        bool check = false;
        bool warn = false;

        if (playerSS_1 != null) {
          if (playerSS_1.Form.IsTrainerSlotLockedForWrestler(0) == false) {
            check = true;
          }
        }

        if (playerSS_2 != null) {
          if (playerSS_2.Form.IsTrainerSlotLockedForWrestler(0) == false) {
            check = true;
          }
        }

        //if nullify turn on, we skip the warning message
        if (isNullifyTrainerBoost) {
          check = false;
        }

        if (check == true) {
          int opponent_total = 0;

          if (opponentSS_1 != null)
            opponent_total += PVEPrepScreenController.GetBuffCountFromTrainers(opponentSS_1, true);
          if (opponentSS_2 != null)
            opponent_total += PVEPrepScreenController.GetBuffCountFromTrainers(opponentSS_2, true);

          if (opponent_total > 0) {
            if (playerSS_1 != null && PVEPrepScreenController.GetBuffCountFromTrainers(playerSS_1, false) <= 0)
              warn = true;
            if (playerSS_2 != null && PVEPrepScreenController.GetBuffCountFromTrainers(playerSS_2, false) <= 0)
              warn = true;
          }

          if (warn == true) {
            // SHOW POPUP
            SimplePopupController.Instance.ShowWarning(KFFLocalization.Get("!!BATTLEWARNING_TRAINER"),
              KFFLocalization.Get("!!BATTLEWARNING_TRAINER_DESC") + "\n\n" +
              KFFLocalization.Get("!!CONTINUE_BATTLE_CONFIRM"), callback, TrainerDisAdvantageWarningCancelCallback,
              TrainerDisAdvantageWarningCallback);

            return true;
          }
        }
      }
    }

    if (TutorialController.Instance?.IsFTUEComplete() == true &&
        !PlayerInfoScript.Instance.SaveData.PlayerData.DisableWarnings[
          (int) WPPlayerData.DisableWarningType.StartBattleWithoutMentorBuff]) {

      var topScr = UIManager.Instance.TopScreen();
      var iMentorSlotHolderScreen = topScr?.GetComponent<IMentorSlotHolderScreen_R49>();
      var slots = iMentorSlotHolderScreen?.GetSlotsScript();
      if (slots != null) {
        for (int idx = 0; idx < slots.Length; idx++) {
          var slot = slots[idx];
          if (slot != null && !slot.IsOpponent) {
            var mentorSlot = slot.GetMentorSlot();
            if (mentorSlot != null && mentorSlot.IsShowingMentorWarning()) {
              MentorDontShowAgainPopup_R49.Config config = new MentorDontShowAgainPopup_R49.Config();
              config.SetButtonLoc(_left: true, "!!R39_BUTTON_EQUIP")
                    .SetButtonLoc(_left: false, "!!CONTINUE")
                    .SetButtonColor(_left: true, MentorDontShowAgainPopup_R49.ButtonColor.Orange)
                    .SetButtonColor(_left: false, MentorDontShowAgainPopup_R49.ButtonColor.Green)
                    .SetButtonAction(_left: true, () =>
                     {
                       var underTopScr = UIManager.Instance.UnderTopScreen();
                       var iMentorSlotHolderScr = underTopScr?.GetComponent<IMentorSlotHolderScreen_R49>();
                       iMentorSlotHolderScr?.WaitToClosePopupMentorDontShowAgain(ChangeMentorScreen_R49.Show);
                     })
                    .SetButtonAction(_left: false, () => callback?.Invoke())
                    .SetToggleAction((_isOn) =>
                     {
                       PlayerInfoScript.Instance.SaveData.PlayerData.DisableWarnings[
                           (int) WPPlayerData.DisableWarningType.StartBattleWithoutMentorBuff]
                         = _isOn;
                     });
              MentorDontShowAgainPopup_R49.Show(WPPlayerData.DisableWarningType.StartBattleWithoutMentorBuff, config);
              return true;
            }
          }
        }
      }
    }

    return false;
  }

  public static bool CheckToShowCaptcha(SimplePopupController.PopupButtonCallback callback) {
    bool res = false;
    var quest = PlayerInfoScript.Instance.StateData.CurrentActiveQuest;
    //Captcha 
    if (CaptchaPopupController.ShouldVerifyCaptcha(quest)) {
      ScreenInfo info = new ScreenInfo();
      Action<bool> cb = (success) => {
        if (success) {
          callback?.Invoke();
        } else {
          TFUtils.LogError("Verify Captcha FAILURE !!!");
        }
      };
      info.Add(CaptchaPopupController.CaptchaCallbackKey, cb);
      info.Add(CaptchaPopupController.QuestKey, quest);
      UIManager.Instance.QueuePush(CaptchaPopupController.PrefabName, info);
      res = true;
    }
    return res;
  }
  
  private static void HealthWarningCallback(bool isToggledOn) {
    PlayerInfoScript.Instance.SaveData.PlayerData.DisableWarnings[(int)WPPlayerData.DisableWarningType.Health] = isToggledOn;

    TFUtils.LogWarning("SCS: Health is Toggled: " + isToggledOn);
  }

  private static void TalentWarningCallback(bool isToggledOn) {
    PlayerInfoScript.Instance.SaveData.PlayerData.DisableWarnings[(int)WPPlayerData.DisableWarningType.Talent] = isToggledOn;

    TFUtils.LogWarning("SCS: Health is Toggled: " + isToggledOn);
  }

  private static void StatsChangeMoveWarningCallback(bool isToggledOn) {
    PlayerInfoScript.Instance.SaveData.PlayerData.DisableWarnings[(int)WPPlayerData.DisableWarningType.StatsChangeMove] = isToggledOn;
  }
  
  private static void AdvantageWarningCallback(bool isToggledOn) {
    PlayerInfoScript.Instance.SaveData.PlayerData.DisableWarnings[(int)WPPlayerData.DisableWarningType.Advantage] = isToggledOn;
    TFUtils.LogWarning("SCS: Health is Toggled: " + isToggledOn);
  }

  private static void TrainerDisAdvantageWarningCallback(bool isToggledOn) {
    PlayerInfoScript.Instance.SaveData.PlayerData.DisableWarnings[(int)WPPlayerData.DisableWarningType.TrainerDisAdvantage] = isToggledOn;
  }

  private static void HealthWarningCancelCallback() {
    PlayerInfoScript.Instance.SaveData.PlayerData.DisableWarnings[(int)WPPlayerData.DisableWarningType.Health] = false;
  }

  private static void TalentWarningCancelCallback() {
    PlayerInfoScript.Instance.SaveData.PlayerData.DisableWarnings[(int)WPPlayerData.DisableWarningType.Talent] = false;
  }
  
  private static void StatsChangeMoveWarningCancelCallback() {
    PlayerInfoScript.Instance.SaveData.PlayerData.DisableWarnings[(int)WPPlayerData.DisableWarningType.StatsChangeMove] = false;
  }

  private static void AdvantageWarningCancelCallback() {
    PlayerInfoScript.Instance.SaveData.PlayerData.DisableWarnings[(int)WPPlayerData.DisableWarningType.Advantage] = false;
  }

  private static void TrainerDisAdvantageWarningCancelCallback() {
    PlayerInfoScript.Instance.SaveData.PlayerData.DisableWarnings[(int)WPPlayerData.DisableWarningType.TrainerDisAdvantage] = false;
  }

  public static bool CheckForWarningMessage(WrestlerItem playerSS_1, WrestlerItem opponentSS_1, SimplePopupController.PopupButtonCallback callback,
      PVEPrepScreenWrestlerSlot[] wrestlerSlots = null, string nullifyDataID = "") {
    return CheckForWarningMessage(playerSS_1, null, opponentSS_1, null, callback, wrestlerSlots, nullifyDataID);
  }

  public void UpdateLinks() {
    Boost link_boost = LinkBonusController.Instance.GetLinkBoostWithWrestler(PlayerInfoScript.Instance.SaveData.ResourcesData.GetCurrentWrestler(), false);
    Boost enemy_link_boost = LinkBonusController.Instance.GetLinkBoostWithWrestler(PlayerInfoScript.Instance.StateData.OpponentWrestlers[0], true);
    if(LinkArt != null) {
      if(link_boost != null && IsTagTeamBattle() && AreAllWrestlersSelected()) {
        if (link_boost.Icon != null) {
          LinkArt.sprite = link_boost.Icon;
        } else {
          KFFResourceManager.Instance.LoadOnDemandLooseTextureToImage(link_boost.IconODLPath, LinkArt);
        }
        LinkArt.rectTransform.SetHeight(150f);
        LinkArt.rectTransform.localPosition = new Vector3(LinkArt.transform.localPosition.x, -15f, LinkArt.transform.localPosition.z);
        LinkArt.gameObject.SetActive(true);
        WarningImage.SetActive(false);
        // Stop any previously running coroutine for showing the team link VFX
        StopWaitToShowTeamLinkVfx();

        // Start a new coroutine that waits for a certain condition before showing the team link VFX
        // The coroutine is stored in the coroutineWaitToShowTeamLinkVfx variable
        coroutineWaitToShowTeamLinkVfx = StartCoroutine(WaitToShowTeamLinkVFX());

        havingLinkBonus = true;
        if (teamLinkBoostCount != null) {
          teamLinkBoostCount.SetActive(true);
        }
      } else {
        if (teamLinkBoostCount != null) {
          teamLinkBoostCount.SetActive(false);
        }
        
        havingLinkBonus = false;
        if (IsTagTeamBattle() && AreAllWrestlersSelected()) {
          WarningImage.SetActive(true);
          LinkArt.sprite = warningSprite;
          LinkArt.gameObject.SetActive(true);
          SetActiveLink(false);
          LinkArt.rectTransform.SetHeight(55f);
          LinkArt.rectTransform.localPosition = new Vector3(LinkArt.transform.localPosition.x, 35f, LinkArt.transform.localPosition.z);
        } else {
          //Single battle
          LinkArt.gameObject.SetActive(false);
          WarningImage.SetActive(false);
          SetActiveLink(false);
        }
        
        // Stop any previously running coroutine for showing the team link VFX
        StopWaitToShowTeamLinkVfx();
      }      
    }

    if(OpponentLinkArt != null) {
      if(enemy_link_boost != null) {
        if(enemy_link_boost.Icon != null) {
          OpponentLinkArt.sprite = enemy_link_boost.Icon;
        } else {
          KFFResourceManager.Instance.LoadOnDemandLooseTextureToImage(enemy_link_boost.IconODLPath, OpponentLinkArt);
        }
        OpponentLinkArt.gameObject.SetActive(true);
        OpponentLinkArtObject.SetActive(true);
      } else {
        OpponentLinkArt.gameObject.SetActive(false);
        OpponentLinkArtObject.SetActive(false);
      }
    }

    //WPAnalyticsManager.Instance.TrackPrematchScreenInfo(GetPlayerSlots().ToArray(), GetOpponentSlots().ToArray());
  }

  private void SetActiveLink(bool active) {
    foreach (var link in LinkArtObject) {
      link.SetActive(active);
    }
  }

  private IEnumerator WaitToShowTeamLinkVFX() {
    yield return new WaitForSeconds(1.3f);
    SetActiveLink(true);
  }

  private void StopWaitToShowTeamLinkVfx() {
    if (coroutineWaitToShowTeamLinkVfx != null) {
      StopCoroutine(coroutineWaitToShowTeamLinkVfx);
    }
  }

  private void OnWrestlerUpdated(WrestlerItem wrestler) {
    if(mWrestlerExclusiveSelects != null) {
      for(int i = 0; i < mWrestlerExclusiveSelects.Count; i++) {
        if(mWrestlerExclusiveSelects[i] != null && mWrestlerExclusiveSelects[i].Form.ID == wrestler.Form.ID) {
          mWrestlerExclusiveSelects[i] = wrestler;
        }
      }
    }

    if(slotScripts != null) {
      for(int i = 0; i < slotScripts.Length; i++) {
        if(slotScripts[i] == null || slotScripts[i].Wrestler == null)
          continue;

        if (slotScripts[i].Wrestler.Form.ID == wrestler.Form.ID) {
          //Refresh the Slot UI only when the SS has just been updated and matches the owner of the Slot UI.
          bool verifySsOwner = slotScripts[i].IsOpponent ^ wrestler.IsPlayerOwned;
          if (verifySsOwner) {
            slotScripts[i].Populate(wrestler, slotScripts[i].IsOpponent, i);
          }
        }
      }
    }
  }

  public SpecialMatchType GetMatchType() {
    if (isRoadBoss) {
      return SpecialMatchType.RoadBoss;
    }
    if (!isFactionFeud)
      return Quest.MatchType;
    else
      return SpecialMatchType.Single;
  }

  public void RedrawFactionFeudData() {
    if(FactionFeudManager.Instance.curOpponentMatchup == null)
      CloseScreen();

    if(currentFactionFeud != null) {
      // check for state change     
      if(FactionFeudManager.Instance.curState != lastKnownState) {
        CloseScreen();
        return;
      }

      if(FactionFeudManager.Instance.curOpponentMatchup == null) {
        CloseScreen();
        return;
      }
    }

    PopulateFFPrep(null);
  }

  void RememberPreviousWrestlers() {
    if(!PlayerInfoScript.Instance.StateData.PlayerWrestlers.Any(w => w != null)) {
      //nothing to remember
      mPreviousPlayerWrestlers = null;
      return;
    }

    mPreviousPlayerWrestlers = new WrestlerItem[PlayerInfoScript.Instance.StateData.PlayerWrestlers.Length];
    PlayerInfoScript.Instance.StateData.PlayerWrestlers.CopyTo(mPreviousPlayerWrestlers, 0);
  }

  public void ForgetPreviousWrestlers() {
    mPreviousPlayerWrestlers = null;
  }

  void RestorePreviousWrestlers() {
    if(mPreviousPlayerWrestlers == null)
      return;

    PlayerInfoScript.Instance.StateData.PlayerWrestlers = mPreviousPlayerWrestlers;
    mPreviousPlayerWrestlers = null;
  }

  private void UpdateUIMentorSlot() {
    int length = slotScripts.Length;
    var filledWrestler = GetFilledWrestler;
    for (int idx = 0; idx < length; idx++) {
      var slot = slotScripts[idx];
      slot.UpdateUIMentorSlot(filledWrestler);
    }
  }

  private void UpdateCSSBonus(bool _setup, ICSSTour_R44 _iTour = null)
  {
    if (!layoutTile) return;

    if (_setup)
    {
      if (!mCSSBonusPrematchController)
      {
        mCSSBonusPrematchController = layoutTile.GetComponent<UICSSBonusPrematchController_R44>();
      }

      if (mCSSBonusPrematchController)
      {
        mCSSBonusPrematchController.Setup(_iTour, GetFilledWrestler);
      }
    }
    else if (mCSSBonusPrematchController)
    {
      mCSSBonusPrematchController.OnFocusScreen(GetFilledWrestler);
    }
  }

  private WrestlerItem[] GetFilledWrestler
  {
    get
    {
      int count = 0;
      foreach (var slot in slotScripts)
      {
        if (!slot.IsOpponent) ++count;
      }

      WrestlerItem[] wrestlers = new WrestlerItem[count];
      for (int i = 0; i < count; ++i)
      {
        wrestlers[i] = slotScripts.Find(item => item.Slot == i + 1 && !item.IsOpponent).Wrestler;
      }

      return wrestlers;
    }
  }

  public List<WrestlerItem> GetPlayerWrestlers
  {
    get
    {
      int length = slotScripts.Length;
      List<WrestlerItem> result = new List<WrestlerItem>();
      for (int idx = 0; idx < length; ++idx) {
        var slot = slotScripts[idx];
        if (slot && !slot.IsOpponent) {
          result.Add(slot.Wrestler);
        }
      }

      return result;
    }
  }

  public PVEPrepScreenWrestlerSlot[] GetSlotsScript() {
    return slotScripts;
  }

  public List<PVEPrepScreenWrestlerSlot> GetPlayerSlots() {
    List<PVEPrepScreenWrestlerSlot> playerSlots = new List<PVEPrepScreenWrestlerSlot>();
    for (int i = 0; i < slotScripts.Count(); i++) {
      if (!slotScripts[i].IsOpponent) {
        playerSlots.Add(slotScripts[i]);
      }
    }
    return playerSlots;
  }

  public List<PVEPrepScreenWrestlerSlot> GetOpponentSlots() {
    List<PVEPrepScreenWrestlerSlot> opponentSlots = new List<PVEPrepScreenWrestlerSlot>();
    for (int i = 0; i < slotScripts.Count(); i++) {
      if (slotScripts[i].IsOpponent) {
        opponentSlots.Add(slotScripts[i]);
      }
    }
    return opponentSlots;
  }


  public void WaitToClosePopupMentorDontShowAgain(Action _action) {
    StartCoroutine(WaitToClosePopupMentorDontShowAgainIE(_action));
  }

  private IEnumerator WaitToClosePopupMentorDontShowAgainIE(Action _action) {
    while (UIManager.Instance.TopScreen() is MentorDontShowAgainPopup_R49) {
      yield return null;
    }
    
    _action?.Invoke();
  }
  
  public void OnClickHealthImage() {
    InventoryModalsScript.ShowGeneric(healthImage.sprite, KFFLocalization.Get("!!R49_BONUS_HEALTH"), KFFLocalization.Get("!!R49_BONUS_HEALTH_DESCRIPTION"));
  }

  public void OnClickTeamLinkBonusIcon() {
    ScreenInfo info = new ScreenInfo();
    info.Add("wrestler1", layoutTile.playerTeamWrestlers[0].Wrestler);
    info.Add("wrestler2", layoutTile.playerTeamWrestlers[1].Wrestler);
    info.Add("isPVP", false);
    info.Add("PVEController", this);
    UIManager.Instance.QueuePush(TeamLinkBonusPopupController_R60.Prefab_Name, info);
    WPAnalyticsManager.Instance.OnUserSeeTooltipInPrematchScreen(ScreenSubCategory.team_link_tooltip, layoutTile.playerTeamWrestlers, layoutTile.opponentTeamWresters);
  }

  private bool IsTagTeamBattle() {
    if (isRoadBoss) {
      return true;
    }
    return PlayerInfoScript.Instance.StateData.CurrentActiveQuest.MatchType == SpecialMatchType.TagTeam;
  }

  //Only use for check tag team battle
  private bool AreAllWrestlersSelected() {
    //Debug.LogError("Wrestler 1 != null: " + (layoutTile.playerTeamWrestlers[0].Wrestler != null) + " === wrestler 2 != null: " + (layoutTile.playerTeamWrestlers[1].Wrestler != null));
    if (layoutTile.playerTeamWrestlers != null && layoutTile.playerTeamWrestlers.Count() >= 2) {
      return layoutTile.playerTeamWrestlers[0].Wrestler != null && layoutTile.playerTeamWrestlers[1].Wrestler != null;
    }

    return false;    
  }

  public void ShowLoot(Transform parent) {
    LootRoot.transform.SetParent(parent);
    LootRoot.SetActive(true);
  }
  
  public void HideLoot() {
    LootRoot.transform.SetParent(this.transform);
    LootRoot.SetActive(false);
  }
  
  void PopulateUniqueRuleIcon(int gamemodeIndex) {
    string nullifyID = PlayerInfoScript.GetNullifyBaseOnGameMode(gamemodeIndex);
    string statChangeID = PlayerInfoScript.GetStatChangesBaseOnGameMode(gamemodeIndex);
    if (!string.IsNullOrEmpty(nullifyID) || !string.IsNullOrEmpty(statChangeID)) {
      if (uniqueRuleRoot != null) {
        if (uniqueRuleController == null) {
          uniqueRuleController = UIPrefabs.CreateInstance<UniqueRuleButtonController>(uniqueRuleRoot.gameObject, UniqueRuleButtonController.PrefabName);
        }

        if (uniqueRuleController != null) {
          uniqueRuleController.gameObject.SetActive(true);
          uniqueRuleController.Populate(statChangeID, nullifyID);
        }
      }
    }
  }
    
  /// <summary>
  /// Updates the visibility of perk buttons based on the nullify item data.
  /// </summary>
  private void UpdatePerkButtons() {
    bool isPerkUnlocked =
      PlayerInfoScript.Instance.SaveData.ResourcesData.PlayerLeagueManager.IsFeatureUnlocked(FeatureGating.Perks) &&
      (!TutorialController.HasInstance || TutorialController.Instance.IsFlagTutorialFinished());
    if (!isPerkUnlocked) {
      SetActive(nullifiedPerkButtons, false);
      SetActive(FlagGroup, false);
      return;
    }
    NullifyItemData nullifyItemData = null;
    if (!string.IsNullOrEmpty(nullifyDataID)) {
      nullifyItemData = NullifyDataManager.Instance.GetData(nullifyDataID);
    }
    if (nullifyItemData != null) {
      _isPerkNullified = NullifyDataManager.IsNullifyBoost(nullifyItemData, false, NullifyBuffType.Perk);
      if (_isPerkNullified) {
        SetActive(nullifiedPerkButtons, true);
        SetActive(FlagGroup, false);
      } else {
        SetActive(nullifiedPerkButtons, false);
        SetActive(FlagGroup, true);
      }
    }
  }

  /// <summary>
  /// Sets the active state of an array of GameObjects.
  /// </summary>
  /// <param name="objs">The array of GameObjects to modify.</param>
  /// <param name="isActive">The desired active state (true for active, false for inactive).</param>
  void SetActive(GameObject[] objs, bool isActive) {
    if (objs == null) {
      return;
    }
    for (int i = 0; i < objs.Length; i++) {
      if (objs[i] != null) {
        objs[i].gameObject.SetActive(isActive);
      }
    }
  }

  /// <summary>
  /// Check to show nullify mask tooltip
  /// </summary>
  void SetActiveMaskPropNullify() {
    if (objMaskPropNullify == null) {
      return;
    }
    objMaskPropNullify.SetActive(false);
    PlayerLeagueManager plm = PlayerInfoScript.Instance.SaveData.ResourcesData.PlayerLeagueManager;
    bool isPropsUnlocked = plm.IsFeatureUnlocked(FeatureGating.Props);
    if (!isPropsUnlocked) {
      return;
    }
    NullifyItemData nullifyItemData = null;
    if (!string.IsNullOrEmpty(nullifyDataID)) {
      nullifyItemData = NullifyDataManager.Instance.GetData(nullifyDataID);
      if (nullifyItemData != null && nullifyItemData.PropNullify) {
        objMaskPropNullify.SetActive(true);
      }
    }

  }
}
