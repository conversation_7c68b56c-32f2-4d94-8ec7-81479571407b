using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class PVEBackgroundScript : MonoBehaviour {

	// for Blitz and Feud backgrounds

	public GameObject topSectionRoot;
	public GameObject bottomSectionRoot;

	[Header ("ODL Art")]

	public Image OTALogoImage;
	public Image OTAEdge1;
	public Image OTAEdge2;
	public Image skylineImage;
	public Image skylineGenericImage;
	public const string skylinesResourcePath = "UI/Skylines/";

	[Header ("Colorize Images / Texts")]
	public GameObject[] BGColor1;
	public GameObject[] BGColor2;
	public GameObject[] AccentColor1;
	public GameObject[] AccentColor2;
	public Image[] LightGradients;
	public Image[] DarkGradients;

	// Use this for initialization
	void Start () {
		
	}
	
	// Update is called once per frame
	void Update () {
		
	}

	public void PopulateBitzBackground(GameObject topRoot, GameObject bottomRoot, FightCard currentFightCard, MapNodeTemplateData mapTemplateData) {

		topSectionRoot.transform.SetParent(topRoot.transform);
		topSectionRoot.GetComponent<RectTransform> ().localPosition = Vector3.zero;
		bottomSectionRoot.transform.SetParent(bottomRoot.transform);
		bottomSectionRoot.GetComponent<RectTransform> ().localPosition = Vector3.zero;

		fightCardColorize (currentFightCard.Data.PrimaryColor_1, currentFightCard.Data.PrimaryColor_2, currentFightCard.Data.SecondaryColor_1, currentFightCard.Data.SecondaryColor_2);

		if (mapTemplateData != null) {
			string theme = mapTemplateData.BGTheme;
			if (theme != null) {
				if (theme == "Orlando_Tut")
					theme = "Orlando";
			} else
				theme = "Generic";

			string skylineImagePath = skylinesResourcePath + "Skyline_" + theme;
			KFFResourceManager.Instance.LoadOnDemandLooseTextureToImage(skylineImagePath, skylineImage);
			KFFResourceManager.Instance.LoadOnDemandLooseTextureToImage("UI/Skylines/Skyline_Generic", skylineGenericImage);
		}

		if (OTALogoImage != null && !string.IsNullOrEmpty (currentFightCard.Data.BattleHubImageOTA)) {
			KFFResourceManager.Instance.LoadOnDemandLooseTextureToImage(currentFightCard.Data.BattleHubImageOTA, this.OTALogoImage);
		} else
			OTALogoImage.gameObject.SetActive (false);

		if (OTAEdge1 != null)
		{
			if (!string.IsNullOrEmpty (currentFightCard.Data.CardHubImageOTA)) {
				KFFResourceManager.Instance.LoadOnDemandLooseTextureToImage(currentFightCard.Data.CardHubImageOTA, OTAEdge1);
				OTAEdge1.gameObject.SetActive (true);
			} else
				OTAEdge1.gameObject.SetActive(false);
		}						

		if (OTAEdge2 != null) {
			if (!string.IsNullOrEmpty (currentFightCard.Data.CardHubImageOTA)) {
				KFFResourceManager.Instance.LoadOnDemandLooseTextureToImage (currentFightCard.Data.CardHubImageOTA, OTAEdge2);
				OTAEdge2.gameObject.SetActive (true);
			} else
				OTAEdge2.gameObject.SetActive (false);
		}

	}

	public void PopulateFeudBackground(GameObject topRoot, GameObject bottomRoot){

		topSectionRoot.transform.SetParent(topRoot.transform);
		topSectionRoot.GetComponent<RectTransform> ().localPosition = Vector3.zero;
		bottomSectionRoot.transform.SetParent(bottomRoot.transform);
		bottomSectionRoot.GetComponent<RectTransform> ().localPosition = Vector3.zero;

		FactionFeudData currentFeud = FactionFeudManager.Instance.curFactionFeud;

		// NOTE: FEUD currently this just does red and blue, but supporting them here in case they want to in the future
		fightCardColorize (currentFeud.primaryColor1, currentFeud.primaryColor2, currentFeud.secondaryColor1, currentFeud.secondaryColor2);

		// populate background ODLs
		if(this.OTALogoImage != null && currentFeud != null && !string.IsNullOrEmpty(currentFeud.battleHubOTAArt)) 
		{
			if (CheckIfTextureNamesDifferent (currentFeud.battleHubOTAArt, this.OTALogoImage.sprite.texture.name)) {
				KFFResourceManager.Instance.LoadOnDemandLooseTextureToImage (currentFeud.battleHubOTAArt, this.OTALogoImage);
				OTALogoImage.gameObject.SetActive (true);
			}
		}

		if(this.OTAEdge1 != null && !string.IsNullOrEmpty(currentFeud.backgroundOTAArt)) {
			if (CheckIfTextureNamesDifferent (currentFeud.backgroundOTAArt, this.OTAEdge1.sprite.texture.name)) {
				KFFResourceManager.Instance.LoadOnDemandLooseTextureToImage (currentFeud.backgroundOTAArt, this.OTAEdge1);
				this.OTAEdge1.gameObject.SetActive (true);
			}
		}

		if(this.OTAEdge2 != null && !string.IsNullOrEmpty(currentFeud.backgroundOTAArt)) {
			if (CheckIfTextureNamesDifferent (currentFeud.backgroundOTAArt, this.OTAEdge2.sprite.texture.name)) {
				KFFResourceManager.Instance.LoadOnDemandLooseTextureToImage (currentFeud.backgroundOTAArt, this.OTAEdge2);
				this.OTAEdge2.gameObject.SetActive (true);
			}
		}

		// load skylines
		if (skylineImage != null && skylineGenericImage != null) {
			string bgTheme = "Generic";
			string skylineImagePath = skylinesResourcePath + "Skyline_" + bgTheme;
			KFFResourceManager.Instance.LoadOnDemandLooseTextureToImage(skylineImagePath, skylineImage);
			KFFResourceManager.Instance.LoadOnDemandLooseTextureToImage("UI/Skylines/Skyline_Generic", skylineGenericImage);
		}
	}

	// COLORIZE FEUD DATA SET COLORS

	private void fightCardColorize(Color primaryColor1, Color primaryColor2, Color secondaryColor1, Color secondaryColor2 ) {
	
		fightCardColorizeArray (BGColor1, primaryColor1);
		fightCardColorizeArray (BGColor2, primaryColor2);
		fightCardColorizeArray (AccentColor1, secondaryColor1);
		fightCardColorizeArray (AccentColor2, secondaryColor2);

		foreach (Image img in this.LightGradients)
			img.color = HSVColorUtility.ModulateLuminanceByPercent (img.color, 0.666f, true);

		foreach (Image img in this.DarkGradients)
			img.color = HSVColorUtility.ModulateLuminanceByPercent (img.color, -0.72f);

	}

	private void fightCardColorizeArray(GameObject[] array, Color newColor) {
		for (int i = 0; i < array.Length; i++) {
			if (array [i] == null)
				continue;
			if (array[i].GetComponent<Text>() != null)
				array[i].GetComponent<Text>().color = newColor;
			if (array[i].GetComponent<Image>() != null)
				array[i].GetComponent<Image>().color = newColor;
		}
	}

	// END COLORIZE

	// prevent duplicate loading of ODL images
	private bool CheckIfTextureNamesDifferent(string texName1, string texName2) {

		int charToCompare = Mathf.Min (texName1.Length, texName2.Length);

		int texName1StartIndex = texName1.Length - charToCompare;
		int texName2StartIndex = texName2.Length - charToCompare;

		string texName1Truncated = texName1.Substring (texName1StartIndex, charToCompare);
		string texName2Truncated = texName2.Substring (texName2StartIndex, charToCompare);

		if (texName1Truncated == texName2Truncated)
			return false;
		else
			return true;
	}
}
