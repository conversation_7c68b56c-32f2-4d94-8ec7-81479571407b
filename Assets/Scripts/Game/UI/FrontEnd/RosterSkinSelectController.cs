using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class RosterSkinSelectController : UIScreen
{
	#region Exposed properties

	[Header("Roster Skin Select")]
	[SerializeField] private Text skinsOwned;
	[SerializeField] private Text currentEquippedSkinName;
	[SerializeField] private GameObject skinInfoTarget;
	[SerializeField] private UIToolTipController.Config skinInfoConfig;
	[Space]
	[SerializeField] private Transform skinTileContainer;
	[SerializeField] private GameObject skinTileTemplate;
	[Header("Skin Boost")]
	[SerializeField] private Button skinBoostButton;
	[SerializeField] private Text skinBoostButtonLabel;
	[SerializeField] private GameObject skinBoostScrollView;
	[SerializeField] private Transform skinBoostContainer;
	[SerializeField] private GameObject skinBoostTemplate;
	[SerializeField] private float showBoostDuration = 2f;
	[SerializeField] private UIToolTipController.Config noBoostsConfig;
	[SerializeField] private UITweenController showBoostListTween;
	[SerializeField] private UITweenController hideBoostListTween;
	[SerializeField] public UITweenController HideInfoTween;
	[SerializeField] public UITweenController ShowInfoTween;
  [SerializeField] private UITweenController ShowListGearTween;

  [SerializeField]
	private UITweenController ShowViewModeTween;
	[SerializeField]
	private UITweenController HideViewModeTween;
	[SerializeField]
	private UITweenController HideViewModeButtonTween;
	[SerializeField] private GameObject objNullifyMask;
	[HideInInspector]
	public bool mShowInfoUI = true;
	private bool mShowSkinUI = false;

	#endregion

	WrestlerItem currentWrestler;
	string originalSkinVisualID;
	RosterSkinSelectTileScript[] skinSelectTiles;
	RosterSkinSelectBoostTileScript[] skinBoostTiles;
	Coroutine hideBoostCoroutine;
	private const float heighDefaultMaskBoost = 120;

	RosterSkinSelectTileScript _lastClickedSkinSelectTile;
	private bool fromPrematch = false;
	private NullifyItemData nullifyData;
	RosterSkinSelectTileScript lastClickedSkinSelectTile
	{
		get
		{
			return _lastClickedSkinSelectTile;
		}
		set
		{
			if (_lastClickedSkinSelectTile == value)
				return;

			if (_lastClickedSkinSelectTile != null)
				_lastClickedSkinSelectTile.isSelected = false;

			_lastClickedSkinSelectTile = value;
			if (_lastClickedSkinSelectTile != null)
			{
				_lastClickedSkinSelectTile.isSelected = true;
				currentEquippedSkinName.text = KFFLocalization.Get("!!SKINS_CURRENT").Replace("<gear_name>", KFFLocalization.Get(_lastClickedSkinSelectTile.skinVisual.SkinName));
			}
		}
	}

	private void OnEnable() {
		if (UIPowerFTUETopMenu.onShowButtonPower != null) {
			UIPowerFTUETopMenu.onShowButtonPower.Invoke(FTUEPowersInfoType.Gear);
		}

	}

	private void OnDisable() {
		if (UIPowerFTUETopMenu.onShowButtonPower != null) {
			UIPowerFTUETopMenu.onDisableButtonPower.Invoke();
		}
	}
	
	#region UIScreen

	public override void Setup()
	{
		skinTileTemplate.SetActive(false);
		skinBoostTemplate.SetActive(false);
	}

	private float timeCounter = 0f;
	private const float COUNTER_INTERVAL = 1f;
	void Update() {
		timeCounter += Time.deltaTime;
		if(timeCounter > COUNTER_INTERVAL) {
			timeCounter = 0f;
			UpdateBoostListScrollHeight();
		}
	}

	public override IEnumerator PushRoutine(ScreenInfo info)
	{
		currentWrestler = info.Get<WrestlerItem>("wrestler");
		if (info.KeyExists("fromPrematch")) { 
			fromPrematch = info.Get<bool>("fromPrematch");
		}
		if (info.KeyExists(ManageRosterNullifyIconController.PopupValueKey)) {
			nullifyData = info.Get<NullifyItemData>(ManageRosterNullifyIconController.PopupValueKey);
		}
		originalSkinVisualID = currentWrestler.EquipedSkinVisual.ID;

		// TODO stop character from spinning when scroll the skin

		PopulateSkinOwned(currentWrestler);
		PopulateSkinSelectScroller(currentWrestler);
		PopulateSkinBoostScroller(currentWrestler);

		skinBoostButtonLabel.text = KFFLocalization.Get("!!SKINS_VIEW_BOOSTS");

		bool isUnlockSequencePlaying = TryStartUnlockSequence(false, true);

		if (skinBoostTiles != null && skinBoostTiles.Length > 0)
		{
			skinBoostButton.interactable = true;
			ShowSkinBoosts(!isUnlockSequencePlaying, true); // don't show the boosts during unlock presentation

		}
		else
		{
			skinBoostButton.interactable = false;
		}

		ActiveTooltipNullify();
		return base.PushRoutine(info);
	}

	public override IEnumerator PopRoutine()
	{
		RestoreSkinToOriginal();
		
		return base.PopRoutine();
	}

	public override void Back()
	{
		OnClickBack();
	}

	public void OnClickBack() {

		if (!mShowInfoUI) {
			OnClickViewMode ();
			return;
		}

		HideViewModeButtonTween.Play ();

		CloseScreen();
	}
		
	#endregion

	#region unlock presentation 

	bool selectAfterUnlock;
	string[] unlockSkinIDs;
	bool TryStartUnlockSequence(bool slectUnlockedSkin = false, bool isAutoUnlock = false)
	{
		unlockSkinIDs = NewSkinsStackManager.Instance.GetSkinIDsForUnlockPresentation(currentWrestler.Form.SSGroup.ID);
		if (unlockSkinIDs.Length <= 0)
			return false;
		selectAfterUnlock = slectUnlockedSkin;

		UIManager.Instance.onScreenPopped += OnPresentationFinished;
		UIScreen.ScreenInfo info = new UIScreen.ScreenInfo();
		info.Add(SkinUnlockPresentationController.KEY_SKIN_IDS, unlockSkinIDs);
		info.Add("isAuto", isAutoUnlock);
		UIManager.Instance.QueuePush("frontEnd_skin_unlock_presentation", info);

		return true;
	}

	void OnPresentationFinished(UIScreen screen)
	{
		if (screen is SkinUnlockPresentationController)
		{
			UIManager.Instance.onScreenPopped -= OnPresentationFinished;

			PopulateSkinOwned(currentWrestler);

			int prevActiveSkinBoostCount = GetActiveSkinBoostInDisplay();
			PopulateSkinBoostScroller(currentWrestler);
			int currActiveSkinBoostCount = GetActiveSkinBoostInDisplay();
			if (prevActiveSkinBoostCount != currActiveSkinBoostCount)
				ShowSkinBoosts(true, true);

			if (selectAfterUnlock && unlockSkinIDs.Length > 0)
			{
				string unlockSkinID = unlockSkinIDs[0];
				SkinVisualData unlockSkinVisual = SkinDataManager.Instance.GetData(unlockSkinIDs[0]).SkinVisual;

				if (unlockSkinVisual == currentWrestler.EquipedSkinVisual)
				{
					// if skin is already been previewed, just set it so it won't be restored when leaving the screen
					SetSkin(unlockSkinID); 
				}
				else
				{
					// if skin is not been previewed, auto select it
					var skinSelectTile = skinSelectTiles.Find(m => m.skinVisual == unlockSkinVisual);
					if (skinSelectTile != null)
						skinSelectTile.OnClick();
				}

				if (UIManager.Instance.GetScreen<SkinModalController> () != null) {          
					UIManager.Instance.QueuePopToScreen<RosterSkinSelectController> ();
				}
			}
		}
	}

	#endregion

	void PopulateSkinOwned(WrestlerItem wrestler)
	{
		List<SkinData> ownableSkins = SkinDataManager.Instance.FindAll (wrestler.Form.SSGroup.ID);
		skinsOwned.text = (wrestler.acquiredSkinIDs.Length + 1).ToString () + "/" + (ownableSkins.Count + 1).ToString();
	}

	void PopulateSkinSelectScroller(WrestlerItem wrestler)
	{
		if (skinSelectTiles != null)
		{
			lastClickedSkinSelectTile = null;
			foreach (var tile in skinSelectTiles)
				Destroy(tile.gameObject);
			skinSelectTiles = null;
		}

		List<SkinVisualData> skinVisuals = wrestler.Form.SSGroup.SkinVisuals;
		skinSelectTiles = new RosterSkinSelectTileScript[skinVisuals.Count];

		skinVisuals.Sort (SkinDataManager.SortByDisplayOrder);

		int i = 0;
		foreach (var skinVisual in skinVisuals)
		{
			GameObject go = skinTileContainer.InstantiateAsChild(skinTileTemplate);
			go.SetActive(true);
			RosterSkinSelectTileScript tile = go.GetComponent<RosterSkinSelectTileScript>();
			tile.Populate(wrestler, skinVisual, OnSkinSelected, OnSkinPurchased);
			if (wrestler.EquipedSkinVisual == skinVisual)
				lastClickedSkinSelectTile = tile;
			else
				tile.isSelected = false;

			skinSelectTiles[i++] = tile;
		}
	}

	#region input handling

	void OnSkinSelected(RosterSkinSelectTileScript tileClicked)
	{
		lastClickedSkinSelectTile = tileClicked;

		// save set skin
		if (currentWrestler.IsEquipedSkinVisualDefault())
		{
			// default skin visual has no skin data or skin id
			SetSkin(null);
		}
		else
		{
			SkinData skin = SkinDataManager.Instance.Find(tileClicked.skinVisual);
			if (skin != null && currentWrestler.IsAcquiredSkin(skin))
			{
				// if the skin is new, upon clicking it we remove it's new status
				NewSkinsStackManager.Instance.Remove(skin.ID);
				SetSkin(skin.ID);
			}
		}
	}

	void SetSkin(string skinID)
	{
		TFServerOp op = Multiplayer.Multiplayer.SetSkin(null, currentWrestler.Form.ID, skinID, SetSkinCallback);
		ICMultiplayer.Instance.BlockTillMessage (ICMPMessageType.SET_SKIN, null, op);
		op.Execute ();
	}

	void OnSkinPurchased(RosterSkinSelectTileScript tileClicked)
	{
    SkinUnlockPresentationController tempScreen = UIManager.Instance.TryGetScreen<SkinUnlockPresentationController>();
    if (tempScreen != null) {
      return;
    }    
		ShowSkinBoosts(false);
		TryStartUnlockSequence(true);
	}

	void SetSkinCallback(bool success, Dictionary<string, object> dict)
	{
		if (success) {
			originalSkinVisualID = currentWrestler.EquipedSkinVisual.ID;
			FBLobbyManager_R53.Instance.WrestlerEquipSkin(currentWrestler);
		}
	}

	public void OnClickInfo()
	{
		UIToolTipController.Instance.Show(skinInfoTarget, skinInfoConfig, KFFLocalization.Get("!!SKINS_INFO"));
	}

	#endregion

	#region skin boost scroller 

	int GetActiveSkinBoostInDisplay()
	{
		int activeSkinBoostCount = 0;
		foreach (var skinBoostTile in skinBoostTiles)
		{
			if (skinBoostTile.isActive)
				activeSkinBoostCount++;
		}
		return activeSkinBoostCount;
	}
	void PopulateSkinBoostScroller(WrestlerItem wrestler)
	{
		if (skinBoostTiles != null)
		{
			foreach (var tile in skinBoostTiles)
				Destroy(tile.gameObject);
			skinBoostTiles = null;
		}

		List<SkinBoostsData> skinBoosts = SkinBoostsDataManager.Instance.GetBoostsOfGivenBoostGroupID(wrestler.Form.SSGroup.SkinBoostGroupID);
		skinBoostTiles = new RosterSkinSelectBoostTileScript[skinBoosts.Count];
		int skinCount = wrestler.acquiredSkinIDs.Length;
		int i = 0;

    foreach (var skinBoost in skinBoosts)
		{
			GameObject go = skinBoostContainer.InstantiateAsChild(skinBoostTemplate);
			go.SetActive(true);
			RosterSkinSelectBoostTileScript tile = go.GetComponent<RosterSkinSelectBoostTileScript>();

			BoostData boost = BoostDataManager.Instance.GetData(skinBoost.BoostID);
			tile.Populate(boost, skinCount + 1, skinBoost.SkinCount, (i + 1 == skinBoosts.Count));
      skinBoostTiles[i++] = tile;
		}
    ShowListGearTween.Play();
  }

	public void OnClickBoosts()
	{
		ShowSkinBoosts(!skinBoostScrollView.activeInHierarchy);
	}

	public void OnClickNoBoosts()
	{
		if (skinBoostButton.IsInteractable())
			return;

		UIToolTipController.Instance.Show(skinBoostButton.gameObject, noBoostsConfig, KFFLocalization.Get("!!SKIN_NO_BOOSTS"));
	}

	public void ShowSkinBoosts(bool isShown, bool autoShowing = false)
	{
		if (hideBoostCoroutine != null)
		{
			StopCoroutine (hideBoostCoroutine);
			hideBoostCoroutine = null;
		}

		if (isShown && !skinBoostContainer.gameObject.activeInHierarchy)
		{
			//skinBoostScrollView.SetActive(true);
			skinBoostButtonLabel.text = KFFLocalization.Get("!!SKINS_HIDE_BOOSTS");
			if (autoShowing)
				hideBoostCoroutine = StartCoroutine (HideBoost ());
			showBoostListTween.Play ();
		}
		else if (!isShown && skinBoostContainer.gameObject.activeInHierarchy)
		{
			//skinBoostScrollView.SetActive(false);
			skinBoostButtonLabel.text = KFFLocalization.Get("!!SKINS_VIEW_BOOSTS");
			hideBoostListTween.Play ();
		}
	}

	public void OnClickViewMode() {
		mShowInfoUI = !mShowInfoUI;

		if (mShowInfoUI) {
			ShowInfoTween.Play ();
			HideViewModeTween.Play ();
		} else {
			HideInfoTween.Play ();
			ShowViewModeTween.Play ();
		}

	}

	IEnumerator HideBoost()
	{
		yield return new WaitForSeconds(showBoostDuration);
		ShowSkinBoosts(false);
	}

	#endregion

	void RestoreSkinToOriginal()
	{
		if (originalSkinVisualID != currentWrestler.EquipedSkinVisual.ID)
			currentWrestler.EquipSkin(originalSkinVisualID);
	}

	ScrollRect skinBoostScrollRect;
	RectTransform skinBoostRectTransform;
	protected override void Awake() {
    base.Awake();
		skinBoostScrollRect = skinBoostScrollView.GetComponent<ScrollRect>();
		skinBoostRectTransform = skinBoostScrollView.GetComponent<RectTransform>();
	}

  private float maxBoostListHeight = 460;
	// make the viewport match the content height up until a known max height
	private void UpdateBoostListScrollHeight () {
		float contentHeight = skinBoostScrollRect.content.GetHeight();
		float height = 0;
		if (contentHeight < maxBoostListHeight) {
			height = contentHeight + 45f;
			skinBoostRectTransform.SetHeight (height);
		} else {
			height = maxBoostListHeight + 45f;
			skinBoostRectTransform.SetHeight (height);
		}
		UpdateMaskHeight(height);
	}
	
	/// <summary>
	/// Active button tooltip nullify
	/// </summary>
	/// <param name="nullifyType"></param>
	public void ActiveTooltipNullify() {
		if (objNullifyMask != null) {
			if (fromPrematch && nullifyData != null) {
				if (nullifyData != null && nullifyData.GearNullify) {
					objNullifyMask.SetActive(true);
					UpdateMaskHeight();
					return;
				}
			}
			objNullifyMask.SetActive(false);
		}
	}

	void UpdateMaskHeight(float scrollHeight = 0) {
		if (objNullifyMask != null && objNullifyMask.activeSelf) {
			objNullifyMask.GetComponent<RectTransform>().sizeDelta = new Vector2(objNullifyMask.GetComponent<RectTransform>().sizeDelta.x, scrollHeight + heighDefaultMaskBoost);
		}
	}
}
