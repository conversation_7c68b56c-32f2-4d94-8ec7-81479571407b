using UnityEngine;

public class SharedSpriteLoader : Singleton<SharedSpriteLoader> {
  [SerializeField]
  private string prefabPath = "Prefabs/SharedSpriteHolder";
  private SharedSpritesHolder _controller;
  public SharedSpritesHolder Controller {
    get {
      if (_controller == null) {
        GameObject prefab = KFFResourceManager.Instance.LoadResource(prefabPath) as GameObject;
        GameObject prefabGO = gameObject.InstantiateAsChild(prefab);
        if (prefabGO != null) {
          _controller = prefabGO.GetComponent<SharedSpritesHolder>();
        }
      }
      return _controller;
    }
  }
}
