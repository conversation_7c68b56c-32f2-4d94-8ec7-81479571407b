using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using EnhancedUI_Original;
using EnhancedUI_Original.EnhancedScroller;

public class RBELeaderboardTileScript : EnhancedScrollerCellView {
  public Text scoreText;
  public Text rankText;
  public Text factionNameText;
  public Image BaseObject;

  public Color FocusedColor = Color.cyan;
  private Color mOriginalColor;

  public GameObject DefaultBGObj;
  public GameObject GoldBGObj;
  public GameObject SilverBGObj;
  public GameObject BronzeBGObj;
  public GameObject MyRankObj;
  public Color colorDefault;
  public Color colorGold;
  public Color colorSilver;
  public Color colorBronze;
  [SerializeField] GameObject bgGrey;
  [SerializeField] GameObject bgBlack;
  
  [Header("User Only")]
  public Text userNameText;

  [Header("Faction Only")]
  public Text factionLvlText;
  public Text factionMembersText;
  public Image factionIcon;
  
  [Header("Nudge")]
  [SerializeField] private CanvasGroup nudgeIcon;
  [SerializeField] private Animator nudgeAnimator;
  
  [Header("Left Faction")]
  [SerializeField] GameObject leftFactionFrame;
  [SerializeField] GameObject leftFactionBg;
  [SerializeField] GameObject inactiveBg;
  [SerializeField] Text leftFactionNameText;
  
  private RBEEventData mEvent;
  private RBEEntry mEntry;
  private bool mIsMe;
  private RBELeaderboardController_R44 leaderboardController;
  private ContextualNudgeAnimSmb nudgeAnimSmb;
  
  private void OnDisable() {
    ICMultiplayerEvent.ContextualNudgeCallback -= SendMessagesCallback;
    if (nudgeAnimSmb != null) {
      nudgeAnimSmb.OnStateExitCallback -= RunBellAnimationCallback;
    }
  }
  
  public void Populate(RBEEventData ev, RBEEntry entry, bool myself, int _index = 0, bool activeNudge = false) {
    if (_index % 2 == 0) {
      bgBlack.SetActive(true);
      bgGrey.SetActive(false);
    } else {
      bgGrey.SetActive(true);
      bgBlack.SetActive(false);
    }
    mEvent = ev;
    mEntry = entry;
    mIsMe = myself;

    rankText.text = entry.Rank.ToString();
    scoreText.text = TFUtils.ConvertLongToStringWithComma(entry.Score);
    if (mEntry.AllianceName != null) {
      factionNameText.text = mEntry.AllianceName;
    }

    DefaultBGObj.SetActive(entry.Rank > 3 || entry.hasLeftFaction);
    GoldBGObj.SetActive(entry.Rank == 1 && entry.hasLeftFaction == false);
    SilverBGObj.SetActive(entry.Rank == 2 && entry.hasLeftFaction == false);
    BronzeBGObj.SetActive(entry.Rank == 3 && entry.hasLeftFaction == false);

    //MyRankObj.SetActive(mIsMe);

    if (mEvent.EventType == RBEEventType.Individual) {
      userNameText.text = mEntry.UserName;
      factionNameText.gameObject.SetActive(!string.IsNullOrEmpty(mEntry.AllianceName));
      if (entry.Rank == 1) {
        userNameText.color = colorGold;
      } else if(entry.Rank == 2) {
        userNameText.color = colorSilver;
      }else if(entry.Rank == 3) {
        userNameText.color = colorBronze;
      } else {
        userNameText.color = colorDefault;
      }
    } else {
      bool isMyFaction = false;
      //Left faction
      if (inactiveBg != null)
        inactiveBg.SetActive(entry.hasLeftFaction);
      if (leftFactionBg != null)
        leftFactionBg.SetActive(entry.hasLeftFaction);
      if (leftFactionFrame != null)
        leftFactionFrame.SetActive(entry.hasLeftFaction);

      if (factionNameText != null) {
        factionNameText.gameObject.SetActive(!string.IsNullOrEmpty(mEntry.AllianceName));
      }

      if (!string.IsNullOrEmpty(mEntry.AllianceName)) {
        if (userNameText != null) {
          userNameText.text = mEntry.UserName;
        }

        if (factionNameText != null) {
          factionNameText.text = mEntry.AllianceName;
          if (entry.Rank == 1) {
            factionNameText.color = colorGold;
          } else if (entry.Rank == 2) {
            factionNameText.color = colorSilver;
          } else if (entry.Rank == 3) {
            factionNameText.color = colorBronze;
          } else {
            factionNameText.color = colorDefault;
          }
        }

        if (entry.hasLeftFaction) {
          factionNameText.text = "";
          rankText.text = "";
          if (leftFactionNameText != null)
            leftFactionNameText.text = mEntry.AllianceName;
        }
        //HungTN-R47 Unused
        // if (mEntry.UserName == "") {
        //   if (mEntry.AllianceID == AllianceManager.Instance.PlayerAlliance.ID) {
        //     isMyFaction = true;
        //   }
        // } else {
        //   isMyFaction = mIsMe;
        // }
        if (nudgeIcon != null) {
          if (activeNudge && entry.UserID != PlayerInfoScript.Instance.SaveData.PlayerData.ID) {
            DisplayNudgeIcon(entry.ContextualCoolDown);
          } else
            nudgeIcon.gameObject.SetActive(false);
        }
      }

      if (factionMembersText != null) {
        factionMembersText.text = string.Format(KFFLocalization.Get("!!ALLIANCE_NUM_MEMBERS"), mEntry.AllianceMembers, mEntry.AllianceMaxCount);
      }

      if (mEntry.AllianceSymbol != null) {
        factionIcon.gameObject.SetActive(true);
        AllianceLogoDataManager.Instance.LoadLogoIconFromODL(mEntry.AllianceSymbol, factionIcon);
        /*factionIcon.sprite = AllianceLogoDataManager.Instance.GetSprite(mEntry.AllianceSymbol);
        if (factionIcon.sprite == null) {//not exist. loading from ODL
          AllianceLogoDataManager.Instance.LoadLogoIconFromODL(mEntry.AllianceSymbol, factionIcon);
        }*/
      } else {
        if (factionIcon) {
          factionIcon.gameObject.SetActive(false);
        }
      }

      if (factionLvlText != null)
        factionLvlText.text = KFFLocalization.Get("!!R23_LV") + " " + mEntry.AllianceLevel.ToString();

      //MyRankObj.SetActive(isMyFaction);
    }

    if (factionNameText.text.ToLower().Contains("no-syndicate")) {
      factionNameText.text = KFFLocalization.Get("!!FACTION_NOT_IN_FACTION_LABEL");
    }
  }
  
  public void SetControllerForTile(RBELeaderboardController_R44 controller) {
    leaderboardController = controller;
  }
  
  void DisplayNudgeIcon(uint cd) {
    if (mEntry.AllianceID == AllianceManager.Instance.PlayerAlliance.ID) {
      nudgeIcon.gameObject.SetActive(true);
      nudgeIcon.alpha = cd < TFUtils.EpochTimeUint() ? 1 : 0.3f;  
    }
  }

  private void RefreshOverlay() {
    if (mIsMe) {
      BaseObject.color = FocusedColor;
    } else {
      BaseObject.color = mOriginalColor;
    }
  }

  //Button events
  public void OnTapContextualNudge() {
    if (mEntry == null || string.IsNullOrEmpty(mEntry.UserID)) {
      return;
    }

    if (mEntry.ContextualCoolDown >= TFUtils.EpochTimeUint()) {
      UIScreen.ScreenInfo info = new UIScreen.ScreenInfo();
      info.Add(FactionNudgeWarningPopupController_R47.SCREEN_KEY, mEntry.ContextualCoolDown);
      UIManager.Instance.QueuePush(FactionNudgeWarningPopupController_R47.PREFAB_NAME, info);
      return;
    }

    if (PlayerPrefs.HasKey(ContextualNudgePopupController_R47.NOT_SHOW_RBE_KEY)) {
      InputLock.Lock();
      nudgeAnimator.SetTrigger(ContextualNudgePopupController_R47.RUN_ANIM);
      if (nudgeAnimSmb == null) {
        nudgeAnimSmb = nudgeAnimator.GetBehaviour<ContextualNudgeAnimSmb>();
      }

      string messageTitle = KFFLocalization.Get("!!R47_RBE_CONTEXTUAL_TITLE");
      string messageBody = KFFLocalization.Get("!!R47_RBE_CONTEXTUAL_BODY");
      TFServerOp op = Multiplayer.Multiplayer.SendFactionNudge(new[] {mEntry.UserID}, messageTitle, messageBody, NudgeType.rbe_nudge_cd);
      ICMultiplayer.Instance.BlockTillMessage(ICMPMessageType.SEND_NUDGE, null, op, false);
      op.Execute();
      ICMultiplayerEvent.ContextualNudgeCallback += SendMessagesCallback;
    } else {
      UIScreen.ScreenInfo info = new UIScreen.ScreenInfo();
      info.Add(ContextualNudgePopupController_R47.SCREEN_KEY, mEntry.UserID);
      info.Add(ContextualNudgePopupController_R47.RBE_TILE_KEY, this);
      info.Add(ContextualNudgePopupController_R47.USER_NAME_KEY, mEntry.UserName);
      UIManager.Instance.QueuePush(ContextualNudgePopupController_R47.PREFAB_NAME, info);
    }
  }

  void SendMessagesCallback(bool isSuccess, Dictionary<string, uint> _DataRes) {
    ICMultiplayerEvent.ContextualNudgeCallback -= SendMessagesCallback;
    foreach (var item in _DataRes) {
      if (mEntry.UserID == item.Key) {
        mEntry.ContextualCoolDown = item.Value;
      }

      if (leaderboardController != null) {
        leaderboardController.SetNudgeCoolDown(item.Key, item.Value);
      }
    }

    if (isSuccess && nudgeAnimSmb != null) {
      nudgeAnimSmb.OnStateExitCallback += RunBellAnimationCallback;
      nudgeAnimator.SetTrigger(ContextualNudgePopupController_R47.STOP_ANIM);
    } else {
      InputLock.Unlock();
      nudgeAnimator.SetTrigger(ContextualNudgePopupController_R47.STOP_ANIM);
      DisplayNudgeIcon(mEntry.ContextualCoolDown);
    }
  }

  void RunBellAnimationCallback() {
    if (nudgeAnimSmb != null) {
      nudgeAnimSmb.OnStateExitCallback -= RunBellAnimationCallback;
      DisplayNudgeIcon(mEntry.ContextualCoolDown);
      InputLock.Unlock();
    }
  }

  public void SetDataAfterSend(string user_id, uint nudge_cd) {
    if (mEntry.UserID == user_id) {
      mEntry.ContextualCoolDown = nudge_cd;
      if (leaderboardController != null) {
        leaderboardController.SetNudgeCoolDown(user_id, nudge_cd);
      }
      DisplayNudgeIcon(nudge_cd);
    }
  }
}
