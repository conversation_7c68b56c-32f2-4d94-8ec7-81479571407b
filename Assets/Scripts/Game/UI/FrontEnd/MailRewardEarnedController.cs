using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using UnityEngine.UI;

public class MailRewardEarnedController : UIScreen {
  // prefab in project: UI_FrontEnd_Mail_Reward_Earned
  
  public Text title;
  public KFFText SubTitle = null;
  public Text unclaimedTxt;
  public GameObject RewardGrid;
  public GameObject PrefabRewardItem;

  public override void Setup()//NEW UI SYSTEM
  {

  }

  public override IEnumerator PushRoutine(ScreenInfo info) {
    if (info != null) {
      if (info.KeyExists("debugTest"))
        TFUtils.LogWarning("SCS: Debug Text: " + info.Get<string>("debugTest"));
    } else {
      MenuShowFunc();
    }

    return base.PushRoutine(info);
  }

  public void MenuShowFunc() {

    // Clear all.
    RewardGrid.transform.DestroyAllChildren();

    int amount = 0;
    for (int i = 0; i < MailManager.Instance.allRewardsDict.Count; ++i) {
      Dictionary<string, object> rewardsDict = MailManager.Instance.allRewardsDict[i];

      var normalRewardArray = (List<object>)rewardsDict["rewards"];

      //R39 - Hoangnh2: Read belt/rune reward data
      var beltRuneArray = new List<object>();
      if (rewardsDict != null && rewardsDict.ContainsKey("belt_rune_data") && rewardsDict["belt_rune_data"] is Dictionary<string, object>) {
        Dictionary<string, object> beltRuneRewardDict = TFUtils.LoadDict(rewardsDict, "belt_rune_data");
        if (beltRuneRewardDict.ContainsKey("belts")) {
          List<object> belts = TFUtils.LoadList<object>(beltRuneRewardDict, "belts");
          beltRuneArray.AddRange(belts);
        }

        if (beltRuneRewardDict.ContainsKey("runes")) {
          List<object> runes = TFUtils.LoadList<object>(beltRuneRewardDict, "runes");
          beltRuneArray.AddRange(runes);
        }
      }

      int count = 0;
      while (normalRewardArray.Count > count) {
        string entry = normalRewardArray[count] + ":" + normalRewardArray[count + 1];

        GameObject spawn = RewardGrid.transform.InstantiateAsChild(PrefabRewardItem);
        ItemManager.Instance.SetRewardIconTexture(spawn, entry, _beltItemScale: 0.7f);

        count += 2;
        amount++;
      }

      for (int j = 0; j < beltRuneArray.Count; j++) {
        GameObject spawn = RewardGrid.transform.InstantiateAsChild(PrefabRewardItem);
        string beltRuneData = MiniJSON.Json.Serialize(beltRuneArray[j]);
        ItemManager.Instance.SetRewardIconTexture(spawn, beltRuneData, _beltItemScale: 0.7f);
        amount++;
      }
    }

    string sub = KFFLocalization.Get("!!MAIL_REWARDSCLAIMED_AMOUNT");
    title.text = KFFLocalization.Get(amount > 0 ? "!!MAIL_REWARDSCLAIMED" : "!!R54_MAIL_REWARDS_UNCLAIMED_TITLE");
    SubTitle.gameObject.SetActive(amount > 0);
    SubTitle.text = sub.Replace("<Val1>", $"<color=#10FF44>{amount}</color>");
    unclaimedTxt.gameObject.SetActive(MailManager.Instance.unclaimedCount > 0);
    unclaimedTxt.text = string.Format(KFFLocalization.Get("!!R54_MAIL_REWARDS_UNCLAIMED_AMOUNT"), MailManager.Instance.unclaimedCount);
  }

  public void MenuHideFunc() {
    MailManager.Instance.RewardsDict = null;
    UIManager.Instance.QueuePop();
  }

  public void OnClickOK() {
    //this requires that the user is forced to press OK (and cannot exit this screen by any of the other menu buttons)
    if (WPPlayerData.WPCashContract.IsReadyToStartCashContract()) {
      BottomMenuController.Instance.OnClickButton(MenuType.Main);
      return;
    }

    //BottomMenuController.Instance.BackToAboveLayerMenu();

    var mailMain = UIManager.Instance.TryGetScreen<MailMainStackController>();
    if (mailMain)
      mailMain.RefreshMailOnFocus();

    MenuHideFunc();
  }

  public override void CloseScreen() {
    if (WPPlayerData.WPCashContract.IsReadyToStartCashContract()) {
      BottomMenuController.Instance.OnClickButton(MenuType.Main);
      return;
    }

    base.CloseScreen();
  }


}