using UnityEngine;
using System.Collections;
using UnityEngine.UI;

public class GrabbagContainerTileScript : MonoBehaviour {

  public Transform TileSpawnPoint;
  public UITweenController RevealTween;
  public RewardEntry Entry;
  public GameObject UnlockLootOnGridVFX;
  public GameObject RevealOnGridVFX;
  public GachaContainerTileMode TileMode;

  public GameObject PosterTilePrefab;
  public GameObject TokenTilePrefab;
  public GameObject BadgeTilePrefab;
  public GameObject CurrencyTilePrefab;

  public AudioClip LootRevealSound;
  public AudioClip LootRevealSoundGacha;
  public AudioClip LootUnlockSound;

  public GameObject mTileObj;

  public bool IsSold;
  public bool IsRecruited;
  public KFFText lblAmount;
  public KFFText lblAmountWithTP;
  public KFFText lblAmountWithGear;
  public KFFText lblAmountWithShard;
  public GameObject objAmount;
  public GameObject objAmountWithTP;
  public GameObject objAmountWithGear;
  public GameObject objAmountWithShard;

  public void Populate(RewardEntry entry, PosterTileMode posterTileMode = PosterTileMode.DisplayOnly, bool displayStar = false) {
    Entry = entry;
    ResetContainer();
    PopulateGachaRevealTile(entry, TileSpawnPoint, posterTileMode);
    mTileObj.ChangeLayer(TileSpawnPoint.gameObject.layer);
    //tileSpawnPoint is sorting above, set the icon sibling index the last manually 
    TileSpawnPoint.GetComponent<RectTransform>().SetAsFirstSibling();
  }

  public void RevealContent(bool onGrid = true) {
    RevealTween.Play();
    if(onGrid) {
      var fxObj = mTileObj.transform.InstantiateAsChild(RevealOnGridVFX);
      if(SceneFlowManager.Instance.InBattleScene())
        fxObj.transform.localScale = Vector3.one * 200;

      fxObj.ChangeLayer(mTileObj.layer);

      //decide which sfx to play based on which scene -gabec
      if(TileMode == GachaContainerTileMode.PostMatch) {
        KFFSoundPlayer.Instance.PlayOneShot(LootRevealSound);
      } else {
        KFFSoundPlayer.Instance.PlayOneShot(LootRevealSoundGacha);
      }
    }
    var currencyTileScript = mTileObj.GetComponent<CurrencyTileScript>();
    if(currencyTileScript != null)
      currencyTileScript.ShowCurrencyText(false);

    var tokenTileScript = mTileObj.GetComponent<TokenTileScript>();
    if(tokenTileScript != null)
      tokenTileScript.SetGrayoutImage(false);
  }

  public void ShowContentAfterNew() {
    TileSpawnPoint.GetComponent<CanvasGroup>().alpha = 1f;
  }

  public void RevealNew() {
    var fxObj = mTileObj.transform.InstantiateAsChild(RevealOnGridVFX);
    if(GachaSequenceLoader.Instance.GachaMode == GachaModeEnum.PostMatch)
      fxObj.transform.localScale = Vector3.one * 200;
    fxObj.ChangeLayer(mTileObj.layer);
    KFFSoundPlayer.Instance.PlayOneShot(LootRevealSound);
  }

  private bool Locked = true;

  public void Unlock() {
    if(Locked) {
      var fxObj = mTileObj.transform.InstantiateAsChild(UnlockLootOnGridVFX);
      KFFSoundPlayer.Instance.PlayOneShot(LootUnlockSound);
      fxObj.ChangeLayer(mTileObj.layer);
      Locked = false;
    }
  }

  public void PopulateGachaRevealTile(RewardEntry entry, bool showCurrencyAmount = true, PosterTileMode posterTileMode = PosterTileMode.DisplayOnly) {
    CurrencyTileScript.TileMode currency_tile_mode = (showCurrencyAmount) ? CurrencyTileScript.TileMode.WithAmount : CurrencyTileScript.TileMode.WithoutAmount;
    objAmount.SetActive(false);
    objAmountWithTP.SetActive(false);
    objAmountWithGear.SetActive(false);
    objAmountWithShard.SetActive(false);
    mTileObj = ItemManager.Instance.PopulateItemPanel(TileSpawnPoint.gameObject, entry, posterTileMode, currency_tile_mode, _setBeltRuneAction: true);
    mTileObj.ChangeLayer(TileSpawnPoint.gameObject.layer);

    if(Entry.ItemRewarded.Poster != null) {
      // reward is Poster
      var tileScript = mTileObj.GetComponent<PosterTileScript>();
      if(tileScript != null) {
        tileScript.OnClickCallback = OnClickPoster;
        tileScript.SetActiveTPBanner(false);
        tileScript.SetActiveTPPosterValue(false);
        if(tileScript.mReward.ItemRewarded.PosterUsage == PosterUsageType.TP) {
          if(tileScript.mReward.ItemRewarded.Poster.IsTPPoster()) {
            //  COLOR TP
            lblAmountWithTP.text = entry.Amount.ToString();
            objAmountWithTP.SetActive(true);
            tileScript.SetActiveRarity(false);
          } else {
            //  POSTER duplicate
            lblAmountWithShard.text = entry.Amount.ToString();
            objAmountWithShard.SetActive(true);
            tileScript.shardBanner.SetActive(false);
            tileScript.SetActiveRarity(false);
          }
        } else {
          //  POSTER case and POSTER FUSE case
          lblAmount.text = "x" + entry.Amount.ToString();
          objAmount.SetActive(true);
        }
      } else {
        var rewardTileScript = mTileObj.GetComponent<RewardTileScript>();
        // Move tp amount to the bottom for pvp notoriety reward screen
        if (rewardTileScript != null && UIManager.Instance.TryGetScreen<PVPHubPanel>() != null) {
          rewardTileScript.SetTPPosterAmountPosition(0, -55, 0);
        }
      }
    }
    if(Entry.ItemRewarded.Token != null) {
      var tileScript = mTileObj.GetComponent<TokenTileScript>();
      if(tileScript != null) {
        tileScript.OnClickCallback = OnClickToken;
        tileScript.SetActiveAmountValue(false);
      }
      lblAmount.text = "x" + entry.Amount.ToString();
      objAmount.SetActive(true);
    }
    if(Entry.ItemRewarded.Skin != null && entry.ConvertedToItem != null) {
      var tileScript = mTileObj.GetComponent<SkinTileScript>();
      if(tileScript != null) {
        tileScript.ShowCurrencyBanner();
        tileScript.SetActiveCurrencyAmount(false);
      }

      SBGPullRoomController sbgPull = UIManager.Instance.GetScreen<SBGPullRoomController>();
      if(sbgPull != null) {
        lblAmountWithGear.text = entry.Amount.ToString();
      } else {
        lblAmountWithGear.text = entry.ConvertedToItem.Amount.ToString();
      }
      objAmountWithGear.SetActive(true);
    }
    if(Entry.ItemRewarded.Currency != null) {
      var tileScript = mTileObj.GetComponent<CurrencyTileScript>();
      if(tileScript != null) {
        tileScript.ShowCurrencyText(false);
      }
      lblAmount.text = "x" + entry.Amount.ToString();
      objAmount.SetActive(true);
    }
    if(Entry.ItemRewarded.Shard != null) {
      // reward is Shard
      var tileScript = mTileObj.GetComponent<PosterTileScript>();
      if(tileScript != null) {
        tileScript.shardAmountText.gameObject.SetActive(false);
        lblAmountWithShard.text = entry.Amount.ToString();
        objAmountWithShard.SetActive(true);
      }
    }
    
    //R39 Belt/Rune
    if(Entry.ItemRewarded.BeltData != null) {
      lblAmount.text = "x" + entry.Amount.ToString();
      objAmount.SetActive(true);
    }
    if(Entry.ItemRewarded.RuneData != null) {
      lblAmount.text = "x" + entry.Amount.ToString();
      objAmount.SetActive(true);
    }
  }

  public void SetPosterTileOnClickCallback(PosterTileScript.CallbackDelegate _callback) {
    if(mTileObj != null) {
      var tileScript = mTileObj.GetComponent<PosterTileScript>();
      if(tileScript != null)
        tileScript.OnClickCallback = _callback;
    }
  }

  public void SetTileSpawnPointPosition() {
    TileSpawnPoint.transform.localPosition = new Vector3(-6f, 0f, 0f);
  }

  public void OnClickPoster(PosterTileScript _p) {
    if(_p.mReward.ItemRewarded.PosterUsage == PosterUsageType.TP) {
      if(_p.mReward.ItemRewarded.Poster.IsTPPoster())
        InventoryModalsScript.ShowGeneric(_p.PosterIcon.sprite, _p.mReward.ItemRewarded.GetItemName(), _p.mReward.ItemRewarded.GetItemDescription());
      else
        InventoryModalsScript.ShowShard(_p.mReward.ItemRewarded.Poster, _p.mReward.Amount);
    } else {
      UIScreen.ScreenInfo info = new UIScreen.ScreenInfo();
      if(Entry != null && Entry.UpgradedWrestlerItem != null) {
        info.Add("wrestler", Entry.UpgradedWrestlerItem);
      } else {
        info.Add("poster", _p.Poster);
      }
      info.Add("showCurrent", true);
      UIManager.Instance.QueuePush("frontEnd_poster_detail", info);
    }
  }

  public void OnClickToken(TokenTileScript _t) {
    var tokens = PlayerInfoScript.Instance.SaveData.ResourcesData.Tokens;
    long tokenQuantity = 0;
    tokens.TryGetValue(_t.Token, out tokenQuantity);

    InventoryModalsScript.ShowToken(_t.Token, tokenQuantity);
  }

  public GameObject GetRevealTileObj() {
    return mTileObj;
  }

  /// <summary>
  /// New gacha tray behaviors : 1/20/2016 EO
  /// </summary>
  public int Slot;

  public void ResetContainer() {
    TileSpawnPoint.DestroyAllChildren();
  }

#region skin

  public void ShowSkinConvertedToCurrency() {
    if(Entry.ItemRewarded.Skin == null)
      return;

    SkinTileScript skinTile = mTileObj.GetComponent<SkinTileScript>();
    skinTile.ShowConvertedToCurrency();
  }

#endregion
}
