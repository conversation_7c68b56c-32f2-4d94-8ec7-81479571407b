using System.Collections.Generic;

public enum MissionType {
  Victory,
  SS_Levelup,
  Loot,
  UseItem,
  Enhance,
  Moves,
  Moves_Levelup,
  Posters,
  FactionJoin,
  FactionBeg,
  FactionHelp,
  FactionXP,
  Talent,
  Rarity,
  FactionFeudBrawlComplete,
  CollectLootCrate,
  CollectStars,
  ExpendItem,
  OpenItem,
  ObtainItem,
  PlayerXP,
  GB,
  Invasion,
  WinBossBattle,
  BossBattle,
  ParticipateScoutMission,
}

public class MissionRewardInfo {
  #region properties from server

  public string ID;
  public string RewardID;
  public int Progress;
  public bool CanClaim;
  public int TotalMission;
  public bool Claimed;

  public RewardEntry RewardEntry { get ; set; }

  public uint resetTime;

  public List<MissionInfo> Missions = new List<MissionInfo>();

  #endregion
}

public class MissionToastInfo {
  #region properties from server

  public string	MissionID;
  public string	Description;

  public int Progress;
  public int Target;

  public bool IsNew;
  public MissionType	Type;

  #endregion

  public MissionToastInfo (string missionID, int progress, int total, bool isNew) {
    MissionID = missionID;
    Progress = progress;
    Target = total;
    IsNew = isNew;
  }
}

public class MissionInfo {
  #region properties from server

  public string	ID;
  public MissionType	Type;
  public string Title;
  public string	Description;

  public string[] Reward;
  public int Progress;
  public int Target;

  public uint DumpTimer;
  public uint TimeLimit;
  public int SpeedupCost;

  public bool CanClaim;

  public int LvlRequire;
  public string MissionDeeplink;
  public bool Claimed;
  public bool Locked;

  public MissionData	Data;
  // For Analytics.

  #endregion

  public bool IsLimitedTime;

  private RewardEntry m_RewardEntry;

  public RewardEntry rewardEntry {
    get { return m_RewardEntry; }
  }

  static RewardEntry ParseReward(string reward) {
    string[] rewardArray = reward.Split(':');
    int cost;
    int.TryParse(rewardArray[1], out cost);
    //mis.CostCurrency = CurrencyDataManager.Instance.GetData(reward[0]);
    return new RewardEntry(rewardArray[0], cost);
  }


  public static MissionInfo GetMissionInfo(string _id, Dictionary<string, object> _progress) {
    MissionInfo	mis	= new MissionInfo();
    MissionData	d	= MissionDataDataManager.Instance.GetData(_id);

    if (d == null) {
      TFUtils.LogWarning("Invalid Mission ID received: [" + _id + "]");
      return null;
    }

    mis.ID = _id;
    mis.Data	= d;

    if (_progress.ContainsKey("progress_data") == true) {
      Dictionary<string, object> data = (Dictionary<string, object>)_progress["progress_data"];

      mis.Progress = TFUtils.LoadInt(data, "progress", 0);
      mis.Target = TFUtils.LoadInt(data, "total", 0);
      mis.CanClaim	= (mis.Progress >= mis.Target);
      mis.Title = d.Title;
      mis.Description = d.Description;
      mis.Reward = d.Reward;
      mis.m_RewardEntry	= ParseReward(d.Reward[0]);
      mis.MissionDeeplink = d.MissionDeepLink;
      mis.LvlRequire = d.LvlRequire;
      if (_progress.ContainsKey("claimed")) {
        mis.Claimed = (bool)_progress["claimed"];
      }
      if (_progress.ContainsKey("locked")) {
        mis.Locked = (bool)_progress["locked"];
      }
      return mis;
    }

    return null;
  }
}

public class MissionManager : Singleton<MissionManager> {
  MissionRewardInfo _currMissionInfo = null;

  #region events

  public event System.Action<MissionRewardInfo> OnRewardUpdate;

  #endregion

  private List<MissionToastInfo> toastInfoList = new List<MissionToastInfo>();


  public static string DAILY_MISSION_RESET_TIME_KEY = "DailyMissionResetTime";

  public MissionRewardInfo currentMissionInfo {
    get {
      return _currMissionInfo; 
    }
    set {
      _currMissionInfo = value;

      StackCountManager.Instance.Set(StackCountType.Stack_Missions, MissionManager.Instance.GetAvailableRewardCount());

      if (OnRewardUpdate != null)
        OnRewardUpdate(_currMissionInfo);
    }
  }

  public void AddMissionToastInfo(MissionToastInfo toastInfo) {
    toastInfoList.Add(toastInfo);
  }

  static RewardEntry ParseReward(string reward) {
    string[] rewardArray = reward.Split(':');
    int cost;
    int.TryParse(rewardArray[1], out cost);
    //mis.CostCurrency = CurrencyDataManager.Instance.GetData(reward[0]);
    return new RewardEntry(rewardArray[0], cost);
  }


  public void UpdateMissionProgressFromToast(string _id, int _progress, int _target) {
    if (this.currentMissionInfo != null) {
      foreach (MissionInfo mis in this.currentMissionInfo.Missions) {
        if (mis.ID == _id) {
          mis.Progress	= _progress;
          mis.Target = _target;
          if (mis.Progress >= mis.Target)
            mis.CanClaim = true;
        }
      }
    }

    StackCountManager.Instance.Set(StackCountType.Stack_Missions, MissionManager.Instance.GetAvailableRewardCount());
  }

  public void ProcessUpdateMissionInfo(Dictionary<string, object> progress_data) {
    if (progress_data != null) {
      object mission_progress = null;
      progress_data.TryGetValue("sorted_missions", out mission_progress);
      int mission_completed_count = TFUtils.LoadInt(progress_data, "mission_completed_count", -1);
      int daily_mission_unlock = TFUtils.LoadInt(progress_data, "daily_mission_unlock", -1);
      string mission_all_id = TFUtils.LoadString(progress_data, "mission_all_id", "DM_ALL");
      bool claimed = TFUtils.LoadBool(progress_data, "claimed", false);
      uint resetTime = TFUtils.LoadUint(progress_data, "reset_time", 0);
      MissionManager.Instance.ProcessUpdateMissionInfo((List<object>)mission_progress, mission_completed_count, claimed, resetTime, daily_mission_unlock, mission_all_id);
    }
  }

  public void ProcessUpdateMissionInfo(List<object> missions, int mission_completed_count = -1, bool claimed = false, uint resetTime = 0, int missionUnlock = -1, string mission_all_id = "DM_ALL") {
    MissionInfo	m = null;
    MissionRewardInfo info = new MissionRewardInfo();
    MissionData missionAll = MissionDataDataManager.Instance.GetData(mission_all_id);

    info.ID = mission_all_id;
    info.resetTime = resetTime;
    if (missionAll != null) {
      info.RewardEntry = ParseReward(missionAll.Reward[0]);
    }
    info.Progress = mission_completed_count;
    info.TotalMission = missionUnlock;
    info.CanClaim	= (info.Progress >= info.TotalMission);
    info.Claimed = claimed;
    info.Missions	= new List<MissionInfo>();
    // DEBUG server provide with no mission, use same old missions as before
    if (missions == null) {
      info.Missions = this.currentMissionInfo.Missions;
    } else {
      foreach (var mis in missions) {
        Dictionary<string, object> mission_progress = (Dictionary<string, object>)mis;
        Dictionary<string, object> p = null;


        foreach (var key in mission_progress.Keys) {
          try {
            p = (Dictionary<string, object>)mission_progress[key];
          } catch {
          }

          if (p != null) {
            m = MissionInfo.GetMissionInfo(key, p);
            if (m != null) {
              // if new this is new mission, send "load" event for mission.
              bool	fire_event = false;
              MissionInfo	m_pre = null;
              if (this.currentMissionInfo == null) {
                fire_event = true;
              } else if (this.currentMissionInfo.Missions != null) {
                m_pre = this.currentMissionInfo.Missions.Find(o => o.ID == m.ID);
                if (m_pre == null)
                  fire_event = true;
              }
              if (fire_event == true)
                WPAnalyticsManager.Instance.TrackMissionProgress(m, "load");

              // Add mission info to the list.
              info.Missions.Add(m);
            }
          }
        }
      }
    }

    this.currentMissionInfo = info;

    // Check FTUE Mission End conditional tutorial.
    if (TutorialController.HasInstance == true) {
      string t_block = "FTUEMissionEnd";
      if (TutorialController.Instance.IsBlockComplete(t_block) == false) {
        if (this.currentMissionInfo.Missions != null) {
          if (this.currentMissionInfo.Missions.Count > 0) {
            bool	done_ftue_mission = true;

            foreach (MissionInfo mis in this.currentMissionInfo.Missions) {
              MissionData	d = MissionDataDataManager.Instance.GetData(mis.ID);
              if (d == null) {
                done_ftue_mission = false;
              } else if (d.Category == "base") {
                done_ftue_mission = false;
              }
            }
            if (done_ftue_mission == true)
              MissionManager.Instance.StartFTUMissionEndTutorial();
          }
        }
      }
    }
  }

  public int GetAvailableRewardCount() {
    int count = 0;

    if (currentMissionInfo != null) {
      if (currentMissionInfo.CanClaim && !currentMissionInfo.Claimed)
        count++;
      foreach (MissionInfo info in currentMissionInfo.Missions) {
        if (info.CanClaim && !info.Claimed && !info.Locked)
          count++;
      }
    }

    return count;
  }

  public void GetMissionsNow(MessageCallback callback = null) {
    // r33 xuyen.nguyenthi added
    bool force_renew = NewbieModularFTUEController.Instance.IsModularFTUECompleted() ? false : true;
    TFServerOp op = Multiplayer.Multiplayer.GetMissions(null, force_renew, callback);
    ICMultiplayer.Instance.BlockTillMessage(ICMPMessageType.GET_MISSIONS, null, op);
    op.Execute();
  }

  public TFServerOp GetMissions(MessageCallback callback = null) {
    // r33 xuyen.nguyenthi added
    bool force_renew = NewbieModularFTUEController.Instance.IsModularFTUECompleted() ? false : true;
    return Multiplayer.Multiplayer.GetMissions(null, force_renew, callback);
  }

  public void DumpMission(string _missionID, MessageCallback callback = null) {
#if !KFF_RELEASE
    // Please remove when server work is done.
    string	_dump_mid	= _missionID;
#endif

    // if find the mission in current mission, send "discard" event for mission tracking.
    MissionInfo	m = this.currentMissionInfo.Missions.Find(o => o.ID == _missionID);
    if (m != null) {
      WPAnalyticsManager.Instance.TrackMissionProgress(m, "discard");
    }

    TFServerOp op = Multiplayer.Multiplayer.DumpMission(null, _missionID, callback);
    ICMultiplayer.Instance.BlockTillMessage(ICMPMessageType.DUMP_MISSION, null, op);
    op.Execute();
  }

  public void RushMission(string _missionID, MessageCallback callback = null) {
#if !KFF_RELEASE
    // Please remove when server work is done.
    string	_rush_mid	= _missionID;
#endif

    // Analytics set by server now
    TFServerOp op = Multiplayer.Multiplayer.RushMission(null, _missionID, callback);
    ICMultiplayer.Instance.BlockTillMessage(ICMPMessageType.RUSH_MISSION, null, op);
    op.Execute();
  }

  public void ClaimMission(string _missionID, MessageCallback callback = null) {
    // if find the mission in current mission, send "discard" event for mission tracking.
    MissionInfo	m = this.currentMissionInfo.Missions.Find(o => o.ID == _missionID);
    if (m != null) {
      WPAnalyticsManager.Instance.TrackMissionProgress(m, "complete");
    }

    TFServerOp op = Multiplayer.Multiplayer.ClaimMission(null, _missionID, callback);
    ICMultiplayer.Instance.BlockTillMessage(ICMPMessageType.CLAIM_MISSION, null, op);
    op.Execute();
  }

  public void ClaimMissionAll(string _missionID, MessageCallback callback = null) {
    TFServerOp op = Multiplayer.Multiplayer.ClaimMissionAll(null, _missionID, callback);
    ICMultiplayer.Instance.BlockTillMessage(ICMPMessageType.CLAIM_MISSION, null, op);
    op.Execute();
  }

  public void ClaimMissionGrabBag(MessageCallback callback = null) {
    /*
		// Analytics are set by server now
		Dictionary<string, object> context = new Dictionary<string, object>();
		context["context"] = "GrabBagReward";
		WPAnalyticsManager.Instance.SetGameTransactionParameters("rewards", "Mission", context);
		*/

    TFServerOp op = Multiplayer.Multiplayer.ClaimMissionGrabBag(null, callback);
    ICMultiplayer.Instance.BlockTillMessage(ICMPMessageType.CLAIM_MISSION_GRAB_BAG, null, op);
    op.Execute();
  }

  public bool StartFTUMissionTutorial() {
    // Check Mission Tutorial is complete or not.
    string t_block = "MissionStart";
    if (TutorialController.Instance.IsBlockComplete(t_block) == false) {
      TutorialController.Instance.StartTutorialBlock(t_block);
      return true;
    }

    return false;
  }

  public bool StartFTUMissionEndTutorial() {
    // Check Mission Tutorial is complete or not.
    string t_block = "FTUEMissionEnd";
    if (TutorialController.Instance.IsBlockComplete(t_block) == false) {
      TutorialController.Instance.StartTutorialBlock(t_block);
      return true;
    }

    return false;
  }

  public void CheckAndShowMisionToast() {
    if (toastInfoList.Count > 0) {
      for (int i = 0; i < toastInfoList.Count; i++) {
        MissionToastInfo info = toastInfoList[i];
        Dictionary<string, object> data = new Dictionary<string, object>();
        data.Add("missionID", info.MissionID);
        ToastController.ShowProgressToast(R26_ProgressToastType.DailyMission, data);
        toastInfoList.RemoveAt(i);
        i--;
      }
    }
  }

}
