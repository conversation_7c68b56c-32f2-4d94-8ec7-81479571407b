// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>

using global::System;
using global::System.Collections.Generic;
using global::FlatBuffers;

public struct SkinVisualFlatBuffer : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_1_12_0(); }
  public static SkinVisualFlatBuffer GetRootAsSkinVisualFlatBuffer(ByteBuffer _bb) { return GetRootAsSkinVisualFlatBuffer(_bb, new SkinVisualFlatBuffer()); }
  public static SkinVisualFlatBuffer GetRootAsSkinVisualFlatBuffer(ByteBuffer _bb, SkinVisualFlatBuffer obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public SkinVisualFlatBuffer __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public SkinVisualFlatBufferDataRaw? Items(int j) { int o = __p.__offset(4); return o != 0 ? (SkinVisualFlatBufferDataRaw?)(new SkinVisualFlatBufferDataRaw()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ItemsLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
  public SkinVisualFlatBufferDataRaw? ItemsByKey(string key) { int o = __p.__offset(4); return o != 0 ? SkinVisualFlatBufferDataRaw.__lookup_by_key(__p.__vector(o), key, __p.bb) : null; }

  public static Offset<SkinVisualFlatBuffer> CreateSkinVisualFlatBuffer(FlatBufferBuilder builder,
      VectorOffset itemsOffset = default(VectorOffset)) {
    builder.StartTable(1);
    SkinVisualFlatBuffer.AddItems(builder, itemsOffset);
    return SkinVisualFlatBuffer.EndSkinVisualFlatBuffer(builder);
  }

  public static void StartSkinVisualFlatBuffer(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddItems(FlatBufferBuilder builder, VectorOffset itemsOffset) { builder.AddOffset(0, itemsOffset.Value, 0); }
  public static VectorOffset CreateItemsVector(FlatBufferBuilder builder, Offset<SkinVisualFlatBufferDataRaw>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateItemsVectorBlock(FlatBufferBuilder builder, Offset<SkinVisualFlatBufferDataRaw>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static void StartItemsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<SkinVisualFlatBuffer> EndSkinVisualFlatBuffer(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<SkinVisualFlatBuffer>(o);
  }
  public static void FinishSkinVisualFlatBufferBuffer(FlatBufferBuilder builder, Offset<SkinVisualFlatBuffer> offset) { builder.Finish(offset.Value); }
  public static void FinishSizePrefixedSkinVisualFlatBufferBuffer(FlatBufferBuilder builder, Offset<SkinVisualFlatBuffer> offset) { builder.FinishSizePrefixed(offset.Value); }
};

public struct SkinVisualFlatBufferDataRaw : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_1_12_0(); }
  public static SkinVisualFlatBufferDataRaw GetRootAsSkinVisualFlatBufferDataRaw(ByteBuffer _bb) { return GetRootAsSkinVisualFlatBufferDataRaw(_bb, new SkinVisualFlatBufferDataRaw()); }
  public static SkinVisualFlatBufferDataRaw GetRootAsSkinVisualFlatBufferDataRaw(ByteBuffer _bb, SkinVisualFlatBufferDataRaw obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public SkinVisualFlatBufferDataRaw __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string ID { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetIDBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetIDBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetIDArray() { return __p.__vector_as_array<byte>(4); }
  public string A0 { get { int o = __p.__offset(6); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetA0Bytes() { return __p.__vector_as_span<byte>(6, 1); }
#else
  public ArraySegment<byte>? GetA0Bytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public byte[] GetA0Array() { return __p.__vector_as_array<byte>(6); }
  public string A10 { get { int o = __p.__offset(8); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetA10Bytes() { return __p.__vector_as_span<byte>(8, 1); }
#else
  public ArraySegment<byte>? GetA10Bytes() { return __p.__vector_as_arraysegment(8); }
#endif
  public byte[] GetA10Array() { return __p.__vector_as_array<byte>(8); }
  public string A3 { get { int o = __p.__offset(10); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetA3Bytes() { return __p.__vector_as_span<byte>(10, 1); }
#else
  public ArraySegment<byte>? GetA3Bytes() { return __p.__vector_as_arraysegment(10); }
#endif
  public byte[] GetA3Array() { return __p.__vector_as_array<byte>(10); }
  public string A4 { get { int o = __p.__offset(12); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetA4Bytes() { return __p.__vector_as_span<byte>(12, 1); }
#else
  public ArraySegment<byte>? GetA4Bytes() { return __p.__vector_as_arraysegment(12); }
#endif
  public byte[] GetA4Array() { return __p.__vector_as_array<byte>(12); }
  public string A5 { get { int o = __p.__offset(14); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetA5Bytes() { return __p.__vector_as_span<byte>(14, 1); }
#else
  public ArraySegment<byte>? GetA5Bytes() { return __p.__vector_as_arraysegment(14); }
#endif
  public byte[] GetA5Array() { return __p.__vector_as_array<byte>(14); }
  public string A6 { get { int o = __p.__offset(16); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetA6Bytes() { return __p.__vector_as_span<byte>(16, 1); }
#else
  public ArraySegment<byte>? GetA6Bytes() { return __p.__vector_as_arraysegment(16); }
#endif
  public byte[] GetA6Array() { return __p.__vector_as_array<byte>(16); }
  public string A7 { get { int o = __p.__offset(18); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetA7Bytes() { return __p.__vector_as_span<byte>(18, 1); }
#else
  public ArraySegment<byte>? GetA7Bytes() { return __p.__vector_as_arraysegment(18); }
#endif
  public byte[] GetA7Array() { return __p.__vector_as_array<byte>(18); }
  public string A8 { get { int o = __p.__offset(20); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetA8Bytes() { return __p.__vector_as_span<byte>(20, 1); }
#else
  public ArraySegment<byte>? GetA8Bytes() { return __p.__vector_as_arraysegment(20); }
#endif
  public byte[] GetA8Array() { return __p.__vector_as_array<byte>(20); }
  public string A9 { get { int o = __p.__offset(22); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetA9Bytes() { return __p.__vector_as_span<byte>(22, 1); }
#else
  public ArraySegment<byte>? GetA9Bytes() { return __p.__vector_as_arraysegment(22); }
#endif
  public byte[] GetA9Array() { return __p.__vector_as_array<byte>(22); }
  public string B0 { get { int o = __p.__offset(24); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetB0Bytes() { return __p.__vector_as_span<byte>(24, 1); }
#else
  public ArraySegment<byte>? GetB0Bytes() { return __p.__vector_as_arraysegment(24); }
#endif
  public byte[] GetB0Array() { return __p.__vector_as_array<byte>(24); }
  public string B1 { get { int o = __p.__offset(26); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetB1Bytes() { return __p.__vector_as_span<byte>(26, 1); }
#else
  public ArraySegment<byte>? GetB1Bytes() { return __p.__vector_as_arraysegment(26); }
#endif
  public byte[] GetB1Array() { return __p.__vector_as_array<byte>(26); }
  public string B2 { get { int o = __p.__offset(28); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetB2Bytes() { return __p.__vector_as_span<byte>(28, 1); }
#else
  public ArraySegment<byte>? GetB2Bytes() { return __p.__vector_as_arraysegment(28); }
#endif
  public byte[] GetB2Array() { return __p.__vector_as_array<byte>(28); }
  public string B3 { get { int o = __p.__offset(30); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetB3Bytes() { return __p.__vector_as_span<byte>(30, 1); }
#else
  public ArraySegment<byte>? GetB3Bytes() { return __p.__vector_as_arraysegment(30); }
#endif
  public byte[] GetB3Array() { return __p.__vector_as_array<byte>(30); }
  public string Gate { get { int o = __p.__offset(32); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetGateBytes() { return __p.__vector_as_span<byte>(32, 1); }
#else
  public ArraySegment<byte>? GetGateBytes() { return __p.__vector_as_arraysegment(32); }
#endif
  public byte[] GetGateArray() { return __p.__vector_as_array<byte>(32); }

  public static Offset<SkinVisualFlatBufferDataRaw> CreateSkinVisualFlatBufferDataRaw(FlatBufferBuilder builder,
      StringOffset IDOffset = default(StringOffset),
      StringOffset a0Offset = default(StringOffset),
      StringOffset a10Offset = default(StringOffset),
      StringOffset a3Offset = default(StringOffset),
      StringOffset a4Offset = default(StringOffset),
      StringOffset a5Offset = default(StringOffset),
      StringOffset a6Offset = default(StringOffset),
      StringOffset a7Offset = default(StringOffset),
      StringOffset a8Offset = default(StringOffset),
      StringOffset a9Offset = default(StringOffset),
      StringOffset b0Offset = default(StringOffset),
      StringOffset b1Offset = default(StringOffset),
      StringOffset b2Offset = default(StringOffset),
      StringOffset b3Offset = default(StringOffset),
      StringOffset gateOffset = default(StringOffset)) {
    builder.StartTable(15);
    SkinVisualFlatBufferDataRaw.AddGate(builder, gateOffset);
    SkinVisualFlatBufferDataRaw.AddB3(builder, b3Offset);
    SkinVisualFlatBufferDataRaw.AddB2(builder, b2Offset);
    SkinVisualFlatBufferDataRaw.AddB1(builder, b1Offset);
    SkinVisualFlatBufferDataRaw.AddB0(builder, b0Offset);
    SkinVisualFlatBufferDataRaw.AddA9(builder, a9Offset);
    SkinVisualFlatBufferDataRaw.AddA8(builder, a8Offset);
    SkinVisualFlatBufferDataRaw.AddA7(builder, a7Offset);
    SkinVisualFlatBufferDataRaw.AddA6(builder, a6Offset);
    SkinVisualFlatBufferDataRaw.AddA5(builder, a5Offset);
    SkinVisualFlatBufferDataRaw.AddA4(builder, a4Offset);
    SkinVisualFlatBufferDataRaw.AddA3(builder, a3Offset);
    SkinVisualFlatBufferDataRaw.AddA10(builder, a10Offset);
    SkinVisualFlatBufferDataRaw.AddA0(builder, a0Offset);
    SkinVisualFlatBufferDataRaw.AddID(builder, IDOffset);
    return SkinVisualFlatBufferDataRaw.EndSkinVisualFlatBufferDataRaw(builder);
  }

  public static void StartSkinVisualFlatBufferDataRaw(FlatBufferBuilder builder) { builder.StartTable(15); }
  public static void AddID(FlatBufferBuilder builder, StringOffset IDOffset) { builder.AddOffset(0, IDOffset.Value, 0); }
  public static void AddA0(FlatBufferBuilder builder, StringOffset a0Offset) { builder.AddOffset(1, a0Offset.Value, 0); }
  public static void AddA10(FlatBufferBuilder builder, StringOffset a10Offset) { builder.AddOffset(2, a10Offset.Value, 0); }
  public static void AddA3(FlatBufferBuilder builder, StringOffset a3Offset) { builder.AddOffset(3, a3Offset.Value, 0); }
  public static void AddA4(FlatBufferBuilder builder, StringOffset a4Offset) { builder.AddOffset(4, a4Offset.Value, 0); }
  public static void AddA5(FlatBufferBuilder builder, StringOffset a5Offset) { builder.AddOffset(5, a5Offset.Value, 0); }
  public static void AddA6(FlatBufferBuilder builder, StringOffset a6Offset) { builder.AddOffset(6, a6Offset.Value, 0); }
  public static void AddA7(FlatBufferBuilder builder, StringOffset a7Offset) { builder.AddOffset(7, a7Offset.Value, 0); }
  public static void AddA8(FlatBufferBuilder builder, StringOffset a8Offset) { builder.AddOffset(8, a8Offset.Value, 0); }
  public static void AddA9(FlatBufferBuilder builder, StringOffset a9Offset) { builder.AddOffset(9, a9Offset.Value, 0); }
  public static void AddB0(FlatBufferBuilder builder, StringOffset b0Offset) { builder.AddOffset(10, b0Offset.Value, 0); }
  public static void AddB1(FlatBufferBuilder builder, StringOffset b1Offset) { builder.AddOffset(11, b1Offset.Value, 0); }
  public static void AddB2(FlatBufferBuilder builder, StringOffset b2Offset) { builder.AddOffset(12, b2Offset.Value, 0); }
  public static void AddB3(FlatBufferBuilder builder, StringOffset b3Offset) { builder.AddOffset(13, b3Offset.Value, 0); }
  public static void AddGate(FlatBufferBuilder builder, StringOffset gateOffset) { builder.AddOffset(14, gateOffset.Value, 0); }
  public static Offset<SkinVisualFlatBufferDataRaw> EndSkinVisualFlatBufferDataRaw(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    builder.Required(o, 4);  // ID
    return new Offset<SkinVisualFlatBufferDataRaw>(o);
  }

  public static VectorOffset CreateSortedVectorOfSkinVisualFlatBufferDataRaw(FlatBufferBuilder builder, Offset<SkinVisualFlatBufferDataRaw>[] offsets) {
    Array.Sort(offsets, (Offset<SkinVisualFlatBufferDataRaw> o1, Offset<SkinVisualFlatBufferDataRaw> o2) => Table.CompareStrings(Table.__offset(4, o1.Value, builder.DataBuffer), Table.__offset(4, o2.Value, builder.DataBuffer), builder.DataBuffer));
    return builder.CreateVectorOfTables(offsets);
  }

  public static SkinVisualFlatBufferDataRaw? __lookup_by_key(int vectorLocation, string key, ByteBuffer bb) {
    byte[] byteKey = System.Text.Encoding.UTF8.GetBytes(key);
    int span = bb.GetInt(vectorLocation - 4);
    int start = 0;
    while (span != 0) {
      int middle = span / 2;
      int tableOffset = Table.__indirect(vectorLocation + 4 * (start + middle), bb);
      int comp = Table.CompareStrings(Table.__offset(4, bb.Length - tableOffset, bb), byteKey, bb);
      if (comp > 0) {
        span = middle;
      } else if (comp < 0) {
        middle++;
        start += middle;
        span -= middle;
      } else {
        return new SkinVisualFlatBufferDataRaw().__assign(tableOffset, bb);
      }
    }
    return null;
  }
};

