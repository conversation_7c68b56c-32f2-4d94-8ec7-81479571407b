using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class NullifyPowerIcon : MonoBehaviour {
  private const string VFXPowerVector = "R67/FX_PowerVector";
  
  [SerializeField] private bool isSmallVersion;
  [SerializeField] private Image Icon;
  [SerializeField] private KFFText txtDescription;
  private NullifyBuffType _nullifyBuffType;
  private bool _isNullify = false;
  private Dictionary<string, Sprite> cachedImageDict = new Dictionary<string, Sprite>();
  
  /// <summary>
  /// Populates the UI elements based on the provided Power vector type
  /// </summary>
  /// <param name="type">Type of power vector</param>
  /// <param name="isNullify">The power vector nullified or not</param>
  public void Populate(NullifyBuffType type, bool isNullify) {
    _nullifyBuffType = type;
    _isNullify = isNullify;
    if (isNullify) {
      KFFResourceManager.Instance.LoadOnDemandLooseTextureToImage(NullifyDataManager.GetIconNullifyPowerSmall(type), Icon);
    } else {
      //KFFResourceManager.Instance.LoadOnDemandLooseTextureToImage(NullifyDataManager.GetIconNullifyPowerBig(type),Icon);
      LoadODL(NullifyDataManager.GetIconNullifyPowerBig(type), Icon,true);
    }

    txtDescription.Text = NullifyDataManager.GetNullifyTypeDescription(type).ToUpper();
  }

  public void CreateVFX(bool smallsize) {
    var _vfx = VFXManager.Instance.PlayInCanvas(VFXPowerVector, transform, -1f, transform.position);
    if (_vfx != null) {
      _vfx.transform.SetSiblingIndex(0);
      _vfx.transform.localScale = smallsize ? new Vector3(0.7f, 0.7f, 0.7f) : new Vector3(1.2f, 1.2f, 1.2f);
    }
  }

  public void OnClickPowerIcon() {
    UIToolTipController.Config config = new UIToolTipController.Config();
    config.tailDirection = UIToolTipController.TailDirection.Down;
    string _mes = GetPowerVectorTooltip(_nullifyBuffType, _isNullify);
    UIToolTipController.Instance.Show(this.gameObject, config, _mes);
  }

  public static string GetPowerVectorTooltip(NullifyBuffType type, bool isNullify) {
    switch (type) {
      case NullifyBuffType.Entourage:
        return KFFLocalization.Get("!!R67_POWER_VECTOR_DES_ENTOURAGE_" + (isNullify ? "NULL" : "ACTIVE"));
      case NullifyBuffType.StrapMedal:
        return KFFLocalization.Get("!!R67_POWER_VECTOR_DES_STRAPMEDAL_" + (isNullify ? "NULL" : "ACTIVE"));
      case NullifyBuffType.SkillPlate:
        return KFFLocalization.Get("!!R67_POWER_VECTOR_DES_SKILLPLATE_" + (isNullify ? "NULL" : "ACTIVE"));
      case NullifyBuffType.Moment:
        return KFFLocalization.Get("!!R67_POWER_VECTOR_DES_MOMENT_" + (isNullify ? "NULL" : "ACTIVE"));
      case NullifyBuffType.Gear:
        return KFFLocalization.Get("!!R67_POWER_VECTOR_DES_GEAR_" + (isNullify ? "NULL" : "ACTIVE"));
      case NullifyBuffType.Perk:
        return KFFLocalization.Get("!!R67_POWER_VECTOR_DES_PERK_" + (isNullify ? "NULL" : "ACTIVE"));
      case NullifyBuffType.Prop:
        return KFFLocalization.Get("!!R67_POWER_VECTOR_DES_PROP_" + (isNullify ? "NULL" : "ACTIVE"));
      default:
        return string.Empty;
    }
  }
  
  #region CACHE_ODL
  /// <summary>
  /// Adds a sprite to the cache if it does not already exist.
  /// </summary>
  /// <param name="_url"></param>
  /// <param name="_sprite"></param>
  public void AddCacheSprite(string _url, Sprite _sprite) {
    if (cachedImageDict.ContainsKey(_url) == false) {
      cachedImageDict.Add(_url, _sprite);
    }
  }

  /// <summary>
  /// Retrieves a sprite from the cache based on the provided URL.
  /// </summary>
  /// <param name="_image"></param>
  /// <returns></returns>
  public Sprite GetCacheSprite(string _image) {
    if (cachedImageDict.ContainsKey(_image)) {
      return cachedImageDict[_image];
    }
    return null;
  }

  /// <summary>
  /// Loads an image from a URL into an Image component.
  /// If caching is enabled, it checks if the image is already cached and uses the cached version if available.
  /// Otherwise, it loads the image on demand and optionally caches it.
  /// </summary>
  /// <param name="_url"></param>
  /// <param name="_img"></param>
  /// <param name="_cacheImage"></param>
  public void LoadODL(string _url, Image _img, bool _cacheImage = false) {
    // Check if the Image component does not already have the desired sprite.
    if (_img.sprite == null || _img.sprite.name != _url) {
      if (_cacheImage) {
        // Attempt to retrieve the sprite from the cache.
        var sprite = GetCacheSprite(_url);
        if (sprite != null) {
          // Use the cached sprite if available.
          _img.sprite = sprite;
        } else {
          // Load the texture on demand and create a new sprite if not cached.
          KFFResourceManager.Instance.LoadOnDemandLooseTextureToImage(_url, _img, null, (str, texture, obj) => {
            if (texture != null) {
              Sprite _sprite = Sprite.Create((Texture2D)texture, new Rect(0f, 0f, texture.width, texture.height), new Vector2(0.5f, 0.5f), 100F, 0, SpriteMeshType.FullRect);
              // Add the newly created sprite to the cache.
              AddCacheSprite(_url, _sprite);
            }
          }, false, true, null);
        }
      } else {
        // Load the texture on demand without caching.
        KFFResourceManager.Instance.LoadOnDemandLooseTextureToImage(_url, _img);
      }
    }
  }
  #endregion
}