using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class UniqueRuleButtonController : MonoBehaviour {
  public static string PrefabName = "unique_rule_btn";

  [SerializeField] private Text highlightCountTxt;
  [SerializeField] private Transform vfxTransform;
  [SerializeField] private GameObject buttonParent;
  [HideInInspector] public NullifyItemData NullifyData;

  private string _statChangeId, _nullifyId;
  private bool _sameUniqueRule;
  private string vfxIdle = "R67/FX_UniqueRule_Button_Green";
  private GameObject vfxGameObject;
  private bool _uniqueRuleForBook = false;
  private bool _isPvPMode = false;
  public bool IsPvPMode => _isPvPMode;

  /// <summary>
  /// cache data, turn on or off button 
  /// </summary>
  /// <param name="statChangeId"></param>
  /// <param name="nullifyId"></param>
  public void Populate(string statChangeId, string nullifyId, bool isPvP = false) {
    _statChangeId = statChangeId;
    _nullifyId = nullifyId;
    NullifyData = null;
    _isPvPMode = isPvP;
    if (!string.IsNullOrEmpty(_nullifyId)) {
      NullifyData = NullifyDataManager.Instance.GetData(_nullifyId);
    }

    if ((NullifyData != null && NullifyData.HasNullifyActive(_isPvPMode)) || !string.IsNullOrEmpty(_statChangeId)) {
      buttonParent.gameObject.SetActive(true);
    }
    else {
      buttonParent.gameObject.SetActive(false);
    }
  }

  /// <summary>
  /// Populates the book data based on the provided book ID.
  /// </summary>
  /// <param name="bookId"></param>
  public void PopulateBook(string bookId) {
    // Set a flag to indicate that we're populating a book
    _uniqueRuleForBook = true;

    // Get the list of chapters for the given book ID
    var listChapter = ChapterDataManager.GetChapterListByBookID(bookId);

    // If the list of chapters is null or empty, deactivate the button parent and return
    if (listChapter.IsNullOrEmpty()) {
      buttonParent.gameObject.SetActive(false);
      return;
    }

    // Initialize the nullify ID and stat change ID to null
    _nullifyId = null;
    _statChangeId = null;
     int chapterActiveCount = 0;
    
    // Assume that all chapters have the same unique rule initially
    _sameUniqueRule = true;
    // Iterate through each chapter in the list
    foreach (var chapter in listChapter) {
      // Skip null chapters
      if (chapter == null || (!chapter.Active && !chapter.IsTicketedEventR46))
        continue;
      
      // Get the nullify data ID and stat changes ID for the current chapter
      string nullifyDataID = chapter.NullifyDataID;
      string statChangesID = chapter.StatChangesID;

      //number chapter active
      chapterActiveCount++;

      // If either the nullify data ID or stat changes ID is not empty
      if (!string.IsNullOrEmpty(nullifyDataID) || !string.IsNullOrEmpty(statChangesID)) {
        var _nullifyData = NullifyDataManager.Instance.GetData(nullifyDataID);
        
        // If the nullify ID and stat change ID are not set yet
        if (_nullifyId == null && _statChangeId == null) {
          // Set them to the values from the current chapter
          if (_nullifyData != null && _nullifyData.HasNullifyActive(_isPvPMode)) {
            _nullifyId = nullifyDataID;
          }

          _statChangeId = statChangesID;
        }
        // If the nullify ID or stat change ID is different from the current chapter's values
        else if (_nullifyId != nullifyDataID || _statChangeId != statChangesID) {
          // Set the flag to indicate that the chapters don't have the same unique rule
          _sameUniqueRule = false;
          // Break out of the loop since we've found a different rule
          break;
        }
      }
    }

    // check only one chapter active show popup UniqueRule
    if (chapterActiveCount == 1) {
      _sameUniqueRule = true;
    }

    // check TicketedEvent active
    CheckChapterTicketedEvent(bookId);

    // Populate the data using the nullify ID and stat change ID
    Populate(_statChangeId, _nullifyId);
  }
  
  /// <summary>
  /// checck ticketed event  active
  /// </summary>
  /// <param name="bookID"></param>
  private void CheckChapterTicketedEvent(string bookID) {
    Book_R46 bookData = new Book_R46();
    List<Book_R46> books = PVEUtils_R46.GetActiveBook();
    foreach (var book in books) {
      if (book.BookID == bookID) {
        bookData = book;
        break;
      }
    }
    //get data new chapter ticketed event
    var normalChapter = bookData.GetChapterByType(ChapterType.Normal);
    foreach (var chapterR46 in normalChapter) {
      if (chapterR46.Value.GetChapterState(bookData) == ChapterState.ACTIVE) {
        if (normalChapter.Count == 1) {
          ChapterData chapter = ChapterDataManager.Instance.GetData(chapterR46.Value.ChapterID);
          //update ID nullify and statschange data
          _nullifyId = chapter.NullifyDataID;
          _statChangeId = chapter.StatChangesID;
          _sameUniqueRule = true;
        }
      }
    }
  }

  /// <summary>
  /// instantiate button vfx
  /// </summary>
  private void Start() {
    if (vfxTransform != null && vfxGameObject == null) {
      vfxGameObject = VFXManager.Instance.PlayInCanvas(vfxIdle, vfxTransform, -1, vfxTransform.position, Quaternion.identity);
      vfxGameObject.transform.localPosition = Vector3.zero;
    }
  }

  public void OnClickStatsChangeButton() {
    if (_uniqueRuleForBook) {
      if (_sameUniqueRule) {
        if (QuickAccessBarController.OnOpenUniqueRulePopup != null) {
          QuickAccessBarController.OnOpenUniqueRulePopup.Invoke();
        }
        UIScreen.ScreenInfo info = new UIScreen.ScreenInfo();
        info.Add(UniqueRulePopupController.NullifyIDKey, _nullifyId);
        info.Add(UniqueRulePopupController.StatsChangeIDKey, _statChangeId);
        info.Add(UniqueRulePopupController.PvPKey, _isPvPMode);
        UIManager.Instance.QueuePush(UniqueRulePopupController.PrefabName, info);
      }
      else {
        var config = new UIToolTipController.Config();
        config.tailDirection = UIToolTipController.TailDirection.Auto;
        UIToolTipController.Instance.Show(buttonParent, config, KFFLocalization.Get("!!R67_UNIQUE_RULE_BUTTON_TOOLTIP"));
      }
    }
    else {
      if (QuickAccessBarController.OnOpenUniqueRulePopup != null) {
        QuickAccessBarController.OnOpenUniqueRulePopup.Invoke();
      }
      UIScreen.ScreenInfo info = new UIScreen.ScreenInfo();
      info.Add(UniqueRulePopupController.NullifyIDKey, _nullifyId);
      info.Add(UniqueRulePopupController.StatsChangeIDKey, _statChangeId);
      info.Add(UniqueRulePopupController.PvPKey, _isPvPMode);
      UIManager.Instance.QueuePush(UniqueRulePopupController.PrefabName, info);
    }
  }

  public string GetStatsChangeID() {
    return _statChangeId;
  }
}
