using System;
using System.Collections;
using UnityEngine;
using UnityEngine.Serialization;
using UnityEngine.UI;

public class StatsChangeTileScript : MonoBehaviour {
  [SerializeField]
  private Image buffIconImage;
  [SerializeField]
  private Image buffArrowImage;
  [SerializeField]
  private KFFText buffValueText;
  [SerializeField]
  private GameObject greyBg;
  [SerializeField]
  private KFFText buffNameText;
  [SerializeField]
  private Sprite bleedSideEffectIcon;
  [SerializeField]
  private Sprite leechSideEffectIcon;
  [SerializeField]
  private Sprite otherSideEffectIcon;
  [SerializeField]
  private Sprite StrikeSideEffectIcon;
  [SerializeField]
  private Sprite upArrowIcon;
  [SerializeField]
  private Sprite downArrowIcon;
  private StatsChangeTileData cachedTileData;

  public void Populate(StatsChangeTileData tileData, StatsChangeTabController statsChangeTabController) {
    cachedTileData = tileData;
  }
  
  private void OnEnable() {
    StartCoroutine(DoWaitAndLoadProperties());
  }

  IEnumerator DoWaitAndLoadProperties() {
    yield return new WaitForEndOfFrame();
    PopulateBuffTile();

    greyBg.SetActive(cachedTileData.tileIndex % 2 == 0);
  }

  private void PopulateBuffTile() {
    if (cachedTileData.gemColor != GemType.None) {
      buffIconImage.sprite = SharedSpritesHolder.Instance.GetIconGemSprite(cachedTileData.gemColor);
      buffNameText.Text = $"{cachedTileData.GetBuffName()} {KFFLocalization.Get("!!R67_GEM_DAMAGE")}";
    }
    if (cachedTileData.moveColor != GemType.None) {
      buffIconImage.sprite = SharedSpritesHolder.Instance.GetMoveCardFameSprite(cachedTileData.moveColor);
      buffNameText.Text = $"{cachedTileData.GetBuffName()} {KFFLocalization.Get("!!R67_MOVE_DAMAGE")}";
    }
    if (cachedTileData.gemModifier != GemModifier.None) {
      GemModifierData data = GemModifierDataManager.Instance.GetData(GemModifierEnumCheck.GetString(cachedTileData.gemModifier));
      if (data != null) {
        string spritePath = data.GetPlayerSpritePath();
        if (!string.IsNullOrEmpty(spritePath)) {
          buffIconImage.sprite = (Sprite)KFFResourceManager.Instance.LoadResource(spritePath, typeof(Sprite));
          buffNameText.Text = $"{cachedTileData.GetBuffName()} {KFFLocalization.Get("!!R67_GEM_DAMAGE")}";
        }
      }
      
      //special case: WHIP-41553 ,use 2xMultiplier sprite for multiplier gem modifier
      if (cachedTileData.gemModifier == GemModifier.Multiplier) {
        buffIconImage.sprite = (Sprite)KFFResourceManager.Instance.LoadResource("UI/Gems/Gem_Frame_2xMultiplier", typeof(Sprite));
      }
    }
    if (cachedTileData.sideEffect != StatChangesItemData.SideEffect.None) {
      buffIconImage.sprite = SelectSideEffectIcon(cachedTileData.sideEffect);
      buffNameText.Text = $"{cachedTileData.GetBuffName()} {KFFLocalization.Get("!!R67_EFFECT")}";
    }

    if (cachedTileData.buffValue > 0) {
      buffValueText.gameObject.SetActive(true);
      buffValueText.Text = $"+{cachedTileData.buffValue}%";
      buffValueText.color = Color.green;
    } else if (cachedTileData.buffValue < 0) {
      buffValueText.gameObject.SetActive(true);
      buffValueText.Text = $"{cachedTileData.buffValue}%";
      buffValueText.color = Color.red;
    } else {
      buffValueText.gameObject.SetActive(false);
    }
    buffArrowImage.gameObject.SetActive(true);
    if (cachedTileData.buffValue == 0) {
      buffArrowImage.gameObject.SetActive(false);
    } else if (cachedTileData.buffValue > 0) {
      buffArrowImage.sprite = upArrowIcon;
    } else {
      buffArrowImage.sprite = downArrowIcon;
    }
  }

  /// <summary>
  /// select icon to set to side effect icon
  /// </summary>
  /// <param name="sideEffect"></param>
  /// <returns></returns>
  private Sprite SelectSideEffectIcon(StatChangesItemData.SideEffect sideEffect) {
    switch (sideEffect) {
      case StatChangesItemData.SideEffect.Bleed:
        return bleedSideEffectIcon;
      case StatChangesItemData.SideEffect.Leech:
        return leechSideEffectIcon;
      case StatChangesItemData.SideEffect.Other:
        return otherSideEffectIcon;
      case StatChangesItemData.SideEffect.Strike:
        return StrikeSideEffectIcon;
      default:
        break;
    }
    return otherSideEffectIcon;
  }
}
