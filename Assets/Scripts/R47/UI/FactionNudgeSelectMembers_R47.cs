using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class FactionNudgeSelectMembers_R47 : UIScreen {
  public const string PREFAB_NAME = "frontEnd_faction_nudge_select_r47";
  public const string TITLE_KEY = "NudgeTitle";
  public const string BODY_KEY = "NudgeBody";
  public const string FACTION_DATA_KEY = "FactionData";
  public const string NUDGE_POPUP_KEY = "NudgeController";
  public const string UNAVAILABLE_KEY = "UNAVAILABLE";
  public const string AVAILABLE_KEY = "AVAILABLE";
  public const string CLEAR_KEY = "CLEAR_DATA";
  
  [SerializeField] private FactionRosterController rosterController;
  [SerializeField] private Text sendTxt;
  [SerializeField] private Text unavailableTxt;
  
  private string title;
  private string body;
  private List<string> userIDs = new List<string>();
  private FactionNudgePopupController_R47 nudgePopupController;
  private AllianceData allianceData;

  public override void Setup() { }

  public override IEnumerator PushRoutine(ScreenInfo info) {
    if (info != null) {
      title = info.TryGet<string>(TITLE_KEY);
      body = info.TryGet<string>(BODY_KEY);
      nudgePopupController = info.TryGet<FactionNudgePopupController_R47>(NUDGE_POPUP_KEY);
      allianceData = info.TryGet<AllianceData>(FACTION_DATA_KEY);
    }

    Populate();
    return base.PushRoutine(info);
  }

  public override IEnumerator PopRoutine() {
    rosterController.OnSelectedMember -= OnSelectedCallback;
    return base.PopRoutine();
  }

  void Populate() {
    UpdateSendText();
    if (allianceData != null) {
      rosterController.OnSelectedMember -= OnSelectedCallback;
      rosterController.OnSelectedMember += OnSelectedCallback;
      rosterController.refreshRosterList(allianceData);
    }
  }

  void OnSelectedCallback(string id) {
    if (id == UNAVAILABLE_KEY || id == AVAILABLE_KEY) {
      unavailableTxt.text = id == UNAVAILABLE_KEY ? UNAVAILABLE_KEY : "";
      return;
    }
    if (userIDs.Contains(id)) {
      userIDs.Remove(id);
    } else {
      userIDs.Add(id);
    }
    rosterController.SetNudgeID(id);
    UpdateSendText();
  }

  void UpdateSendText() {
    if (userIDs.Count > 0) {
      sendTxt.text = string.Format(KFFLocalization.Get("!!R47_SEND_MESS"), userIDs.Count);
    } else {
      sendTxt.text = KFFLocalization.Get("!!CHAT_SEND");
    }
  }
  
  //Button Events
  public void OnTapSend() {
    if (userIDs.Count > 0) {
      string messageTitle = title;
      string messageBody = body;
      if (ProfanityFilterDataManager.Instance != null) {
        messageTitle = ProfanityFilterDataManager.Instance.ReplaceProfanity(messageTitle);
        messageBody = ProfanityFilterDataManager.Instance.ReplaceProfanity(messageBody);
      }
      TFServerOp op = Multiplayer.Multiplayer.SendFactionNudge(userIDs.ToArray(), messageTitle, messageBody, NudgeType.mass_nudge_cd);
      ICMultiplayerEvent.ContextualNudgeCallback -= SendMessagesCallback;
      ICMultiplayerEvent.ContextualNudgeCallback += SendMessagesCallback;
      ICMultiplayer.Instance.BlockTillMessage(ICMPMessageType.SEND_NUDGE, null, op);
      op.Execute();
    } else {
      ScreenInfo info = new ScreenInfo();
      info.Add(FactionNudgeWarningPopupController_R47.BODY_KEY, "!!R47_FACTION_WARNING_BODY_3");
      UIManager.Instance.QueuePush(FactionNudgeWarningPopupController_R47.PREFAB_NAME, info);
    }
  }

  void SendMessagesCallback(bool isSuccess, Dictionary<string, uint> _DataRes) {
    //Hanle for case when player send nugde to more than one member
    //only one member successed and the rest member failed, server will response 2 code (0 & -47107)
    if (isSuccess) {
      string des = string.Format(KFFLocalization.Get("!!R47_FACTION_NUDGE_TOAST"), KFFLocalization.Get("!!CHAT_ALLIANCE"));
      ToastController.ShowInformationalToast(des);
      if (_DataRes.Count == userIDs.Count) {
        ICMultiplayerEvent.ContextualNudgeCallback -= SendMessagesCallback;
        CloseScreen();
        if (nudgePopupController != null) {
          nudgePopupController.shouldClose = true;
        }
      }
    } else {
      ICMultiplayerEvent.ContextualNudgeCallback -= SendMessagesCallback;
      allianceData = AllianceManager.Instance.PlayerAlliance;
      userIDs.Clear();
      rosterController.SetNudgeID(CLEAR_KEY);
      Populate();
    }
  }
}