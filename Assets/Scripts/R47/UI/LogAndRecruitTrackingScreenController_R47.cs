using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class LogAndRecruitTrackingScreenController_R47 : UIScreen {
  
#region Declare Variables
  public static string PREFAB_NAME = "frontEnd_faction_log_recruit_r47";
  
  [SerializeField] private GameObject recruitRoot;
  [SerializeField] private GameObject logRoot;
  [SerializeField] private UITabManager tabManager;
  
#endregion

#region UIScreen Base Methods
  public override void Setup() { }

  public override IEnumerator PushRoutine(ScreenInfo info) {
    ActiveScreen();
    DisplayTabContent(true);
    return base.PushRoutine(info);
  }

  public override IEnumerator PopRoutine() {
    DeactivateScreen();
    return base.PopRoutine();
  }

#endregion

  void DisplayTabContent(bool isLog) {
    logRoot.SetActive(isLog);
    recruitRoot.SetActive(!isLog);
    if (isLog) {
      PlayerInfoScript.Instance.SaveData.PlayerData.ResetAllianceActivityLogStackCounter();
    } else { 
      PlayerInfoScript.Instance.SaveData.PlayerData.ResetRecruitmentLogStackCounter();
    }
  }

#region Log and Recruit Screen
  void ActiveScreen() {
    DeactivateScreen();
    StackCountManager.Instance.SubscribeToChanged(StackCountType.Stack_ActivityLog, OnActivitiesLogStackCountChanged);
    StackCountManager.Instance.SubscribeToChanged(StackCountType.Stack_RecruitmentLog, OnActivitiesRecruitStackCountChanged);
  }

  void DeactivateScreen() {
    StackCountManager.Instance.UnsubscribeFromChanged(StackCountType.Stack_ActivityLog, OnActivitiesLogStackCountChanged);
    StackCountManager.Instance.UnsubscribeFromChanged(StackCountType.Stack_RecruitmentLog, OnActivitiesRecruitStackCountChanged);
  }
  
  private void OnActivitiesRecruitStackCountChanged(int count) {
    tabManager.SetStackCount(1, count);
  }

  private void OnActivitiesLogStackCountChanged(int count) {
    tabManager.SetStackCount(0, count);
  }
#endregion

#region Button Click Events
  public void OnTapLogTab() {
    DisplayTabContent(true);
  }

  public void OnTapRecruitmentTab() {
    DisplayTabContent(false);
  }
#endregion
}