using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class EventBase_R46 : MonoBehaviour {
  #region VFX

  private AssetBundle assetBundle { get; set; }
  private bool loading = false;
  private Transform parent;
  private GameObject effectObject;

  private string vfxName = "";
  
  private Color colorStart_0 = Color.white;
  private Color colorStart_1 = Color.white;
  private Color colorStart_2 = Color.white;

  private AssetBundleType bundleType;
  
  protected void PopulateEffect(string vfxData, Transform _parent) {
    if (string.IsNullOrEmpty(vfxData)) {
      return;
    }

    if (KFFLODManager.IsLowEndDevice()) {
      return;
    }

    var vfxDatas = vfxData.Split(',');

    if (vfxDatas.Length != 4) {
      Debug.LogWarning("VFX Color format error");
      return;
    }

    vfxName = vfxDatas[0];
    
    ParseColor(ref colorStart_0, vfxDatas[1]);
    ParseColor(ref colorStart_1, vfxDatas[2]);
    ParseColor(ref colorStart_2, vfxDatas[3]);
    
    parent = _parent;
    
    bool useODBPath = false;
#if USE_ODB
    if (KFFResourceManager.Instance.IsUsingAssetBundles())
      useODBPath = true;
#endif
    bundleType = (useODBPath) ? AssetBundleType.OnDemandBundle : AssetBundleType.None;
    
    #if GEAR_LOCAL_PREFAB
    GameObject effect = KFFResourceManager.Instance.LoadResource(GetPrefabName()) as GameObject;
    if (effect != null) {
      GameObject prefab = Instantiate(effect, parent) as GameObject;
    }
    #else
    StartCoroutine(LoadRuntimeEffectAsssetBundle());
    #endif
  }

  private void ParseColor(ref Color color, string data) {
    if (string.IsNullOrEmpty(data)) { 
      return;
    }

    if (!ColorUtility.TryParseHtmlString("#"+data, out color)) {
      Debug.LogError("VFX Color format error: "+data);
    } 
  }

  private IEnumerator LoadRuntimeEffectAsssetBundle() {
    if (assetBundle == null) {
      yield return StartCoroutine(LoadEffectAssetBundleCoroutine());
    }

    yield return null;

    if (assetBundle != null) {
      GameObject effect = KFFResourceManager.Instance.LoadResource(assetBundle,GetPrefabName(), bundleType) as GameObject;
      if (effect != null) {
        effectObject = Instantiate(effect, parent) as GameObject;

        if (effectObject != null) {
          var particles = effectObject.GetComponentsInChildren<ParticleSystem>();
          if (particles.Length == 3) {
            particles[0].startColor = colorStart_0;
            particles[1].startColor = colorStart_1;
            particles[2].startColor = colorStart_2;
          }
        }
      }
    }
  }

  public IEnumerator LoadEffectAssetBundleCoroutine(System.Action<AssetBundle,object> callback = null, object param = null) {
    if (assetBundle == null && !loading) {
      string assetBundleName = GetAssetBundleName();
      AssetBundle ab = KFFAssetBundleManager.Instance.GetAssetBundleByName(assetBundleName);
      if (ab != null) {
        assetBundle = ab;
      } else {
        while (loading)
          yield return null;

        loading = true;
        LoadAssetBundleCallbackParam param2 = new LoadAssetBundleCallbackParam();
    
        yield return KFFResourceManager.Instance.StartCoroutine(KFFResourceManager.Instance.LoadAssetBundleCoroutine(false, assetBundleName, LoadAssetBundleCallback, (object)param2, showProgressBar: false, bundleType: bundleType, retryOnError: false));
        assetBundle = param2.assetBundle;
        loading = false;
      }
    }
    if (callback != null)
      callback(assetBundle, param);
  }

  private string GetAssetBundleName() {
    return WPAssetBundleDownloader.GetVFXAssetBundleName(vfxName, KFFLODManager.IsLowEndDevice());
  }

  private string GetPrefabName() {
    return "VFXs/" + vfxName + "/" + vfxName;
  }
  
  class LoadAssetBundleCallbackParam {
    public AssetBundle assetBundle;
  }
  
  void LoadAssetBundleCallback(AssetBundle assetBundle, object param) {
    LoadAssetBundleCallbackParam p = param as LoadAssetBundleCallbackParam;
    if (p != null) {
      p.assetBundle = assetBundle;
    }
  }

  #endregion
}
