using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using UnityEngine;
using UnityEngine.UI;
using EnhancedUI.EnhancedScroller;

public partial class RosterInventoryController {
  public const string EnforcerSlotsKey = "EnforcerSlotsKey";
  public const string EnforcerChapterKey = "EnforcerChapterKey";
  public const string EnforcerDidJoinChapterKey = "EnforcerDidJoinChapterKey";
  public const string EnforcerDidGoFromMapCylinder = "EnforcerDidGoFromMapCylinder";
  private List<EnforcerSlot> slotList = new List<EnforcerSlot>();
  // private List<WrestlerItem> enforcerWrestlerList = new List<WrestlerItem>();

  [Header("Enforcer Control")] [SerializeField]
  private GameObject enforcerHeader;

  [SerializeField] private GameObject bottomEnforcer;

  [SerializeField] private LayoutElement enforcerSortingLayoutGroup;
  [SerializeField] private List<GameObject> enforcerDisableList;

  [SerializeField] private EnforcerEquipController_R46 enforcerController;
  [SerializeField] private KFFText ownedEnforcerText;
  [SerializeField] private AudioClip selectEnforcerSound;
  [SerializeField] private AudioClip removeEnforcerClickSound;

  [Header("Roster filter by stipulation")] 
  public RosterStipulationFilterManager_R461 rosterStipulationFilter_R461;

  [Header("Roster filter by boss attributes")]
  public RosterBossAttributeFilter_R53 rosterBossAttributeFilter_R53;
  public FBScoutMissionProgressbarScript_R53 scoutMissionProgressbarScript_R53;

  //R67 Mechanic filter for stats change feature
  [Header("Roster filter by stats change")] 
  public RosterStatsChangeFilterManager rosterStatsChangeFilter;
  public StatChangesItemData CachedStatsChangeData = null;
  
  [Header("Roster filter by stats change")] 
  public UIRestrictionRules restrictionRulesFilterUI;
  public RosterRestrictionRulesFilterController restrictionRulesFilterController;
  public List<RestrictionRuleData> RestrictionRuleCacheList = new List<RestrictionRuleData>();
  
  [Header("Road boss filter")] 
  public RoadBossDynamicFilterUI roadBossDynamicFilterUI;
  
  private void OnClickSelectEnforcerCallback(WrestlerTileScript _tile) {
    var wrestlerItem = _tile.Wrestler;

    if (!SubscriptionVerification(wrestlerItem.Form)) {
      return;
    }

    KFFSoundPlayer.Instance.PlayOneShot(selectEnforcerSound);

    _tile.factionSelectTween.PlayWithCallback(() => {
      OnSelectEnforcerComplete(wrestlerItem);
      _tile.factionSelectTween.ResetToBeginning();
    });
  }

  private void OnSelectEnforcerComplete(WrestlerItem wrestlerItem) {
    wrestlerItem.isJustSelected = true;
    enforcerController.EquipEnforcer(wrestlerItem);
    UpdateEnforcerScroll();
  }

  public void OnclickRemoveEnforcer() {
    KFFSoundPlayer.Instance.PlayOneShot(removeEnforcerClickSound);
    enforcerController.RemoveEnforcer();
    UpdateEnforcerScroll();
  }

  public void InitialEnforcerRoster() {
    mSorts.Clear();
    SortEntry se = null;
    switch (mSortType) {
      case SortType.Stardom:
        se = new SortEntry(SortTypeEnum.Rarity);
        break;
      case SortType.Class:
        se = new SortEntry(SortTypeEnum.GemFocus);
        break;
      case SortType.Era:
        se = new SortEntry(SortTypeEnum.Era);
        break;
      case SortType.Level:
        se = new SortEntry(SortTypeEnum.Level);
        break;
      case SortType.Talent:
        se = new SortEntry(SortTypeEnum.Talent);
        break;
      case SortType.Name:
        se = new SortEntry(SortTypeEnum.Name);
        break;
    }

    if (se != null) {
      se.Reversed = mReverseSort;
      mSorts.Add(se);
      mSortTypeText.text = se.GetName();
    } else {
      se = new SortEntry(SortTypeEnum.Talent);
      se.Reversed = mReverseSort;
      mSorts.Add(se);
    }
    mMatGrayscale = new Material(mMatSource);
    mMatGrayscale.SetFloat("_Saturation", .3f);
    UpdateEnforcerScroll();
  }

  public void UpdateEnforcerScroll() {
    for (int i = 0; i < enforcerDisableList.Count; i++) {
      if (enforcerDisableList[i]) {
        enforcerDisableList[i].gameObject.SetActive(false);
      }
    }

    if (enforcerHeader && bottomEnforcer) {
      enforcerHeader.SetActive(true);
      bottomEnforcer.SetActive(true);
    }

    mItemList = new List<MultiViewScrollerData>();
    List<WrestlerItem> ownedList = new List<WrestlerItem>();
    for (int i = 0; i < enforcerController.selectedSlot.basicFilteredWrestlers.Count; i++) {
      WrestlerItem item = enforcerController.selectedSlot.basicFilteredWrestlers[i];
      if (!ShouldWrestlerItemAddToScroll(item)) {
        continue;
      }
      item.ClearSortingCache();
      ownedList.Add(item);
    }

    ownedEnforcerText.text = string.Format(KFFLocalization.Get("!!R46_OWNED"), ownedList.Count);
    var mCompare = new WrestlerComparer(mSorts);
    ownedList.Sort(mCompare);
    AddRowTile(ownedList, PopulateEnforcerWrestlerTile);

    //get in loot wrestler filtered by basic requirement
    mGetInLootWrestlerList = new List<WrestlerItem>();
    for (int i = 0; i < enforcerController.selectedSlot.inLootWrestlers.Count; i++) {
      WrestlerItem item = enforcerController.selectedSlot.inLootWrestlers[i];
      if (!ShouldWrestlerItemAddToScroll(item)) {
        continue;
      }
      mGetInLootWrestlerList.Add(item);
    }

    if (mGetInLootWrestlerList.Count > 0) {
      AddRowTile(mGetInLootWrestlerList, PopulateLootJumpWrestlerTileR15);
    }

    mMultiScroller.SetData(mItemList.ToArray());
    mMultiScroller.Reset();
  }
  
  private bool ShouldWrestlerItemAddToScroll(WrestlerItem wrestlerItem) {
    if (mSelectGender != GenderType.Universal) {
      if (wrestlerItem.Form.SSGroup.Gender != mSelectGender) {
        return false;
      }
    }

    if (enforcerController.IsUsedEnforcer(wrestlerItem)) {
      return false;
    }
    return true;
  }

  private void PopulateEnforcerWrestlerTile(WrestlerTileScript _tile, WrestlerItem _w) {
    WrestlerTileScript tile = PopulateWrestlerTile(_tile, _w, WrestlerTileMode.RosterInventory);
    if (tile != null) {
      tile.OnClickCallback = GetOnClickCallBack();
    }
  }

  //Filtering roster by scoring rule
  private void PopulateRosterFilterR461(bool isShow) {
    rosterStipulationFilter_R461.gameObject.SetActive(isShow);
    if (isShow) {
      chapterR46.UpdateActiveBattleScoreData();
      rosterStipulationFilter_R461.Populate(chapterR46.GetActiveBattleScoreDataList(), this); 
    }
  }
  /// <summary>
  /// Method to show or hide the stats change filter and populate it with data if needed
  /// </summary>
  /// <param name="statsChangeData"></param>
  private void PopulateStatsChangeFilter(StatChangesItemData statsChangeData) {
    //in PVP, remove stat changes filter to use restriction rules filer, comment code to use in future
    if (PlayerInfoScript.Instance.StateData.IsPVP && restrictionRulesFilterUI.gameObject.activeInHierarchy) {
      rosterStatsChangeFilter.gameObject.SetActive(false);
      return;
    }
    // Set the active state of the stats change filter based on the parameter
    if (statsChangeData == null) {
      rosterStatsChangeFilter.gameObject.SetActive(false);
      return;
    } else {
      rosterStatsChangeFilter.gameObject.SetActive(true);
    }

    CachedStatsChangeData = statsChangeData;
    // Populate the roster stats change filter with the retrieved data
    rosterStatsChangeFilter.Populate(this, statsChangeData);
  }

  /// <summary>
  /// Method to show or hide the restriction rules filter and populate it with data if needed
  /// </summary>
  private void PopulateRestrictionRulesFilter(MenuMode menuMode) {
    // Populate the roster restricton rules filter with the retrieved data
    bool shouldShowRestrictionRulesFilter = false;
    if (restrictionRulesFilterUI != null) {
      if (PVPHubController.HasInstance && PVPHubController.Instance.CurrentTournament.RestrictionRuleList != null) {
        RestrictionRuleCacheList = PVPHubController.Instance.CurrentTournament.RestrictionRuleList;
        if (RestrictionRuleCacheList != null && RestrictionRuleCacheList.Count > 0) {
          shouldShowRestrictionRulesFilter = true;
        } else {
          shouldShowRestrictionRulesFilter = false;
        }
      }
      if (shouldShowRestrictionRulesFilter && PlayerInfoScript.Instance.StateData.IsPVP && PVPHubController.HasInstance) {
        restrictionRulesFilterUI.gameObject.SetActive(true);
        restrictionRulesFilterUI.Init(PVPHubController.Instance.GetCurrentLeagueIndex() + 1, Color.magenta, menuMode, true);
      } else {
        restrictionRulesFilterUI.gameObject.SetActive(false);
      }
    }
  }

  //Check if scoring rule of R46 pve active
  private bool IsRosterFilterR46Available() {
    // Check if the chapter has any scoring rules defined
    if (chapterR46?.ScoringRules == null || chapterR46.ScoringRules.Count == 0) {
      return false;
    }
    List<BattleScoreData> activeBattleScoreDataList = chapterR46.GetActiveBattleScoreDataList();
    // Iterate through each scoring rule ID in the chapter's scoring rules
    foreach (var scoreData in activeBattleScoreDataList) {
      // Check if the score data is not null and meets the requirement for roster filtering
      if (scoreData != null && scoreData.IsMeetRequirementForRosterFilter) {
        return true; // Return true if at least one rule meets the requirements
      }
    }
    // Return false if no scoring rules meet the requirements
    return false;
  }

  private bool IsRosterFilterR46Active => IsRosterFilterR46Available() && rosterStipulationFilter_R461.ListBattleScore.Count > 0;
  public void SortRefreshRoster() {
    SortRefresh_Hide.PlayWithCallback(PanelHideTweenCallback);
  }
}

public enum PointMethodCategory {
  None,
  [Description("!!R461_FILTER_CATEGORY_MAKE_GEM_MOVE")]
  MakeGemMove,
  [Description("!!R461_FILTER_CATEGORY_DESTROY_GEM_MOVE")]
  DestroyGemMove,
  [Description("!!R461_FILTER_CATEGORY_COLOR_MOVE")]
  ColorMove,
  [Description("!!R461_FILTER_CATEGORY_MAKE_GEM_MODIFIER")]
  MakeGemModifier,
  [Description("!!R461_FILTER_CATEGORY_DESTROY_GEM_MODIFIER")]
  DestroyGemModifier,
  [Description("!!R461_FILTER_CATEGORY_EFFECT_MODIFIER")]
  EffectModifier
}

public static partial class EnumExtensions {
  public static string DescriptionString(this PointMethodCategory val) {
    DescriptionAttribute[] attributes = (DescriptionAttribute[]) val
      .GetType()
      .GetField(val.ToString())
      .GetCustomAttributes(typeof(DescriptionAttribute), false);
    return attributes.Length > 0 ? attributes[0].Description : string.Empty;
  }
}
