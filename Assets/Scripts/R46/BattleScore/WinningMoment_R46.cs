using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class WinningMoment_R46 : UIScreen {

  [SerializeField] public KFFText rankLabel;
  [SerializeField] public KFFText scoreLabel;
  [SerializeField] public Image imageBanner;
  [SerializeField] public KFFText chapterNameLabel;

  private Chapter_R46 chapterDataR46;

  public static string DISABLE_COMPLETE_BOSS_POPUP_KEY = "Complete_Boss_Popup_Key";

  public const string GENERIC_GRANDTOUR_WIN_BANNER_ODL_PATH = "UI/R505/Generic_Summry_Banner";
  public override void Setup() {
    chapterDataR46 = PlayerInfoScript.Instance.SaveData.PVEManager_R46.currentChapter;
    ChapterData chapterData = ChapterDataManager.Instance.GetData(chapterDataR46.ChapterID);
    chapterNameLabel.text = KFFLocalization.Get(chapterData.Name);
    if (chapterDataR46.book != null && chapterDataR46.book.IsWebGrandTour) {
      KFFResourceManager.Instance.LoadOnDemandLooseTextureToImage(GENERIC_GRANDTOUR_WIN_BANNER_ODL_PATH, imageBanner, loadTextureCallback: OnImageLoaded);
    } else {
      KFFResourceManager.Instance.LoadOnDemandLooseTextureToImage(chapterData.BookTexture, imageBanner, loadTextureCallback: OnImageLoaded);
    }
    // ICMultiplayerEvent.onGetLeaderboardR46Success += PopulateRank;
    rankLabel.gameObject.SetActive(false);
    scoreLabel.gameObject.SetActive(false);
  }

  protected void OnImageLoaded(string code, Texture texture, object data) {
    imageBanner.preserveAspect = true;
  }


  public void OnDestroy() {
    // ICMultiplayerEvent.onGetLeaderboardR46Success -= PopulateRank;
  }
  
  public override IEnumerator PushRoutine(ScreenInfo info) {
    if (!string.IsNullOrEmpty(chapterDataR46.LeaderboardID)) {
      PopulateRank();
    }
    yield return base.PushRoutine(info);
  }

  public void CloseThisPopup() {
    this.closeScreenCallback = CloseCallback;
    base.CloseScreen();
  }

  public void CloseCallback() {
    if (PlayerInfoScript.Instance.StateData.CurrentActiveQuest != null && PlayerInfoScript.Instance.StateData.CurrentActiveQuest.QuestBoss) {
      if (!PlayerPrefs.HasKey(DISABLE_COMPLETE_BOSS_POPUP_KEY)) {
        SimplePopupController.Instance.ShowWarning(KFFLocalization.Get("!!R46_COMPLETE_BOSS_TITLE"), KFFLocalization.Get("!!R46_COMPLETE_BOSS_DESC"), null, null, ToggleDisableCompleteBossPopup, null, null, true);
      }
    }
  }

  private static void ToggleDisableCompleteBossPopup(bool isToggledOn) {
    if (isToggledOn) {
      PlayerPrefs.SetInt(DISABLE_COMPLETE_BOSS_POPUP_KEY, 1);
    }
  }

  private void PopulateRank() {
    if (chapterDataR46 != null) {
      Dictionary<string, object>  leaderboardData = chapterDataR46.LeaderboardData;
      if (leaderboardData != null) {
        Dictionary<string, object> data = TFUtils.TryLoadDict(leaderboardData, "current_player_data");
        if (data != null) {
          rankLabel.gameObject.SetActive(true);
          scoreLabel.gameObject.SetActive(true);
          rankLabel.text = TFUtils.LoadInt(data, "rank", -1).ToString();
          scoreLabel.text = string.Format(KFFLocalization.Get("!!R46_WINNING_SCORE_LABEL"), TFUtils.LoadInt(data, "score", -1).ToString());
        }
      }
    }
  }
}