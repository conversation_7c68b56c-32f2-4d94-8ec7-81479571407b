using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class HomeScreenEventHub
{
  protected static HomeScreenEventHub _instance;
  protected static HomeScreenEventHub Instance {
    get {
      if (_instance == null) {
        _instance = new HomeScreenEventHub();
      }
      return _instance;
    }
    private set {
      _instance = value;
    }
  }

  #region Topic Name
  public static string SHOW_PROFILE_PANEL = "HIDE_PROFILE_PANEL";
  public static string SHOW_HOME_SCREEN = "SHOW_HOME_SCREEN";
  public static string REVEAL_DAILY_MISSION = "REVEAL_DAILY_MISSION";
  public static string REVEAL_DAILY_MISSION_FINISH = "REVEAL_DAILY_MISSION_FINISH";
  public static string SHOW_NORMAL_OFFER_HOME = "SHOW_NORMAL_OFFER_HOME";
  public static string CHECK_OFFER = "CHECK_OFFER";
  #endregion

  public Action<object> EventCallback;
  protected Dictionary<string, List<Action<object>>> EventMap = new Dictionary<string, List<Action<object>>>();

  protected HomeScreenEventHub() {
    
  }

  /// <summary>
  /// Follow a topic
  /// </summary>
  /// <param name="topicName"></param>
  /// <param name="callback"></param>
  public static void FollowTopic(string topicName, Action<object> callback) {
    if(!Instance.CheckAndAddNewEventCallbackList(topicName)) {
      return;
    }

    Instance.EventMap[topicName].Add(callback);
  }

  /// <summary>
  /// Unfollow a topic
  /// </summary>
  /// <param name="topicName"></param>
  /// <param name="callback"></param>
  public static void UnFollowTopic(string topicName, Action<object> callback) {
    if(!Instance.CheckAndAddNewEventCallbackList(topicName)) {
      return;
    }

    Instance.EventMap[topicName].Remove(callback);
  }

  /// <summary>
  /// Emit an event into a topic
  /// </summary>
  /// <param name="topicName"></param>
  /// <param name="data"></param>
  public static void EmitEventInTopic(string topicName, object data) {
    if(!Instance.CheckAndAddNewEventCallbackList(topicName)) {
      return;
    }
    
    List<Action<object>> callbacks = Instance.EventMap[topicName];
    int callbackCount = callbacks.Count;
    for (int i = 0; i < callbackCount; i++) {
      try {
        if (callbacks[i].Target == null)
          continue;
        callbacks[i](data);
      }
      catch (System.Exception ex) {
        Debug.LogError(ex);
      }
    }
  }

  protected bool CheckAndAddNewEventCallbackList(string topicName) {
    if (string.IsNullOrEmpty(topicName))
      return false;

    if (!EventMap.ContainsKey(topicName)) {
      EventMap.Add(topicName, new List<Action<object>>());
    }
    return true;
  }
}
