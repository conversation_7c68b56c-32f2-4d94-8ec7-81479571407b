using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class DownloadBlueprintVariantsState : StartupState {
  
  private bool _stateDone;
  private bool _shouldReloadBlueprint;
  
  public override StartupStateType StateType() {
    return StartupStateType.DownloadBlueprintVariants;
  }
  protected override IEnumerator StartInternal() {
    #if UNITY_WEBGL
    yield break;
    #endif
    CheckBlueprintVariants();
    while (!_stateDone) {
      yield return null;
    }
  }
  
  private void CheckBlueprintVariants() {
    var lastVariantID = PlayerPrefs.GetString(GearLoadingUtils.BLUEPRINT_LAST_VARIANTS, string.Empty);
    var variantId = GearSessionManager.Instance.SCSUserAttributes.GetBlueprintVariantId();
    if (!string.IsNullOrEmpty(variantId)) {
      if (!lastVariantID.Equals(variantId)) {
        GearLoadingUtils.BlueprintsVariantLookup.Clear();
        PlayerPrefs.DeleteKey(GearLoadingUtils.BLUEPRINT_VARIANTS_LOOKUP);
      }
      PlayerPrefs.SetString(GearLoadingUtils.BLUEPRINT_LAST_VARIANTS, variantId);

      SessionManager.CheckBlueprintVariants(variantId).Then(async responseHelper => {
        TFUtils.MyLog(new object[]{
        "CheckBlueprintVariants callback", responseHelper.HttpStatusCode, Time.realtimeSinceStartup
      }, go: null, color: "magenta");
        if (responseHelper.HttpStatusCode == System.Net.HttpStatusCode.NotModified) {
          _stateDone = true;
        } else {
          DownloadBlueprintVariants();
        }
      }).Catch((ex) => {
        //skip the variants
        _stateDone = true;
        GearLoadingUtils.LogException(ex);
      });
    } else {
      PlayerPrefs.DeleteKey(GearLoadingUtils.BLUEPRINT_LAST_VARIANTS);
      PlayerPrefs.DeleteKey(GearLoadingUtils.BLUEPRINT_VARIANTS_LOOKUP);
      GearLoadingUtils.BlueprintsVariantLookup.Clear();
      _stateDone = true;
    }
  }

  private void DownloadBlueprintVariants() {
    SessionManager.DownloadBlueprintVariants(GameDataType.Blueprints).Then(responseHelper => {
      _shouldReloadBlueprint = true;
      _stateDone = true;
    }).Catch((ex) => {
      //skip the variants
      _stateDone = true;
      GearLoadingUtils.LogException(ex);
    });
  }

  public bool ShouldReloadBlueprints() {
    return _shouldReloadBlueprint;
  }
  
  public override StartupState ResetState() {
    _stateDone = false;
    _shouldReloadBlueprint = false;
    return base.ResetState();
  }
}
