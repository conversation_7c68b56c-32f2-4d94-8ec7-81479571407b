using System;
using System.Collections;
using System.Collections.Generic;
using AdvancedInputFieldPlugin;
using UnityEngine;
using UnityEngine.UI;

public class NewsModalFeatureTabData : MultiViewScrollerData {
  private NewsModalData _newsModalData;

  public override string GetPrefabName() {
    return "frontEnd_featureTab_news_modal_row_item";
  }

  public void SetData(NewsModalData newsModalData) {
    this._newsModalData = newsModalData;
  }

  public NewsModalData GetData() {
    return _newsModalData;
  }
}

public class NewsModalFeatureTabDataScript : MonoBehaviour {
  public KFFText smallDescText;
  public Image imageBanner;
  public GameObject badgeIcon;
  public NewsModalFeatureTabData cell = null;
  public MiniTimer timer;
  public GameObject endTimeGroup;
  public KFFText title;

  [SerializeField]
  private RectTransform _contentTextRectTransform;
  [SerializeField]
  private RectTransform _titleRectTransform;
  private bool _hasUpdatedTextContent = false;
  private bool _hasUpdatedTextTitle = false;
  private int titleCharacterCount = 0;
  private int tabId = 0;
  private double timeToClickButton = 0;

  private void Awake() {
    _contentTextRectTransform = smallDescText.GetComponent<RectTransform>();
    _titleRectTransform = title.GetComponent<RectTransform>();
  }
  
  public void Populate(NewsModalFeatureTabData _cell, int _tabId) {
    _hasUpdatedTextContent = false;
    _hasUpdatedTextTitle = false;
    cell = _cell;
    NewsModalData data = _cell.GetData();
    tabId = _tabId;

    if (data != null) {
      SetTitle(KFFLocalization.Get(data.Title));
      SetDescription(KFFLocalization.Get(data.Description));
      KFFResourceManager.Instance.LoadOnDemandLooseTextureToImage(data.ImageUrl, imageBanner);
      if (endTimeGroup != null) {
        if (MiscParams.Instance.ShouldShowTimerNewsModal && timer != null && !data.IsLeagueTriggeredRBE) {
          endTimeGroup.SetActive(true);
          timer.StartCountDown((int)(data.EndTime.UnixTimestamp()), (int)TFUtils.ServerTimeUtc.UnixTimestamp());
        } else {
          endTimeGroup.SetActive(false);
        }
      }

      //Display post badge by post ID
      if (badgeIcon != null && BadgingSystemManager_R48.HasInstance) {
        bool haveBadge = BadgingSystemManager_R48.Instance.ShouldShowBadgeOnNewsModalPost(data.ID);
        badgeIcon.SetActive(haveBadge);

        // Mark as screen this
        if (haveBadge) {
          BadgingSystemManager_R48.Instance.SetBadgeState(BadgingSystemManager_R48.BadgeGroup.NewsModal_Post, BadgingSystemManager_R48.NEWS_MODAL_POST + data.ID, false);
          BadgingSystemManager_R48.Instance.UpdateBadgeDataFeatureTab(tabId);
        }
      }
    }
  }
  
  void SetDescription(string name) {
    smallDescText.text = name;
    smallDescText.UpdateImmediately();
  }

  private void Update() {
    if (!_hasUpdatedTextContent) {
      float height = _contentTextRectTransform.rect.height;
      if (smallDescText.preferredHeight <= height) {
        _hasUpdatedTextContent = true;
      } else {
        int characterCountVisible = smallDescText.cachedTextGenerator.characterCountVisible;
        if (characterCountVisible > 0) {
          // remove the last 3 character to add 3 dot character
          smallDescText.text = $"{smallDescText.text.Substring(0, characterCountVisible - 3)}...";
        }
      }
    }

    if (!_hasUpdatedTextTitle) {
      float height = _titleRectTransform.rect.height;
      if (title.preferredHeight <= height) {
        _hasUpdatedTextTitle = true;
      } else {
        if (titleCharacterCount > 0) {
          title.text = $"{title.text.Substring(0, titleCharacterCount - 1)}...";
          titleCharacterCount -= 1;
        }
      }
    }
  }

  private void SetTitle(string titleString) {
    if (string.IsNullOrEmpty(titleString) || title == null) {
      return;
    }
    title.text = titleString.ToUpper();
    title.UpdateImmediately();
    titleCharacterCount = title.cachedTextGenerator.characterCountVisible;
  }
  
  public void OnClickDetailPost() {
    if (Time.realtimeSinceStartup < timeToClickButton) {
      return;
    }
    
    timeToClickButton = Time.realtimeSinceStartup + 0.7f;
    UIScreen.ScreenInfo info = new UIScreen.ScreenInfo();
    info.Add(NewsModalDetailController.NewsModalID, cell.GetData().ID);
    UIManager.Instance.QueuePush(NewsModalDetailController.PrefabName, info, true);
  }
}
